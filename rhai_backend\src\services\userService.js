const { executeQuery } = require('../config/database');
const bcrypt = require('bcryptjs');

class UserService {
  // Get all users with pagination and filters (both admin and client users)
  static async getAllUsers(page = 1, limit = 10, filters = {}) {
    try {
      const offset = (page - 1) * limit;
      let whereConditions = [];
      let queryParams = [];

      // Build WHERE conditions
      if (filters.role) {
        if (filters.role === 'admin') {
          whereConditions.push("'admin' IN (SELECT role FROM admin_employee_accounts WHERE id = u.id)");
        } else if (filters.role === 'client') {
          whereConditions.push("u.user_type = 'client'");
        }
      }

      if (filters.search) {
        whereConditions.push(`(
          u.username LIKE ? OR
          u.first_name LIKE ? OR
          u.last_name LIKE ? OR
          u.email LIKE ?
        )`);
        const searchTerm = `%${filters.search}%`;
        queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
      }

      if (filters.is_active !== undefined) {
        whereConditions.push("u.status = ?");
        queryParams.push(filters.is_active ? 'active' : 'inactive');
      }

      const whereClause = whereConditions.length > 0 ?
        `WHERE ${whereConditions.join(' AND ')}` : '';

      // Combined query for both admin and client users
      const query = `
        SELECT
          u.id,
          u.username,
          u.first_name,
          u.last_name,
          u.email,
          u.status,
          u.user_type as type,
          u.created_at,
          u.last_login
        FROM (
          -- Admin users
          SELECT
            aea.id,
            aea.username,
            aep.first_name,
            aep.last_name,
            aep.email,
            aea.status,
            'admin' as user_type,
            aea.created_at,
            aea.last_login
          FROM admin_employee_accounts aea
          LEFT JOIN admin_employee_profiles aep ON aea.id = aep.account_id
          WHERE aea.role = 'admin'

          UNION ALL

          -- Client users
          SELECT
            ca.id,
            ca.username,
            cp.first_name,
            cp.last_name,
            cp.email,
            ca.status,
            'client' as user_type,
            ca.created_at,
            ca.last_login
          FROM client_accounts ca
          LEFT JOIN client_profiles cp ON ca.id = cp.account_id
        ) u
        ${whereClause}
        ORDER BY u.created_at DESC
        LIMIT ? OFFSET ?
      `;

      queryParams.push(limit, offset);

      const users = await executeQuery(query, queryParams);

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM (
          SELECT aea.id FROM admin_employee_accounts aea
          LEFT JOIN admin_employee_profiles aep ON aea.id = aep.account_id
          WHERE aea.role = 'admin'

          UNION ALL

          SELECT ca.id FROM client_accounts ca
          LEFT JOIN client_profiles cp ON ca.id = cp.account_id
        ) u
        ${whereClause}
      `;

      const countParams = queryParams.slice(0, -2); // Remove limit and offset
      const [{ total }] = await executeQuery(countQuery, countParams);

      return {
        success: true,
        data: {
          users: users.map(user => ({
            ...user,
            full_name: `${user.first_name || ''} ${user.last_name || ''}`.trim(),
            email: user.email || 'N/A'
          })),
          pagination: {
            total: parseInt(total),
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(total / limit)
          }
        }
      };
    } catch (error) {
      throw error;
    }
  }

  // Get user by ID (check both admin and client tables)
  static async getUserById(userId) {
    try {
      // Try to find in admin accounts first
      let query = `
        SELECT
          aea.id,
          aea.username,
          aep.first_name,
          aep.last_name,
          aep.email,
          aea.status,
          'admin' as type,
          aea.created_at,
          aea.last_login,
          aep.phone_number,
          aep.position,
          aep.department
        FROM admin_employee_accounts aea
        LEFT JOIN admin_employee_profiles aep ON aea.id = aep.account_id
        WHERE aea.id = ? AND aea.role = 'admin'
      `;

      let user = await executeQuery(query, [userId]);

      if (user.length === 0) {
        // Try to find in client accounts
        query = `
          SELECT
            ca.id,
            ca.username,
            cp.first_name,
            cp.last_name,
            cp.email,
            ca.status,
            'client' as type,
            ca.created_at,
            ca.last_login,
            cp.phone_number,
            cp.barangay,
            cp.city_municipality,
            cp.province
          FROM client_accounts ca
          LEFT JOIN client_profiles cp ON ca.id = cp.account_id
          WHERE ca.id = ?
        `;

        user = await executeQuery(query, [userId]);
      }

      if (user.length === 0) {
        throw new Error('User not found');
      }

      const userData = user[0];
      return {
        success: true,
        data: {
          ...userData,
          full_name: `${userData.first_name || ''} ${userData.last_name || ''}`.trim(),
          email: userData.email || 'N/A'
        }
      };
    } catch (error) {
      throw error;
    }
  }

  // Create new user (admin or client)
  static async createUser(userData) {
    try {
      const { username, email, password, first_name, last_name, role, phone_number } = userData;

      // Hash password
      const passwordHash = await bcrypt.hash(password, 12);

      let userId;

      if (role === 'admin') {
        // Create admin account
        const adminQuery = `
          INSERT INTO admin_employee_accounts (username, password_hash, role, status, created_at)
          VALUES (?, ?, 'admin', 'active', NOW())
        `;
        const adminResult = await executeQuery(adminQuery, [username, passwordHash]);
        userId = adminResult.insertId;

        // Create admin profile
        const profileQuery = `
          INSERT INTO admin_employee_profiles (account_id, first_name, last_name, email, phone_number, created_at)
          VALUES (?, ?, ?, ?, ?, NOW())
        `;
        await executeQuery(profileQuery, [userId, first_name, last_name, email, phone_number]);
      } else {
        // Create client account
        const clientQuery = `
          INSERT INTO client_accounts (username, password_hash, status, email_verified, phone_verified, created_at)
          VALUES (?, ?, 'active', 1, 1, NOW())
        `;
        const clientResult = await executeQuery(clientQuery, [username, passwordHash]);
        userId = clientResult.insertId;

        // Create client profile
        const profileQuery = `
          INSERT INTO client_profiles (account_id, first_name, last_name, email, phone_number, birth_date, gender, civil_status_id, barangay, city_municipality, province, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, '1990-01-01', 'male', 1, 'Default', 'Default', 'Default', NOW(), NOW())
        `;
        await executeQuery(profileQuery, [userId, first_name, last_name, email, phone_number]);
      }

      // Get the created user
      const createdUser = await this.getUserById(userId);

      return {
        success: true,
        data: createdUser.data,
        message: 'User created successfully'
      };
    } catch (error) {
      if (error.code === 'ER_DUP_ENTRY') {
        throw new Error('Username or email already exists');
      }
      throw error;
    }
  }

  // Update user
  static async updateUser(userId, updateData) {
    try {
      // First, get the user to determine type
      const userResult = await this.getUserById(userId);
      if (!userResult.success) {
        throw new Error('User not found');
      }

      const user = userResult.data;
      const { username, email, first_name, last_name, status, phone_number } = updateData;

      if (user.type === 'admin') {
        // Update admin account
        if (username || status) {
          const adminQuery = `
            UPDATE admin_employee_accounts
            SET ${username ? 'username = ?, ' : ''}${status ? 'status = ?, ' : ''}updated_at = NOW()
            WHERE id = ?
          `;
          const params = [];
          if (username) params.push(username);
          if (status) params.push(status);
          params.push(userId);

          await executeQuery(adminQuery, params);
        }

        // Update admin profile
        if (first_name || last_name || email || phone_number) {
          const profileQuery = `
            UPDATE admin_employee_profiles
            SET ${first_name ? 'first_name = ?, ' : ''}${last_name ? 'last_name = ?, ' : ''}${email ? 'email = ?, ' : ''}${phone_number ? 'phone_number = ?, ' : ''}updated_at = NOW()
            WHERE account_id = ?
          `;
          const params = [];
          if (first_name) params.push(first_name);
          if (last_name) params.push(last_name);
          if (email) params.push(email);
          if (phone_number) params.push(phone_number);
          params.push(userId);

          await executeQuery(profileQuery, params);
        }
      } else {
        // Update client account
        if (username || status) {
          const clientQuery = `
            UPDATE client_accounts
            SET ${username ? 'username = ?, ' : ''}${status ? 'status = ?, ' : ''}updated_at = NOW()
            WHERE id = ?
          `;
          const params = [];
          if (username) params.push(username);
          if (status) params.push(status);
          params.push(userId);

          await executeQuery(clientQuery, params);
        }

        // Update client profile
        if (first_name || last_name || email || phone_number) {
          const profileQuery = `
            UPDATE client_profiles
            SET ${first_name ? 'first_name = ?, ' : ''}${last_name ? 'last_name = ?, ' : ''}${email ? 'email = ?, ' : ''}${phone_number ? 'phone_number = ?, ' : ''}updated_at = NOW()
            WHERE account_id = ?
          `;
          const params = [];
          if (first_name) params.push(first_name);
          if (last_name) params.push(last_name);
          if (email) params.push(email);
          if (phone_number) params.push(phone_number);
          params.push(userId);

          await executeQuery(profileQuery, params);
        }
      }

      // Get the updated user
      const updatedUser = await this.getUserById(userId);

      return {
        success: true,
        data: updatedUser.data,
        message: 'User updated successfully'
      };
    } catch (error) {
      if (error.code === 'ER_DUP_ENTRY') {
        throw new Error('Username or email already exists');
      }
      throw error;
    }
  }

  // Delete user (soft delete by setting status to inactive)
  static async deleteUser(userId) {
    try {
      // First, get the user to determine type
      const userResult = await this.getUserById(userId);
      if (!userResult.success) {
        throw new Error('User not found');
      }

      const user = userResult.data;

      let deleteQuery;
      if (user.type === 'admin') {
        // For admin users, set status to inactive (soft delete)
        deleteQuery = `
          UPDATE admin_employee_accounts
          SET status = 'inactive', updated_at = NOW()
          WHERE id = ?
        `;
      } else {
        // For client users, set status to inactive (soft delete)
        deleteQuery = `
          UPDATE client_accounts
          SET status = 'inactive', updated_at = NOW()
          WHERE id = ?
        `;
      }

      await executeQuery(deleteQuery, [userId]);

      return {
        success: true,
        message: 'User deleted successfully'
      };
    } catch (error) {
      throw error;
    }
  }

  // Activate/Deactivate user (works for both admin and client users)
  static async toggleUserStatus(userId) {
    try {
      // First, get the user to determine type and current status
      const userResult = await this.getUserById(userId);
      if (!userResult.success) {
        throw new Error('User not found');
      }

      const user = userResult.data;
      const newStatus = user.status === 'active' ? 'suspended' : 'active';

      let updateQuery;
      if (user.type === 'admin') {
        updateQuery = `
          UPDATE admin_employee_accounts
          SET status = ?, updated_at = NOW()
          WHERE id = ?
        `;
      } else {
        updateQuery = `
          UPDATE client_accounts
          SET status = ?, updated_at = NOW()
          WHERE id = ?
        `;
      }

      await executeQuery(updateQuery, [newStatus, userId]);

      return {
        success: true,
        data: { ...user, status: newStatus },
        message: `User ${newStatus === 'active' ? 'activated' : 'suspended'} successfully`
      };
    } catch (error) {
      throw error;
    }
  }

  // Get users by role
  static async getUsersByRole(role, page = 1, limit = 10) {
    try {
      const filters = { role };
      const result = await this.getAllUsers(page, limit, filters);
      return result;
    } catch (error) {
      throw error;
    }
  }

  // Search users (updated implementation)
  static async searchUsers(searchTerm, limit = 10) {
    try {
      const searchQuery = `
        SELECT
          u.id,
          u.username,
          u.first_name,
          u.last_name,
          u.email,
          u.status,
          u.user_type as type,
          u.created_at
        FROM (
          SELECT
            aea.id,
            aea.username,
            aep.first_name,
            aep.last_name,
            aep.email,
            aea.status,
            'admin' as user_type,
            aea.created_at
          FROM admin_employee_accounts aea
          LEFT JOIN admin_employee_profiles aep ON aea.id = aep.account_id
          WHERE aea.role = 'admin'

          UNION ALL

          SELECT
            ca.id,
            ca.username,
            cp.first_name,
            cp.last_name,
            cp.email,
            ca.status,
            'client' as user_type,
            ca.created_at
          FROM client_accounts ca
          LEFT JOIN client_profiles cp ON ca.id = cp.account_id
        ) u
        WHERE (
          u.username LIKE ? OR
          u.first_name LIKE ? OR
          u.last_name LIKE ? OR
          u.email LIKE ?
        )
        ORDER BY u.created_at DESC
        LIMIT ?
      `;

      const searchTerm_wildcard = `%${searchTerm}%`;
      const users = await executeQuery(searchQuery, [
        searchTerm_wildcard, searchTerm_wildcard, searchTerm_wildcard, searchTerm_wildcard, limit
      ]);

      return {
        success: true,
        data: users.map(user => ({
          ...user,
          full_name: `${user.first_name || ''} ${user.last_name || ''}`.trim(),
          email: user.email || 'N/A'
        }))
      };
    } catch (error) {
      throw error;
    }
  }

  // Get user statistics
  static async getUserStats() {
    try {
      const statsQuery = `
        SELECT
          COUNT(*) as total,
          SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
          SUM(CASE WHEN status = 'pending' OR status = 'pending_verification' THEN 1 ELSE 0 END) as pending,
          SUM(CASE WHEN user_type = 'admin' THEN 1 ELSE 0 END) as admins,
          SUM(CASE WHEN user_type = 'client' THEN 1 ELSE 0 END) as clients
        FROM (
          SELECT status, 'admin' as user_type
          FROM admin_employee_accounts
          WHERE role = 'admin'

          UNION ALL

          SELECT status, 'client' as user_type
          FROM client_accounts
        ) u
      `;

      const [stats] = await executeQuery(statsQuery);

      return {
        success: true,
        data: {
          total: parseInt(stats.total) || 0,
          active: parseInt(stats.active) || 0,
          pending: parseInt(stats.pending) || 0,
          admins: parseInt(stats.admins) || 0,
          clients: parseInt(stats.clients) || 0,
          inactive: parseInt(stats.total) - parseInt(stats.active) || 0
        }
      };
    } catch (error) {
      throw error;
    }
  }

  // Search users
  static async searchUsers(searchTerm, limit = 10) {
    try {
      const searchQuery = `
        SELECT
          u.id,
          u.username,
          u.first_name,
          u.last_name,
          u.email,
          u.status,
          u.user_type as type,
          u.created_at
        FROM (
          SELECT
            aea.id,
            aea.username,
            aep.first_name,
            aep.last_name,
            aep.email,
            aea.status,
            'admin' as user_type,
            aea.created_at
          FROM admin_employee_accounts aea
          LEFT JOIN admin_employee_profiles aep ON aea.id = aep.account_id
          WHERE aea.role = 'admin'

          UNION ALL

          SELECT
            ca.id,
            ca.username,
            cp.first_name,
            cp.last_name,
            cp.email,
            ca.status,
            'client' as user_type,
            ca.created_at
          FROM client_accounts ca
          LEFT JOIN client_profiles cp ON ca.id = cp.account_id
        ) u
        WHERE (
          u.username LIKE ? OR
          u.first_name LIKE ? OR
          u.last_name LIKE ? OR
          u.email LIKE ?
        )
        ORDER BY u.created_at DESC
        LIMIT ?
      `;

      const searchTerm_wildcard = `%${searchTerm}%`;
      const users = await executeQuery(searchQuery, [
        searchTerm_wildcard, searchTerm_wildcard, searchTerm_wildcard, searchTerm_wildcard, limit
      ]);

      return {
        success: true,
        data: users.map(user => ({
          ...user,
          full_name: `${user.first_name || ''} ${user.last_name || ''}`.trim(),
          email: user.email || 'N/A'
        }))
      };
    } catch (error) {
      throw error;
    }
  }
}

module.exports = UserService;
