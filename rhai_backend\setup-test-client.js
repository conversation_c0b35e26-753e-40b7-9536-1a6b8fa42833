const bcrypt = require('bcryptjs');
const { executeQuery } = require('./src/config/database');

async function createTestClient() {
  console.log('👤 Creating test client for API testing...');
  
  try {
    const username = 'testapi';
    const password = 'testpass123';
    
    // Check if client already exists
    const existingQuery = `SELECT id FROM client_accounts WHERE username = ?`;
    const existing = await executeQuery(existingQuery, [username]);
    
    if (existing.length > 0) {
      console.log(`✅ Test client '${username}' already exists with ID: ${existing[0].id}`);
      
      // Update password to known value
      const passwordHash = await bcrypt.hash(password, 12);
      const updateQuery = `UPDATE client_accounts SET password_hash = ?, status = 'active' WHERE username = ?`;
      await executeQuery(updateQuery, [passwordHash, username]);
      console.log(`✅ Updated password for test client`);
      
      return { username, password, id: existing[0].id };
    }
    
    // Create new test client
    const passwordHash = await bcrypt.hash(password, 12);
    
    const insertQuery = `
      INSERT INTO client_accounts (username, password_hash, status, email_verified, phone_verified, password_changed_at)
      VALUES (?, ?, 'active', 1, 1, NOW())
    `;
    
    const result = await executeQuery(insertQuery, [username, passwordHash]);
    const clientId = result.insertId;
    
    console.log(`✅ Created test client '${username}' with ID: ${clientId}`);
    
    // Create a test document request for this client
    const requestQuery = `
      INSERT INTO document_requests (
        request_number, client_id, document_type_id, purpose_category_id, 
        purpose_details, status_id, base_fee, requested_at
      ) VALUES (
        CONCAT('TEST-', DATE_FORMAT(NOW(), '%Y-%m%d-%H%i%s')), 
        ?, 1, 1, 'Test document request for API testing', 1, 50.00, NOW()
      )
    `;
    
    const requestResult = await executeQuery(requestQuery, [clientId]);
    console.log(`✅ Created test document request with ID: ${requestResult.insertId}`);
    
    return { username, password, id: clientId };
    
  } catch (error) {
    console.error('❌ Error creating test client:', error);
    return null;
  }
}

createTestClient().then(result => {
  if (result) {
    console.log('\n🎉 Test client ready!');
    console.log(`Username: ${result.username}`);
    console.log(`Password: ${result.password}`);
    console.log(`Client ID: ${result.id}`);
  }
  process.exit(0);
}).catch(console.error);
