{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport api from './api';\nclass UserManagementService {\n  constructor() {\n    this.baseURL = '/users';\n  }\n\n  /**\n   * Get all users with filtering and pagination\n   */\n  async getUsers(params = {}) {\n    try {\n      const response = await api.get(this.baseURL, {\n        params\n      });\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Get user statistics\n   */\n  async getUserStats() {\n    try {\n      const response = await api.get(`${this.baseURL}/stats`);\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Get specific user details\n   */\n  async getUser(userId) {\n    try {\n      const response = await api.get(`${this.baseURL}/${userId}`);\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Create new user (admin or client)\n   */\n  async createUser(userData) {\n    try {\n      const response = await api.post(this.baseURL, userData);\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Update user information\n   */\n  async updateUser(userId, userData) {\n    try {\n      const response = await api.put(`${this.baseURL}/${userId}`, userData);\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Update user status (activate, suspend, etc.)\n   */\n  async updateUserStatus(userId, status, reason = '') {\n    try {\n      const response = await api.patch(`${this.baseURL}/${userId}/status`, {\n        status,\n        reason\n      });\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Delete user\n   */\n  async deleteUser(userId, reason = '') {\n    try {\n      const response = await api.delete(`${this.baseURL}/${userId}`, {\n        data: {\n          reason\n        }\n      });\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Reset user password\n   */\n  async resetUserPassword(userId, newPassword) {\n    try {\n      const response = await api.post(`${this.baseURL}/${userId}/reset-password`, {\n        new_password: newPassword\n      });\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Get user activity logs\n   */\n  async getUserActivity(userId, params = {}) {\n    try {\n      const response = await api.get(`${this.baseURL}/${userId}/activity`, {\n        params\n      });\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Bulk operations on users\n   */\n  async bulkUpdateUsers(userIds, action, data = {}) {\n    try {\n      const response = await api.post(`${this.baseURL}/bulk`, {\n        user_ids: userIds,\n        action,\n        data\n      });\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Export users data\n   */\n  async exportUsers(format = 'csv', filters = {}) {\n    try {\n      const response = await api.get(`${this.baseURL}/export`, {\n        params: {\n          format,\n          ...filters\n        },\n        responseType: 'blob'\n      });\n\n      // Create download link\n      const url = window.URL.createObjectURL(new Blob([response.data]));\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', `users_export_${new Date().toISOString().split('T')[0]}.${format}`);\n      document.body.appendChild(link);\n      link.click();\n      link.remove();\n      window.URL.revokeObjectURL(url);\n      return {\n        success: true,\n        message: 'Export completed successfully'\n      };\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Get available user roles/types\n   */\n  async getUserRoles() {\n    try {\n      const response = await api.get(`${this.baseURL}/roles`);\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Handle successful API response\n   */\n  handleResponse(response) {\n    if (response.data.success) {\n      return {\n        success: true,\n        data: response.data.data,\n        message: response.data.message,\n        pagination: response.data.pagination\n      };\n    } else {\n      throw new Error(response.data.message || 'Operation failed');\n    }\n  }\n\n  /**\n   * Handle API errors\n   */\n  handleError(error) {\n    console.error('User Management Service Error:', error);\n    let message = 'An unexpected error occurred';\n    let errors = [];\n    let status = 0;\n    if (error.response) {\n      status = error.response.status;\n      const data = error.response.data;\n      if (data.message) {\n        message = data.message;\n      }\n      if (data.errors) {\n        errors = Array.isArray(data.errors) ? data.errors : [data.errors];\n      }\n\n      // Handle specific HTTP status codes\n      switch (status) {\n        case 401:\n          message = 'Authentication required. Please log in again.';\n          break;\n        case 403:\n          message = 'You do not have permission to perform this action.';\n          break;\n        case 404:\n          message = 'User not found.';\n          break;\n        case 422:\n          message = 'Validation failed. Please check your input.';\n          break;\n        case 429:\n          message = 'Too many requests. Please try again later.';\n          break;\n        case 500:\n          message = 'Server error. Please try again later.';\n          break;\n      }\n    } else if (error.request) {\n      message = 'Network error. Please check your connection.';\n    }\n    return {\n      success: false,\n      message,\n      errors,\n      status\n    };\n  }\n\n  /**\n   * Validate user data before submission\n   */\n  validateUserData(userData, isUpdate = false) {\n    const errors = [];\n    if (!isUpdate || userData.username) {\n      if (!userData.username || userData.username.length < 3) {\n        errors.push('Username must be at least 3 characters long');\n      }\n    }\n    if (!isUpdate || userData.email) {\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (!userData.email || !emailRegex.test(userData.email)) {\n        errors.push('Please provide a valid email address');\n      }\n    }\n    if (!isUpdate || userData.password) {\n      if (!userData.password || userData.password.length < 6) {\n        errors.push('Password must be at least 6 characters long');\n      }\n    }\n    if (!isUpdate || userData.first_name) {\n      if (!userData.first_name || userData.first_name.trim().length < 2) {\n        errors.push('First name must be at least 2 characters long');\n      }\n    }\n    if (!isUpdate || userData.last_name) {\n      if (!userData.last_name || userData.last_name.trim().length < 2) {\n        errors.push('Last name must be at least 2 characters long');\n      }\n    }\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n\n  /**\n   * Format user data for display\n   */\n  formatUserData(user) {\n    return {\n      ...user,\n      full_name: `${user.first_name || ''} ${user.last_name || ''}`.trim(),\n      status_label: this.getStatusLabel(user.status),\n      type_label: this.getTypeLabel(user.type || user.role),\n      created_at_formatted: this.formatDate(user.created_at),\n      last_login_formatted: user.last_login ? this.formatDate(user.last_login) : 'Never'\n    };\n  }\n\n  /**\n   * Get human-readable status label\n   */\n  getStatusLabel(status) {\n    const labels = {\n      'active': 'Active',\n      'inactive': 'Inactive',\n      'pending': 'Pending Verification',\n      'suspended': 'Suspended',\n      'pending_verification': 'Pending Verification'\n    };\n    return labels[status] || status;\n  }\n\n  /**\n   * Get human-readable type label\n   */\n  getTypeLabel(type) {\n    const labels = {\n      'admin': 'Administrator',\n      'employee': 'Employee',\n      'client': 'Client',\n      'user': 'User'\n    };\n    return labels[type] || type;\n  }\n\n  /**\n   * Format date for display\n   */\n  formatDate(dateString) {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n}\nexport default new UserManagementService();", "map": {"version": 3, "names": ["api", "UserManagementService", "constructor", "baseURL", "getUsers", "params", "response", "get", "handleResponse", "error", "handleError", "getUserStats", "getUser", "userId", "createUser", "userData", "post", "updateUser", "put", "updateUserStatus", "status", "reason", "patch", "deleteUser", "delete", "data", "resetUserPassword", "newPassword", "new_password", "getUserActivity", "bulkUpdateUsers", "userIds", "action", "user_ids", "exportUsers", "format", "filters", "responseType", "url", "window", "URL", "createObjectURL", "Blob", "link", "document", "createElement", "href", "setAttribute", "Date", "toISOString", "split", "body", "append<PERSON><PERSON><PERSON>", "click", "remove", "revokeObjectURL", "success", "message", "getUserRoles", "pagination", "Error", "console", "errors", "Array", "isArray", "request", "validateUserData", "isUpdate", "username", "length", "push", "email", "emailRegex", "test", "password", "first_name", "trim", "last_name", "<PERSON><PERSON><PERSON><PERSON>", "formatUserData", "user", "full_name", "status_label", "getStatusLabel", "type_label", "getTypeLabel", "type", "role", "created_at_formatted", "formatDate", "created_at", "last_login_formatted", "last_login", "labels", "dateString", "date", "toLocaleDateString", "year", "month", "day", "hour", "minute"], "sources": ["D:/rhai_front_and_back/BOSFDR/src/services/userManagementService.js"], "sourcesContent": ["import api from './api';\n\nclass UserManagementService {\n  constructor() {\n    this.baseURL = '/users';\n  }\n\n  /**\n   * Get all users with filtering and pagination\n   */\n  async getUsers(params = {}) {\n    try {\n      const response = await api.get(this.baseURL, { params });\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Get user statistics\n   */\n  async getUserStats() {\n    try {\n      const response = await api.get(`${this.baseURL}/stats`);\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Get specific user details\n   */\n  async getUser(userId) {\n    try {\n      const response = await api.get(`${this.baseURL}/${userId}`);\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Create new user (admin or client)\n   */\n  async createUser(userData) {\n    try {\n      const response = await api.post(this.baseURL, userData);\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Update user information\n   */\n  async updateUser(userId, userData) {\n    try {\n      const response = await api.put(`${this.baseURL}/${userId}`, userData);\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Update user status (activate, suspend, etc.)\n   */\n  async updateUserStatus(userId, status, reason = '') {\n    try {\n      const response = await api.patch(`${this.baseURL}/${userId}/status`, {\n        status,\n        reason\n      });\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Delete user\n   */\n  async deleteUser(userId, reason = '') {\n    try {\n      const response = await api.delete(`${this.baseURL}/${userId}`, {\n        data: { reason }\n      });\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Reset user password\n   */\n  async resetUserPassword(userId, newPassword) {\n    try {\n      const response = await api.post(`${this.baseURL}/${userId}/reset-password`, {\n        new_password: newPassword\n      });\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Get user activity logs\n   */\n  async getUserActivity(userId, params = {}) {\n    try {\n      const response = await api.get(`${this.baseURL}/${userId}/activity`, { params });\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Bulk operations on users\n   */\n  async bulkUpdateUsers(userIds, action, data = {}) {\n    try {\n      const response = await api.post(`${this.baseURL}/bulk`, {\n        user_ids: userIds,\n        action,\n        data\n      });\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Export users data\n   */\n  async exportUsers(format = 'csv', filters = {}) {\n    try {\n      const response = await api.get(`${this.baseURL}/export`, {\n        params: { format, ...filters },\n        responseType: 'blob'\n      });\n      \n      // Create download link\n      const url = window.URL.createObjectURL(new Blob([response.data]));\n      const link = document.createElement('a');\n      link.href = url;\n      link.setAttribute('download', `users_export_${new Date().toISOString().split('T')[0]}.${format}`);\n      document.body.appendChild(link);\n      link.click();\n      link.remove();\n      window.URL.revokeObjectURL(url);\n      \n      return { success: true, message: 'Export completed successfully' };\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Get available user roles/types\n   */\n  async getUserRoles() {\n    try {\n      const response = await api.get(`${this.baseURL}/roles`);\n      return this.handleResponse(response);\n    } catch (error) {\n      return this.handleError(error);\n    }\n  }\n\n  /**\n   * Handle successful API response\n   */\n  handleResponse(response) {\n    if (response.data.success) {\n      return {\n        success: true,\n        data: response.data.data,\n        message: response.data.message,\n        pagination: response.data.pagination\n      };\n    } else {\n      throw new Error(response.data.message || 'Operation failed');\n    }\n  }\n\n  /**\n   * Handle API errors\n   */\n  handleError(error) {\n    console.error('User Management Service Error:', error);\n    \n    let message = 'An unexpected error occurred';\n    let errors = [];\n    let status = 0;\n\n    if (error.response) {\n      status = error.response.status;\n      const data = error.response.data;\n      \n      if (data.message) {\n        message = data.message;\n      }\n      \n      if (data.errors) {\n        errors = Array.isArray(data.errors) ? data.errors : [data.errors];\n      }\n      \n      // Handle specific HTTP status codes\n      switch (status) {\n        case 401:\n          message = 'Authentication required. Please log in again.';\n          break;\n        case 403:\n          message = 'You do not have permission to perform this action.';\n          break;\n        case 404:\n          message = 'User not found.';\n          break;\n        case 422:\n          message = 'Validation failed. Please check your input.';\n          break;\n        case 429:\n          message = 'Too many requests. Please try again later.';\n          break;\n        case 500:\n          message = 'Server error. Please try again later.';\n          break;\n      }\n    } else if (error.request) {\n      message = 'Network error. Please check your connection.';\n    }\n\n    return {\n      success: false,\n      message,\n      errors,\n      status\n    };\n  }\n\n  /**\n   * Validate user data before submission\n   */\n  validateUserData(userData, isUpdate = false) {\n    const errors = [];\n\n    if (!isUpdate || userData.username) {\n      if (!userData.username || userData.username.length < 3) {\n        errors.push('Username must be at least 3 characters long');\n      }\n    }\n\n    if (!isUpdate || userData.email) {\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (!userData.email || !emailRegex.test(userData.email)) {\n        errors.push('Please provide a valid email address');\n      }\n    }\n\n    if (!isUpdate || userData.password) {\n      if (!userData.password || userData.password.length < 6) {\n        errors.push('Password must be at least 6 characters long');\n      }\n    }\n\n    if (!isUpdate || userData.first_name) {\n      if (!userData.first_name || userData.first_name.trim().length < 2) {\n        errors.push('First name must be at least 2 characters long');\n      }\n    }\n\n    if (!isUpdate || userData.last_name) {\n      if (!userData.last_name || userData.last_name.trim().length < 2) {\n        errors.push('Last name must be at least 2 characters long');\n      }\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n\n  /**\n   * Format user data for display\n   */\n  formatUserData(user) {\n    return {\n      ...user,\n      full_name: `${user.first_name || ''} ${user.last_name || ''}`.trim(),\n      status_label: this.getStatusLabel(user.status),\n      type_label: this.getTypeLabel(user.type || user.role),\n      created_at_formatted: this.formatDate(user.created_at),\n      last_login_formatted: user.last_login ? this.formatDate(user.last_login) : 'Never'\n    };\n  }\n\n  /**\n   * Get human-readable status label\n   */\n  getStatusLabel(status) {\n    const labels = {\n      'active': 'Active',\n      'inactive': 'Inactive',\n      'pending': 'Pending Verification',\n      'suspended': 'Suspended',\n      'pending_verification': 'Pending Verification'\n    };\n    return labels[status] || status;\n  }\n\n  /**\n   * Get human-readable type label\n   */\n  getTypeLabel(type) {\n    const labels = {\n      'admin': 'Administrator',\n      'employee': 'Employee',\n      'client': 'Client',\n      'user': 'User'\n    };\n    return labels[type] || type;\n  }\n\n  /**\n   * Format date for display\n   */\n  formatDate(dateString) {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n}\n\nexport default new UserManagementService();\n"], "mappings": ";AAAA,OAAOA,GAAG,MAAM,OAAO;AAEvB,MAAMC,qBAAqB,CAAC;EAC1BC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAG,QAAQ;EACzB;;EAEA;AACF;AACA;EACE,MAAMC,QAAQA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;IAC1B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMN,GAAG,CAACO,GAAG,CAAC,IAAI,CAACJ,OAAO,EAAE;QAAEE;MAAO,CAAC,CAAC;MACxD,OAAO,IAAI,CAACG,cAAc,CAACF,QAAQ,CAAC;IACtC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd,OAAO,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC;IAChC;EACF;;EAEA;AACF;AACA;EACE,MAAME,YAAYA,CAAA,EAAG;IACnB,IAAI;MACF,MAAML,QAAQ,GAAG,MAAMN,GAAG,CAACO,GAAG,CAAC,GAAG,IAAI,CAACJ,OAAO,QAAQ,CAAC;MACvD,OAAO,IAAI,CAACK,cAAc,CAACF,QAAQ,CAAC;IACtC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd,OAAO,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC;IAChC;EACF;;EAEA;AACF;AACA;EACE,MAAMG,OAAOA,CAACC,MAAM,EAAE;IACpB,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMN,GAAG,CAACO,GAAG,CAAC,GAAG,IAAI,CAACJ,OAAO,IAAIU,MAAM,EAAE,CAAC;MAC3D,OAAO,IAAI,CAACL,cAAc,CAACF,QAAQ,CAAC;IACtC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd,OAAO,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC;IAChC;EACF;;EAEA;AACF;AACA;EACE,MAAMK,UAAUA,CAACC,QAAQ,EAAE;IACzB,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMN,GAAG,CAACgB,IAAI,CAAC,IAAI,CAACb,OAAO,EAAEY,QAAQ,CAAC;MACvD,OAAO,IAAI,CAACP,cAAc,CAACF,QAAQ,CAAC;IACtC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd,OAAO,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC;IAChC;EACF;;EAEA;AACF;AACA;EACE,MAAMQ,UAAUA,CAACJ,MAAM,EAAEE,QAAQ,EAAE;IACjC,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMN,GAAG,CAACkB,GAAG,CAAC,GAAG,IAAI,CAACf,OAAO,IAAIU,MAAM,EAAE,EAAEE,QAAQ,CAAC;MACrE,OAAO,IAAI,CAACP,cAAc,CAACF,QAAQ,CAAC;IACtC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd,OAAO,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC;IAChC;EACF;;EAEA;AACF;AACA;EACE,MAAMU,gBAAgBA,CAACN,MAAM,EAAEO,MAAM,EAAEC,MAAM,GAAG,EAAE,EAAE;IAClD,IAAI;MACF,MAAMf,QAAQ,GAAG,MAAMN,GAAG,CAACsB,KAAK,CAAC,GAAG,IAAI,CAACnB,OAAO,IAAIU,MAAM,SAAS,EAAE;QACnEO,MAAM;QACNC;MACF,CAAC,CAAC;MACF,OAAO,IAAI,CAACb,cAAc,CAACF,QAAQ,CAAC;IACtC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd,OAAO,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC;IAChC;EACF;;EAEA;AACF;AACA;EACE,MAAMc,UAAUA,CAACV,MAAM,EAAEQ,MAAM,GAAG,EAAE,EAAE;IACpC,IAAI;MACF,MAAMf,QAAQ,GAAG,MAAMN,GAAG,CAACwB,MAAM,CAAC,GAAG,IAAI,CAACrB,OAAO,IAAIU,MAAM,EAAE,EAAE;QAC7DY,IAAI,EAAE;UAAEJ;QAAO;MACjB,CAAC,CAAC;MACF,OAAO,IAAI,CAACb,cAAc,CAACF,QAAQ,CAAC;IACtC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd,OAAO,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC;IAChC;EACF;;EAEA;AACF;AACA;EACE,MAAMiB,iBAAiBA,CAACb,MAAM,EAAEc,WAAW,EAAE;IAC3C,IAAI;MACF,MAAMrB,QAAQ,GAAG,MAAMN,GAAG,CAACgB,IAAI,CAAC,GAAG,IAAI,CAACb,OAAO,IAAIU,MAAM,iBAAiB,EAAE;QAC1Ee,YAAY,EAAED;MAChB,CAAC,CAAC;MACF,OAAO,IAAI,CAACnB,cAAc,CAACF,QAAQ,CAAC;IACtC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd,OAAO,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC;IAChC;EACF;;EAEA;AACF;AACA;EACE,MAAMoB,eAAeA,CAAChB,MAAM,EAAER,MAAM,GAAG,CAAC,CAAC,EAAE;IACzC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMN,GAAG,CAACO,GAAG,CAAC,GAAG,IAAI,CAACJ,OAAO,IAAIU,MAAM,WAAW,EAAE;QAAER;MAAO,CAAC,CAAC;MAChF,OAAO,IAAI,CAACG,cAAc,CAACF,QAAQ,CAAC;IACtC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd,OAAO,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC;IAChC;EACF;;EAEA;AACF;AACA;EACE,MAAMqB,eAAeA,CAACC,OAAO,EAAEC,MAAM,EAAEP,IAAI,GAAG,CAAC,CAAC,EAAE;IAChD,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAMN,GAAG,CAACgB,IAAI,CAAC,GAAG,IAAI,CAACb,OAAO,OAAO,EAAE;QACtD8B,QAAQ,EAAEF,OAAO;QACjBC,MAAM;QACNP;MACF,CAAC,CAAC;MACF,OAAO,IAAI,CAACjB,cAAc,CAACF,QAAQ,CAAC;IACtC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd,OAAO,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC;IAChC;EACF;;EAEA;AACF;AACA;EACE,MAAMyB,WAAWA,CAACC,MAAM,GAAG,KAAK,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC9C,IAAI;MACF,MAAM9B,QAAQ,GAAG,MAAMN,GAAG,CAACO,GAAG,CAAC,GAAG,IAAI,CAACJ,OAAO,SAAS,EAAE;QACvDE,MAAM,EAAE;UAAE8B,MAAM;UAAE,GAAGC;QAAQ,CAAC;QAC9BC,YAAY,EAAE;MAChB,CAAC,CAAC;;MAEF;MACA,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAACpC,QAAQ,CAACmB,IAAI,CAAC,CAAC,CAAC;MACjE,MAAMkB,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGR,GAAG;MACfK,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,gBAAgB,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAIf,MAAM,EAAE,CAAC;MACjGS,QAAQ,CAACO,IAAI,CAACC,WAAW,CAACT,IAAI,CAAC;MAC/BA,IAAI,CAACU,KAAK,CAAC,CAAC;MACZV,IAAI,CAACW,MAAM,CAAC,CAAC;MACbf,MAAM,CAACC,GAAG,CAACe,eAAe,CAACjB,GAAG,CAAC;MAE/B,OAAO;QAAEkB,OAAO,EAAE,IAAI;QAAEC,OAAO,EAAE;MAAgC,CAAC;IACpE,CAAC,CAAC,OAAOhD,KAAK,EAAE;MACd,OAAO,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC;IAChC;EACF;;EAEA;AACF;AACA;EACE,MAAMiD,YAAYA,CAAA,EAAG;IACnB,IAAI;MACF,MAAMpD,QAAQ,GAAG,MAAMN,GAAG,CAACO,GAAG,CAAC,GAAG,IAAI,CAACJ,OAAO,QAAQ,CAAC;MACvD,OAAO,IAAI,CAACK,cAAc,CAACF,QAAQ,CAAC;IACtC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd,OAAO,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC;IAChC;EACF;;EAEA;AACF;AACA;EACED,cAAcA,CAACF,QAAQ,EAAE;IACvB,IAAIA,QAAQ,CAACmB,IAAI,CAAC+B,OAAO,EAAE;MACzB,OAAO;QACLA,OAAO,EAAE,IAAI;QACb/B,IAAI,EAAEnB,QAAQ,CAACmB,IAAI,CAACA,IAAI;QACxBgC,OAAO,EAAEnD,QAAQ,CAACmB,IAAI,CAACgC,OAAO;QAC9BE,UAAU,EAAErD,QAAQ,CAACmB,IAAI,CAACkC;MAC5B,CAAC;IACH,CAAC,MAAM;MACL,MAAM,IAAIC,KAAK,CAACtD,QAAQ,CAACmB,IAAI,CAACgC,OAAO,IAAI,kBAAkB,CAAC;IAC9D;EACF;;EAEA;AACF;AACA;EACE/C,WAAWA,CAACD,KAAK,EAAE;IACjBoD,OAAO,CAACpD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IAEtD,IAAIgD,OAAO,GAAG,8BAA8B;IAC5C,IAAIK,MAAM,GAAG,EAAE;IACf,IAAI1C,MAAM,GAAG,CAAC;IAEd,IAAIX,KAAK,CAACH,QAAQ,EAAE;MAClBc,MAAM,GAAGX,KAAK,CAACH,QAAQ,CAACc,MAAM;MAC9B,MAAMK,IAAI,GAAGhB,KAAK,CAACH,QAAQ,CAACmB,IAAI;MAEhC,IAAIA,IAAI,CAACgC,OAAO,EAAE;QAChBA,OAAO,GAAGhC,IAAI,CAACgC,OAAO;MACxB;MAEA,IAAIhC,IAAI,CAACqC,MAAM,EAAE;QACfA,MAAM,GAAGC,KAAK,CAACC,OAAO,CAACvC,IAAI,CAACqC,MAAM,CAAC,GAAGrC,IAAI,CAACqC,MAAM,GAAG,CAACrC,IAAI,CAACqC,MAAM,CAAC;MACnE;;MAEA;MACA,QAAQ1C,MAAM;QACZ,KAAK,GAAG;UACNqC,OAAO,GAAG,+CAA+C;UACzD;QACF,KAAK,GAAG;UACNA,OAAO,GAAG,oDAAoD;UAC9D;QACF,KAAK,GAAG;UACNA,OAAO,GAAG,iBAAiB;UAC3B;QACF,KAAK,GAAG;UACNA,OAAO,GAAG,6CAA6C;UACvD;QACF,KAAK,GAAG;UACNA,OAAO,GAAG,4CAA4C;UACtD;QACF,KAAK,GAAG;UACNA,OAAO,GAAG,uCAAuC;UACjD;MACJ;IACF,CAAC,MAAM,IAAIhD,KAAK,CAACwD,OAAO,EAAE;MACxBR,OAAO,GAAG,8CAA8C;IAC1D;IAEA,OAAO;MACLD,OAAO,EAAE,KAAK;MACdC,OAAO;MACPK,MAAM;MACN1C;IACF,CAAC;EACH;;EAEA;AACF;AACA;EACE8C,gBAAgBA,CAACnD,QAAQ,EAAEoD,QAAQ,GAAG,KAAK,EAAE;IAC3C,MAAML,MAAM,GAAG,EAAE;IAEjB,IAAI,CAACK,QAAQ,IAAIpD,QAAQ,CAACqD,QAAQ,EAAE;MAClC,IAAI,CAACrD,QAAQ,CAACqD,QAAQ,IAAIrD,QAAQ,CAACqD,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;QACtDP,MAAM,CAACQ,IAAI,CAAC,6CAA6C,CAAC;MAC5D;IACF;IAEA,IAAI,CAACH,QAAQ,IAAIpD,QAAQ,CAACwD,KAAK,EAAE;MAC/B,MAAMC,UAAU,GAAG,4BAA4B;MAC/C,IAAI,CAACzD,QAAQ,CAACwD,KAAK,IAAI,CAACC,UAAU,CAACC,IAAI,CAAC1D,QAAQ,CAACwD,KAAK,CAAC,EAAE;QACvDT,MAAM,CAACQ,IAAI,CAAC,sCAAsC,CAAC;MACrD;IACF;IAEA,IAAI,CAACH,QAAQ,IAAIpD,QAAQ,CAAC2D,QAAQ,EAAE;MAClC,IAAI,CAAC3D,QAAQ,CAAC2D,QAAQ,IAAI3D,QAAQ,CAAC2D,QAAQ,CAACL,MAAM,GAAG,CAAC,EAAE;QACtDP,MAAM,CAACQ,IAAI,CAAC,6CAA6C,CAAC;MAC5D;IACF;IAEA,IAAI,CAACH,QAAQ,IAAIpD,QAAQ,CAAC4D,UAAU,EAAE;MACpC,IAAI,CAAC5D,QAAQ,CAAC4D,UAAU,IAAI5D,QAAQ,CAAC4D,UAAU,CAACC,IAAI,CAAC,CAAC,CAACP,MAAM,GAAG,CAAC,EAAE;QACjEP,MAAM,CAACQ,IAAI,CAAC,+CAA+C,CAAC;MAC9D;IACF;IAEA,IAAI,CAACH,QAAQ,IAAIpD,QAAQ,CAAC8D,SAAS,EAAE;MACnC,IAAI,CAAC9D,QAAQ,CAAC8D,SAAS,IAAI9D,QAAQ,CAAC8D,SAAS,CAACD,IAAI,CAAC,CAAC,CAACP,MAAM,GAAG,CAAC,EAAE;QAC/DP,MAAM,CAACQ,IAAI,CAAC,8CAA8C,CAAC;MAC7D;IACF;IAEA,OAAO;MACLQ,OAAO,EAAEhB,MAAM,CAACO,MAAM,KAAK,CAAC;MAC5BP;IACF,CAAC;EACH;;EAEA;AACF;AACA;EACEiB,cAAcA,CAACC,IAAI,EAAE;IACnB,OAAO;MACL,GAAGA,IAAI;MACPC,SAAS,EAAE,GAAGD,IAAI,CAACL,UAAU,IAAI,EAAE,IAAIK,IAAI,CAACH,SAAS,IAAI,EAAE,EAAE,CAACD,IAAI,CAAC,CAAC;MACpEM,YAAY,EAAE,IAAI,CAACC,cAAc,CAACH,IAAI,CAAC5D,MAAM,CAAC;MAC9CgE,UAAU,EAAE,IAAI,CAACC,YAAY,CAACL,IAAI,CAACM,IAAI,IAAIN,IAAI,CAACO,IAAI,CAAC;MACrDC,oBAAoB,EAAE,IAAI,CAACC,UAAU,CAACT,IAAI,CAACU,UAAU,CAAC;MACtDC,oBAAoB,EAAEX,IAAI,CAACY,UAAU,GAAG,IAAI,CAACH,UAAU,CAACT,IAAI,CAACY,UAAU,CAAC,GAAG;IAC7E,CAAC;EACH;;EAEA;AACF;AACA;EACET,cAAcA,CAAC/D,MAAM,EAAE;IACrB,MAAMyE,MAAM,GAAG;MACb,QAAQ,EAAE,QAAQ;MAClB,UAAU,EAAE,UAAU;MACtB,SAAS,EAAE,sBAAsB;MACjC,WAAW,EAAE,WAAW;MACxB,sBAAsB,EAAE;IAC1B,CAAC;IACD,OAAOA,MAAM,CAACzE,MAAM,CAAC,IAAIA,MAAM;EACjC;;EAEA;AACF;AACA;EACEiE,YAAYA,CAACC,IAAI,EAAE;IACjB,MAAMO,MAAM,GAAG;MACb,OAAO,EAAE,eAAe;MACxB,UAAU,EAAE,UAAU;MACtB,QAAQ,EAAE,QAAQ;MAClB,MAAM,EAAE;IACV,CAAC;IACD,OAAOA,MAAM,CAACP,IAAI,CAAC,IAAIA,IAAI;EAC7B;;EAEA;AACF;AACA;EACEG,UAAUA,CAACK,UAAU,EAAE;IACrB,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAC1B,MAAMC,IAAI,GAAG,IAAI/C,IAAI,CAAC8C,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;AACF;AAEA,eAAe,IAAIpG,qBAAqB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}