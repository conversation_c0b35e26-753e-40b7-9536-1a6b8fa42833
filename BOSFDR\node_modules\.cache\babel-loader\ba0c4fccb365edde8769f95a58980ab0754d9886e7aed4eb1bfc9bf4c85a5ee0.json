{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport axios from 'axios';\nclass NotificationService {\n  constructor() {\n    // CRITICAL: Prevent multiple instances\n    if (window.__notificationServiceInstance) {\n      return window.__notificationServiceInstance;\n    }\n    this.eventSource = null;\n    this.listeners = new Map();\n    this.isConnected = false;\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n    this.reconnectDelay = 1000; // Start with 1 second\n    this.maxReconnectDelay = 30000; // Max 30 seconds\n    this.baseURL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api';\n    this.connectionRefs = 0; // Track how many components are using the connection\n    this.currentUserType = null; // Track current connection type\n    this.isConnecting = false; // Prevent multiple simultaneous connection attempts\n    this.pollingInterval = null; // For polling-based notifications\n\n    // Store global reference\n    window.__notificationServiceInstance = this;\n  }\n\n  /**\n   * Initialize connection (simplified)\n   */\n  init(userType = 'admin') {\n    console.log('🚀 Initializing notification service');\n    if (!this.eventSource) {\n      this.connect(userType);\n    }\n    return Promise.resolve();\n  }\n\n  /**\n   * Cleanup (simplified)\n   */\n  cleanup() {\n    console.log('🧹 Notification service cleanup');\n    // Don't disconnect - let connection persist\n  }\n\n  /**\n   * Connect using PROPER SSE (Google/Mozilla Standard)\n   */\n  connect(userType = 'admin') {\n    // Clean up any existing connections\n    this.disconnect();\n\n    // Prevent multiple calls\n    if (this.isConnected || this.isConnecting) {\n      console.log('🔗 Already connected or connecting');\n      return Promise.resolve();\n    }\n    this.isConnecting = true;\n    console.log('🔗 Establishing SSE connection (Google standard)');\n    return new Promise((resolve, reject) => {\n      try {\n        const token = userType === 'admin' ? localStorage.getItem('adminToken') : localStorage.getItem('clientToken');\n        if (!token) {\n          this.isConnecting = false;\n          reject(new Error('No authentication token'));\n          return;\n        }\n\n        // Create EventSource with proper URL\n        const url = `${this.baseURL}/notifications/stream?token=${encodeURIComponent(token)}`;\n        console.log('🔗 SSE URL:', url.replace(/token=[^&]+/, 'token=***'));\n        this.eventSource = new EventSource(url);\n        this.currentUserType = userType;\n\n        // Store references to prevent garbage collection\n        window.__sseConnection = this.eventSource;\n        window.__notificationEventSource = this.eventSource;\n\n        // PROPER event handlers (Google/Mozilla standard)\n        this.eventSource.onopen = event => {\n          console.log('✅ SSE Connection established');\n          this.isConnected = true;\n          this.isConnecting = false;\n          this.reconnectAttempts = 0;\n          this.emit('connected');\n          resolve();\n        };\n        this.eventSource.onmessage = event => {\n          try {\n            const data = JSON.parse(event.data);\n            console.log('📨 SSE Message:', data);\n            this.handleNotification(data);\n          } catch (error) {\n            console.error('SSE message parse error:', error);\n          }\n        };\n\n        // Handle specific event types\n        this.eventSource.addEventListener('connected', event => {\n          console.log('🎯 Connected event received');\n        });\n        this.eventSource.addEventListener('heartbeat', event => {\n          console.log('💓 Heartbeat received');\n        });\n        this.eventSource.onerror = event => {\n          console.error('❌ SSE Error:', event);\n          this.isConnected = false;\n          this.isConnecting = false;\n\n          // Handle different error states\n          if (this.eventSource.readyState === EventSource.CLOSED) {\n            console.log('🔌 SSE Connection closed by server');\n            this.emit('disconnected');\n          } else if (this.eventSource.readyState === EventSource.CONNECTING) {\n            console.log('🔄 SSE Reconnecting...');\n          }\n\n          // Auto-reconnect with exponential backoff\n          if (this.reconnectAttempts < this.maxReconnectAttempts) {\n            this.scheduleReconnect();\n          } else {\n            console.error('🚫 Max reconnection attempts reached');\n            this.emit('max_reconnect_attempts');\n            reject(new Error('Max reconnection attempts reached'));\n          }\n        };\n      } catch (error) {\n        console.error('Failed to create SSE connection:', error);\n        this.isConnecting = false;\n        reject(error);\n      }\n    });\n  }\n\n  /**\n   * Poll for new notifications and updates\n   */\n  async pollNotifications(userType = 'admin') {\n    try {\n      const token = userType === 'admin' ? localStorage.getItem('adminToken') : localStorage.getItem('clientToken');\n      if (!token) return;\n\n      // Get unread count\n      const response = await fetch(`${this.baseURL}/notifications/unread-count`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        const count = data.data?.count || data.count || 0;\n\n        // Only emit if count changed or every 10th poll\n        if (!this.lastCount || this.lastCount !== count || this.pollCount % 10 === 0) {\n          this.emit('notification', {\n            type: 'unread_count_update',\n            count: count,\n            timestamp: new Date().toISOString()\n          });\n          this.lastCount = count;\n        }\n        this.pollCount = (this.pollCount || 0) + 1;\n      }\n    } catch (error) {\n      console.error('Failed to poll notifications:', error);\n    }\n  }\n\n  /**\n   * Disconnect from SSE (Google/Mozilla standard)\n   */\n  disconnect() {\n    console.log('🔌 Disconnecting SSE connection');\n    this.isConnected = false;\n    this.isConnecting = false;\n\n    // Clear any reconnection timers\n    if (this.reconnectTimer) {\n      clearTimeout(this.reconnectTimer);\n      this.reconnectTimer = null;\n    }\n\n    // Close EventSource properly\n    if (this.eventSource) {\n      console.log('🔌 Closing EventSource');\n      this.eventSource.close();\n      this.eventSource = null;\n    }\n\n    // Clear global references\n    if (window.__sseConnection) {\n      delete window.__sseConnection;\n    }\n    if (window.__notificationEventSource) {\n      delete window.__notificationEventSource;\n    }\n\n    // Clean up any polling fallback\n    if (this.pollingInterval) {\n      clearInterval(this.pollingInterval);\n      this.pollingInterval = null;\n    }\n    console.log('✅ SSE Disconnected cleanly');\n    this.emit('disconnected');\n  }\n\n  /**\n   * Schedule reconnection attempt\n   */\n  scheduleReconnect() {\n    console.log('🚫 Auto-reconnect disabled to prevent connection loops');\n    // Disabled to prevent connection issues during debugging\n  }\n\n  /**\n   * Handle incoming notification\n   */\n  handleNotification(notification) {\n    console.log('📢 Received notification:', notification);\n\n    // Emit to specific type listeners\n    this.emit(notification.type, notification);\n\n    // Emit to general notification listeners\n    this.emit('notification', notification);\n\n    // Show browser notification if permission granted\n    this.showBrowserNotification(notification);\n  }\n\n  /**\n   * Show browser notification\n   */\n  showBrowserNotification(notification) {\n    if ('Notification' in window && Notification.permission === 'granted') {\n      const options = {\n        body: notification.message,\n        icon: '/favicon.ico',\n        badge: '/favicon.ico',\n        tag: `notification-${notification.id}`,\n        requireInteraction: notification.priority === 'high' || notification.priority === 'urgent'\n      };\n      const browserNotification = new Notification(notification.title, options);\n      browserNotification.onclick = () => {\n        window.focus();\n        this.emit('notification_click', notification);\n        browserNotification.close();\n      };\n\n      // Auto close after 5 seconds for normal priority\n      if (notification.priority !== 'high' && notification.priority !== 'urgent') {\n        setTimeout(() => {\n          browserNotification.close();\n        }, 5000);\n      }\n    }\n  }\n\n  /**\n   * Request browser notification permission\n   */\n  async requestNotificationPermission() {\n    if ('Notification' in window) {\n      const permission = await Notification.requestPermission();\n      return permission === 'granted';\n    }\n    return false;\n  }\n\n  /**\n   * Subscribe to notification events\n   */\n  on(event, callback) {\n    if (!this.listeners.has(event)) {\n      this.listeners.set(event, new Set());\n    }\n    this.listeners.get(event).add(callback);\n  }\n\n  /**\n   * Unsubscribe from notification events\n   */\n  off(event, callback) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).delete(callback);\n    }\n  }\n\n  /**\n   * Emit event to listeners\n   */\n  emit(event, data = null) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).forEach(callback => {\n        try {\n          callback(data);\n        } catch (error) {\n          console.error(`Error in notification listener for ${event}:`, error);\n        }\n      });\n    }\n  }\n\n  /**\n   * Get user notifications\n   */\n  async getNotifications(page = 1, limit = 20, unreadOnly = false) {\n    try {\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin ? localStorage.getItem('adminToken') : localStorage.getItem('clientToken');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.get(`${this.baseURL}/notifications`, {\n        params: {\n          page,\n          limit,\n          unread_only: unreadOnly\n        },\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get notifications:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get unread notification count\n   */\n  async getUnreadCount() {\n    try {\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin ? localStorage.getItem('adminToken') : localStorage.getItem('clientToken');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.get(`${this.baseURL}/notifications/unread-count`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data.data.count;\n    } catch (error) {\n      console.error('Failed to get unread count:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Mark notification as read\n   */\n  async markAsRead(notificationId) {\n    try {\n      // Validate notification ID\n      if (!notificationId || notificationId === 'undefined') {\n        throw new Error('Invalid notification ID provided');\n      }\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin ? localStorage.getItem('adminToken') : localStorage.getItem('clientToken');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      console.log('Marking notification as read:', notificationId);\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.put(`${this.baseURL}/notifications/${notificationId}/read`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to mark notification as read:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Mark all notifications as read\n   */\n  async markAllAsRead() {\n    try {\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin ? localStorage.getItem('adminToken') : localStorage.getItem('clientToken');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.put(`${this.baseURL}/notifications/mark-all-read`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to mark all notifications as read:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Send test notification (admin only)\n   */\n  async sendTestNotification(data) {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.post(`${this.baseURL}/notifications/test`, data, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to send test notification:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get notification statistics (admin only)\n   */\n  async getStatistics() {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.get(`${this.baseURL}/notifications/statistics`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get notification statistics:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Test SSE connection (for debugging)\n   */\n  async testConnection() {\n    console.log('🧪 Testing SSE connection...');\n    try {\n      // Clear any existing connection\n      if (this.eventSource) {\n        console.log('🧪 Clearing existing connection');\n        this.eventSource.close();\n        this.eventSource = null;\n        this.isConnected = false;\n        this.isConnecting = false;\n      }\n\n      // Reset state\n      this.connectionRefs = 1;\n\n      // Test connection\n      await this.connect('admin');\n      console.log('🧪 Test connection established');\n\n      // Keep connection alive for 10 seconds\n      setTimeout(() => {\n        console.log('🧪 Test completed, keeping connection');\n      }, 10000);\n    } catch (error) {\n      console.error('🧪 Test connection failed:', error);\n    }\n  }\n\n  /**\n   * Clean up old notifications (admin only)\n   */\n  async cleanupOldNotifications(days = 90) {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.delete(`${this.baseURL}/notifications/cleanup`, {\n        params: {\n          days\n        },\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to cleanup old notifications:', error);\n      throw error;\n    }\n  }\n}\n\n// Create singleton instance\nconst notificationService = new NotificationService();\n\n// Make available globally for debugging\nwindow.__notificationService = notificationService;\n\n// Add global test function\nwindow.testNotifications = () => {\n  console.log('🧪 Testing notification system manually...');\n  notificationService.connect('admin');\n};\nwindow.stopNotifications = () => {\n  console.log('🛑 EMERGENCY STOP - Clearing all polling intervals...');\n\n  // Clear all possible intervals\n  if (notificationService.pollingInterval) {\n    clearInterval(notificationService.pollingInterval);\n    notificationService.pollingInterval = null;\n  }\n\n  // Clear any orphaned intervals\n  for (let i = 1; i < 10000; i++) {\n    clearInterval(i);\n  }\n  notificationService.isConnected = false;\n  console.log('✅ All polling stopped');\n};\nexport default notificationService;", "map": {"version": 3, "names": ["axios", "NotificationService", "constructor", "window", "__notificationServiceInstance", "eventSource", "listeners", "Map", "isConnected", "reconnectAttempts", "maxReconnectAttempts", "reconnectDelay", "maxReconnectDelay", "baseURL", "process", "env", "VUE_APP_API_URL", "connectionRefs", "currentUserType", "isConnecting", "pollingInterval", "init", "userType", "console", "log", "connect", "Promise", "resolve", "cleanup", "disconnect", "reject", "token", "localStorage", "getItem", "Error", "url", "encodeURIComponent", "replace", "EventSource", "__sseConnection", "__notificationEventSource", "onopen", "event", "emit", "onmessage", "data", "JSON", "parse", "handleNotification", "error", "addEventListener", "onerror", "readyState", "CLOSED", "CONNECTING", "scheduleReconnect", "pollNotifications", "response", "fetch", "headers", "ok", "json", "count", "lastCount", "pollCount", "type", "timestamp", "Date", "toISOString", "reconnectTimer", "clearTimeout", "close", "clearInterval", "notification", "showBrowserNotification", "Notification", "permission", "options", "body", "message", "icon", "badge", "tag", "id", "requireInteraction", "priority", "browserNotification", "title", "onclick", "focus", "setTimeout", "requestNotificationPermission", "requestPermission", "on", "callback", "has", "set", "Set", "get", "add", "off", "delete", "for<PERSON>ach", "getNotifications", "page", "limit", "unreadOnly", "isAdmin", "params", "unread_only", "getUnreadCount", "mark<PERSON><PERSON><PERSON>", "notificationId", "put", "markAllAsRead", "sendTestNotification", "post", "getStatistics", "testConnection", "cleanupOldNotifications", "days", "notificationService", "__notificationService", "testNotifications", "stopNotifications", "i"], "sources": ["D:/rhai_front_and_back/BOSFDR/src/services/notificationService.js"], "sourcesContent": ["import axios from 'axios';\n\nclass NotificationService {\n  constructor() {\n    // CRITICAL: Prevent multiple instances\n    if (window.__notificationServiceInstance) {\n      return window.__notificationServiceInstance;\n    }\n\n    this.eventSource = null;\n    this.listeners = new Map();\n    this.isConnected = false;\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n    this.reconnectDelay = 1000; // Start with 1 second\n    this.maxReconnectDelay = 30000; // Max 30 seconds\n    this.baseURL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api';\n    this.connectionRefs = 0; // Track how many components are using the connection\n    this.currentUserType = null; // Track current connection type\n    this.isConnecting = false; // Prevent multiple simultaneous connection attempts\n    this.pollingInterval = null; // For polling-based notifications\n\n    // Store global reference\n    window.__notificationServiceInstance = this;\n  }\n\n  /**\n   * Initialize connection (simplified)\n   */\n  init(userType = 'admin') {\n    console.log('🚀 Initializing notification service');\n    if (!this.eventSource) {\n      this.connect(userType);\n    }\n    return Promise.resolve();\n  }\n\n  /**\n   * Cleanup (simplified)\n   */\n  cleanup() {\n    console.log('🧹 Notification service cleanup');\n    // Don't disconnect - let connection persist\n  }\n\n  /**\n   * Connect using PROPER SSE (Google/Mozilla Standard)\n   */\n  connect(userType = 'admin') {\n    // Clean up any existing connections\n    this.disconnect();\n\n    // Prevent multiple calls\n    if (this.isConnected || this.isConnecting) {\n      console.log('🔗 Already connected or connecting');\n      return Promise.resolve();\n    }\n\n    this.isConnecting = true;\n    console.log('🔗 Establishing SSE connection (Google standard)');\n\n    return new Promise((resolve, reject) => {\n      try {\n        const token = userType === 'admin'\n          ? localStorage.getItem('adminToken')\n          : localStorage.getItem('clientToken');\n\n        if (!token) {\n          this.isConnecting = false;\n          reject(new Error('No authentication token'));\n          return;\n        }\n\n        // Create EventSource with proper URL\n        const url = `${this.baseURL}/notifications/stream?token=${encodeURIComponent(token)}`;\n        console.log('🔗 SSE URL:', url.replace(/token=[^&]+/, 'token=***'));\n\n        this.eventSource = new EventSource(url);\n        this.currentUserType = userType;\n\n        // Store references to prevent garbage collection\n        window.__sseConnection = this.eventSource;\n        window.__notificationEventSource = this.eventSource;\n\n        // PROPER event handlers (Google/Mozilla standard)\n        this.eventSource.onopen = (event) => {\n          console.log('✅ SSE Connection established');\n          this.isConnected = true;\n          this.isConnecting = false;\n          this.reconnectAttempts = 0;\n          this.emit('connected');\n          resolve();\n        };\n\n        this.eventSource.onmessage = (event) => {\n          try {\n            const data = JSON.parse(event.data);\n            console.log('📨 SSE Message:', data);\n            this.handleNotification(data);\n          } catch (error) {\n            console.error('SSE message parse error:', error);\n          }\n        };\n\n        // Handle specific event types\n        this.eventSource.addEventListener('connected', (event) => {\n          console.log('🎯 Connected event received');\n        });\n\n        this.eventSource.addEventListener('heartbeat', (event) => {\n          console.log('💓 Heartbeat received');\n        });\n\n        this.eventSource.onerror = (event) => {\n          console.error('❌ SSE Error:', event);\n          this.isConnected = false;\n          this.isConnecting = false;\n\n          // Handle different error states\n          if (this.eventSource.readyState === EventSource.CLOSED) {\n            console.log('🔌 SSE Connection closed by server');\n            this.emit('disconnected');\n          } else if (this.eventSource.readyState === EventSource.CONNECTING) {\n            console.log('🔄 SSE Reconnecting...');\n          }\n\n          // Auto-reconnect with exponential backoff\n          if (this.reconnectAttempts < this.maxReconnectAttempts) {\n            this.scheduleReconnect();\n          } else {\n            console.error('🚫 Max reconnection attempts reached');\n            this.emit('max_reconnect_attempts');\n            reject(new Error('Max reconnection attempts reached'));\n          }\n        };\n\n      } catch (error) {\n        console.error('Failed to create SSE connection:', error);\n        this.isConnecting = false;\n        reject(error);\n      }\n    });\n  }\n\n  /**\n   * Poll for new notifications and updates\n   */\n  async pollNotifications(userType = 'admin') {\n    try {\n      const token = userType === 'admin'\n        ? localStorage.getItem('adminToken')\n        : localStorage.getItem('clientToken');\n\n      if (!token) return;\n\n      // Get unread count\n      const response = await fetch(`${this.baseURL}/notifications/unread-count`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        const count = data.data?.count || data.count || 0;\n\n        // Only emit if count changed or every 10th poll\n        if (!this.lastCount || this.lastCount !== count || this.pollCount % 10 === 0) {\n          this.emit('notification', {\n            type: 'unread_count_update',\n            count: count,\n            timestamp: new Date().toISOString()\n          });\n          this.lastCount = count;\n        }\n\n        this.pollCount = (this.pollCount || 0) + 1;\n      }\n    } catch (error) {\n      console.error('Failed to poll notifications:', error);\n    }\n  }\n\n  /**\n   * Disconnect from SSE (Google/Mozilla standard)\n   */\n  disconnect() {\n    console.log('🔌 Disconnecting SSE connection');\n\n    this.isConnected = false;\n    this.isConnecting = false;\n\n    // Clear any reconnection timers\n    if (this.reconnectTimer) {\n      clearTimeout(this.reconnectTimer);\n      this.reconnectTimer = null;\n    }\n\n    // Close EventSource properly\n    if (this.eventSource) {\n      console.log('🔌 Closing EventSource');\n      this.eventSource.close();\n      this.eventSource = null;\n    }\n\n    // Clear global references\n    if (window.__sseConnection) {\n      delete window.__sseConnection;\n    }\n    if (window.__notificationEventSource) {\n      delete window.__notificationEventSource;\n    }\n\n    // Clean up any polling fallback\n    if (this.pollingInterval) {\n      clearInterval(this.pollingInterval);\n      this.pollingInterval = null;\n    }\n\n    console.log('✅ SSE Disconnected cleanly');\n    this.emit('disconnected');\n  }\n\n  /**\n   * Schedule reconnection attempt\n   */\n  scheduleReconnect() {\n    console.log('🚫 Auto-reconnect disabled to prevent connection loops');\n    // Disabled to prevent connection issues during debugging\n  }\n\n  /**\n   * Handle incoming notification\n   */\n  handleNotification(notification) {\n    console.log('📢 Received notification:', notification);\n    \n    // Emit to specific type listeners\n    this.emit(notification.type, notification);\n    \n    // Emit to general notification listeners\n    this.emit('notification', notification);\n    \n    // Show browser notification if permission granted\n    this.showBrowserNotification(notification);\n  }\n\n  /**\n   * Show browser notification\n   */\n  showBrowserNotification(notification) {\n    if ('Notification' in window && Notification.permission === 'granted') {\n      const options = {\n        body: notification.message,\n        icon: '/favicon.ico',\n        badge: '/favicon.ico',\n        tag: `notification-${notification.id}`,\n        requireInteraction: notification.priority === 'high' || notification.priority === 'urgent'\n      };\n\n      const browserNotification = new Notification(notification.title, options);\n      \n      browserNotification.onclick = () => {\n        window.focus();\n        this.emit('notification_click', notification);\n        browserNotification.close();\n      };\n\n      // Auto close after 5 seconds for normal priority\n      if (notification.priority !== 'high' && notification.priority !== 'urgent') {\n        setTimeout(() => {\n          browserNotification.close();\n        }, 5000);\n      }\n    }\n  }\n\n  /**\n   * Request browser notification permission\n   */\n  async requestNotificationPermission() {\n    if ('Notification' in window) {\n      const permission = await Notification.requestPermission();\n      return permission === 'granted';\n    }\n    return false;\n  }\n\n  /**\n   * Subscribe to notification events\n   */\n  on(event, callback) {\n    if (!this.listeners.has(event)) {\n      this.listeners.set(event, new Set());\n    }\n    this.listeners.get(event).add(callback);\n  }\n\n  /**\n   * Unsubscribe from notification events\n   */\n  off(event, callback) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).delete(callback);\n    }\n  }\n\n  /**\n   * Emit event to listeners\n   */\n  emit(event, data = null) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).forEach(callback => {\n        try {\n          callback(data);\n        } catch (error) {\n          console.error(`Error in notification listener for ${event}:`, error);\n        }\n      });\n    }\n  }\n\n  /**\n   * Get user notifications\n   */\n  async getNotifications(page = 1, limit = 20, unreadOnly = false) {\n    try {\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin\n        ? localStorage.getItem('adminToken')\n        : localStorage.getItem('clientToken');\n\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.get(`${this.baseURL}/notifications`, {\n        params: { page, limit, unread_only: unreadOnly },\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get notifications:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get unread notification count\n   */\n  async getUnreadCount() {\n    try {\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin\n        ? localStorage.getItem('adminToken')\n        : localStorage.getItem('clientToken');\n\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.get(`${this.baseURL}/notifications/unread-count`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data.data.count;\n    } catch (error) {\n      console.error('Failed to get unread count:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Mark notification as read\n   */\n  async markAsRead(notificationId) {\n    try {\n      // Validate notification ID\n      if (!notificationId || notificationId === 'undefined') {\n        throw new Error('Invalid notification ID provided');\n      }\n\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin\n        ? localStorage.getItem('adminToken')\n        : localStorage.getItem('clientToken');\n\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      console.log('Marking notification as read:', notificationId);\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.put(`${this.baseURL}/notifications/${notificationId}/read`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to mark notification as read:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Mark all notifications as read\n   */\n  async markAllAsRead() {\n    try {\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin\n        ? localStorage.getItem('adminToken')\n        : localStorage.getItem('clientToken');\n\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.put(`${this.baseURL}/notifications/mark-all-read`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to mark all notifications as read:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Send test notification (admin only)\n   */\n  async sendTestNotification(data) {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.post(`${this.baseURL}/notifications/test`, data, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to send test notification:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get notification statistics (admin only)\n   */\n  async getStatistics() {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.get(`${this.baseURL}/notifications/statistics`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get notification statistics:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Test SSE connection (for debugging)\n   */\n  async testConnection() {\n    console.log('🧪 Testing SSE connection...');\n\n    try {\n      // Clear any existing connection\n      if (this.eventSource) {\n        console.log('🧪 Clearing existing connection');\n        this.eventSource.close();\n        this.eventSource = null;\n        this.isConnected = false;\n        this.isConnecting = false;\n      }\n\n      // Reset state\n      this.connectionRefs = 1;\n\n      // Test connection\n      await this.connect('admin');\n\n      console.log('🧪 Test connection established');\n\n      // Keep connection alive for 10 seconds\n      setTimeout(() => {\n        console.log('🧪 Test completed, keeping connection');\n      }, 10000);\n\n    } catch (error) {\n      console.error('🧪 Test connection failed:', error);\n    }\n  }\n\n  /**\n   * Clean up old notifications (admin only)\n   */\n  async cleanupOldNotifications(days = 90) {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.delete(`${this.baseURL}/notifications/cleanup`, {\n        params: { days },\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to cleanup old notifications:', error);\n      throw error;\n    }\n  }\n}\n\n// Create singleton instance\nconst notificationService = new NotificationService();\n\n// Make available globally for debugging\nwindow.__notificationService = notificationService;\n\n// Add global test function\nwindow.testNotifications = () => {\n  console.log('🧪 Testing notification system manually...');\n  notificationService.connect('admin');\n};\n\nwindow.stopNotifications = () => {\n  console.log('🛑 EMERGENCY STOP - Clearing all polling intervals...');\n\n  // Clear all possible intervals\n  if (notificationService.pollingInterval) {\n    clearInterval(notificationService.pollingInterval);\n    notificationService.pollingInterval = null;\n  }\n\n  // Clear any orphaned intervals\n  for (let i = 1; i < 10000; i++) {\n    clearInterval(i);\n  }\n\n  notificationService.isConnected = false;\n  console.log('✅ All polling stopped');\n};\n\nexport default notificationService;\n"], "mappings": ";;;;;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,mBAAmB,CAAC;EACxBC,WAAWA,CAAA,EAAG;IACZ;IACA,IAAIC,MAAM,CAACC,6BAA6B,EAAE;MACxC,OAAOD,MAAM,CAACC,6BAA6B;IAC7C;IAEA,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACC,cAAc,GAAG,IAAI,CAAC,CAAC;IAC5B,IAAI,CAACC,iBAAiB,GAAG,KAAK,CAAC,CAAC;IAChC,IAAI,CAACC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,eAAe,IAAI,2BAA2B;IACzE,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC,CAAC;IACzB,IAAI,CAACC,eAAe,GAAG,IAAI,CAAC,CAAC;IAC7B,IAAI,CAACC,YAAY,GAAG,KAAK,CAAC,CAAC;IAC3B,IAAI,CAACC,eAAe,GAAG,IAAI,CAAC,CAAC;;IAE7B;IACAjB,MAAM,CAACC,6BAA6B,GAAG,IAAI;EAC7C;;EAEA;AACF;AACA;EACEiB,IAAIA,CAACC,QAAQ,GAAG,OAAO,EAAE;IACvBC,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnD,IAAI,CAAC,IAAI,CAACnB,WAAW,EAAE;MACrB,IAAI,CAACoB,OAAO,CAACH,QAAQ,CAAC;IACxB;IACA,OAAOI,OAAO,CAACC,OAAO,CAAC,CAAC;EAC1B;;EAEA;AACF;AACA;EACEC,OAAOA,CAAA,EAAG;IACRL,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C;EACF;;EAEA;AACF;AACA;EACEC,OAAOA,CAACH,QAAQ,GAAG,OAAO,EAAE;IAC1B;IACA,IAAI,CAACO,UAAU,CAAC,CAAC;;IAEjB;IACA,IAAI,IAAI,CAACrB,WAAW,IAAI,IAAI,CAACW,YAAY,EAAE;MACzCI,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjD,OAAOE,OAAO,CAACC,OAAO,CAAC,CAAC;IAC1B;IAEA,IAAI,CAACR,YAAY,GAAG,IAAI;IACxBI,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;IAE/D,OAAO,IAAIE,OAAO,CAAC,CAACC,OAAO,EAAEG,MAAM,KAAK;MACtC,IAAI;QACF,MAAMC,KAAK,GAAGT,QAAQ,KAAK,OAAO,GAC9BU,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAClCD,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;QAEvC,IAAI,CAACF,KAAK,EAAE;UACV,IAAI,CAACZ,YAAY,GAAG,KAAK;UACzBW,MAAM,CAAC,IAAII,KAAK,CAAC,yBAAyB,CAAC,CAAC;UAC5C;QACF;;QAEA;QACA,MAAMC,GAAG,GAAG,GAAG,IAAI,CAACtB,OAAO,+BAA+BuB,kBAAkB,CAACL,KAAK,CAAC,EAAE;QACrFR,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEW,GAAG,CAACE,OAAO,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QAEnE,IAAI,CAAChC,WAAW,GAAG,IAAIiC,WAAW,CAACH,GAAG,CAAC;QACvC,IAAI,CAACjB,eAAe,GAAGI,QAAQ;;QAE/B;QACAnB,MAAM,CAACoC,eAAe,GAAG,IAAI,CAAClC,WAAW;QACzCF,MAAM,CAACqC,yBAAyB,GAAG,IAAI,CAACnC,WAAW;;QAEnD;QACA,IAAI,CAACA,WAAW,CAACoC,MAAM,GAAIC,KAAK,IAAK;UACnCnB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC3C,IAAI,CAAChB,WAAW,GAAG,IAAI;UACvB,IAAI,CAACW,YAAY,GAAG,KAAK;UACzB,IAAI,CAACV,iBAAiB,GAAG,CAAC;UAC1B,IAAI,CAACkC,IAAI,CAAC,WAAW,CAAC;UACtBhB,OAAO,CAAC,CAAC;QACX,CAAC;QAED,IAAI,CAACtB,WAAW,CAACuC,SAAS,GAAIF,KAAK,IAAK;UACtC,IAAI;YACF,MAAMG,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,KAAK,CAACG,IAAI,CAAC;YACnCtB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEqB,IAAI,CAAC;YACpC,IAAI,CAACG,kBAAkB,CAACH,IAAI,CAAC;UAC/B,CAAC,CAAC,OAAOI,KAAK,EAAE;YACd1B,OAAO,CAAC0B,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAClD;QACF,CAAC;;QAED;QACA,IAAI,CAAC5C,WAAW,CAAC6C,gBAAgB,CAAC,WAAW,EAAGR,KAAK,IAAK;UACxDnB,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC5C,CAAC,CAAC;QAEF,IAAI,CAACnB,WAAW,CAAC6C,gBAAgB,CAAC,WAAW,EAAGR,KAAK,IAAK;UACxDnB,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;QACtC,CAAC,CAAC;QAEF,IAAI,CAACnB,WAAW,CAAC8C,OAAO,GAAIT,KAAK,IAAK;UACpCnB,OAAO,CAAC0B,KAAK,CAAC,cAAc,EAAEP,KAAK,CAAC;UACpC,IAAI,CAAClC,WAAW,GAAG,KAAK;UACxB,IAAI,CAACW,YAAY,GAAG,KAAK;;UAEzB;UACA,IAAI,IAAI,CAACd,WAAW,CAAC+C,UAAU,KAAKd,WAAW,CAACe,MAAM,EAAE;YACtD9B,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;YACjD,IAAI,CAACmB,IAAI,CAAC,cAAc,CAAC;UAC3B,CAAC,MAAM,IAAI,IAAI,CAACtC,WAAW,CAAC+C,UAAU,KAAKd,WAAW,CAACgB,UAAU,EAAE;YACjE/B,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;UACvC;;UAEA;UACA,IAAI,IAAI,CAACf,iBAAiB,GAAG,IAAI,CAACC,oBAAoB,EAAE;YACtD,IAAI,CAAC6C,iBAAiB,CAAC,CAAC;UAC1B,CAAC,MAAM;YACLhC,OAAO,CAAC0B,KAAK,CAAC,sCAAsC,CAAC;YACrD,IAAI,CAACN,IAAI,CAAC,wBAAwB,CAAC;YACnCb,MAAM,CAAC,IAAII,KAAK,CAAC,mCAAmC,CAAC,CAAC;UACxD;QACF,CAAC;MAEH,CAAC,CAAC,OAAOe,KAAK,EAAE;QACd1B,OAAO,CAAC0B,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAAC9B,YAAY,GAAG,KAAK;QACzBW,MAAM,CAACmB,KAAK,CAAC;MACf;IACF,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACE,MAAMO,iBAAiBA,CAAClC,QAAQ,GAAG,OAAO,EAAE;IAC1C,IAAI;MACF,MAAMS,KAAK,GAAGT,QAAQ,KAAK,OAAO,GAC9BU,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAClCD,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MAEvC,IAAI,CAACF,KAAK,EAAE;;MAEZ;MACA,MAAM0B,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAAC7C,OAAO,6BAA6B,EAAE;QACzE8C,OAAO,EAAE;UACP,eAAe,EAAE,UAAU5B,KAAK;QAClC;MACF,CAAC,CAAC;MAEF,IAAI0B,QAAQ,CAACG,EAAE,EAAE;QACf,MAAMf,IAAI,GAAG,MAAMY,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClC,MAAMC,KAAK,GAAGjB,IAAI,CAACA,IAAI,EAAEiB,KAAK,IAAIjB,IAAI,CAACiB,KAAK,IAAI,CAAC;;QAEjD;QACA,IAAI,CAAC,IAAI,CAACC,SAAS,IAAI,IAAI,CAACA,SAAS,KAAKD,KAAK,IAAI,IAAI,CAACE,SAAS,GAAG,EAAE,KAAK,CAAC,EAAE;UAC5E,IAAI,CAACrB,IAAI,CAAC,cAAc,EAAE;YACxBsB,IAAI,EAAE,qBAAqB;YAC3BH,KAAK,EAAEA,KAAK;YACZI,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;UACpC,CAAC,CAAC;UACF,IAAI,CAACL,SAAS,GAAGD,KAAK;QACxB;QAEA,IAAI,CAACE,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS,IAAI,CAAC,IAAI,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOf,KAAK,EAAE;MACd1B,OAAO,CAAC0B,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF;;EAEA;AACF;AACA;EACEpB,UAAUA,CAAA,EAAG;IACXN,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C,IAAI,CAAChB,WAAW,GAAG,KAAK;IACxB,IAAI,CAACW,YAAY,GAAG,KAAK;;IAEzB;IACA,IAAI,IAAI,CAACkD,cAAc,EAAE;MACvBC,YAAY,CAAC,IAAI,CAACD,cAAc,CAAC;MACjC,IAAI,CAACA,cAAc,GAAG,IAAI;IAC5B;;IAEA;IACA,IAAI,IAAI,CAAChE,WAAW,EAAE;MACpBkB,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC,IAAI,CAACnB,WAAW,CAACkE,KAAK,CAAC,CAAC;MACxB,IAAI,CAAClE,WAAW,GAAG,IAAI;IACzB;;IAEA;IACA,IAAIF,MAAM,CAACoC,eAAe,EAAE;MAC1B,OAAOpC,MAAM,CAACoC,eAAe;IAC/B;IACA,IAAIpC,MAAM,CAACqC,yBAAyB,EAAE;MACpC,OAAOrC,MAAM,CAACqC,yBAAyB;IACzC;;IAEA;IACA,IAAI,IAAI,CAACpB,eAAe,EAAE;MACxBoD,aAAa,CAAC,IAAI,CAACpD,eAAe,CAAC;MACnC,IAAI,CAACA,eAAe,GAAG,IAAI;IAC7B;IAEAG,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzC,IAAI,CAACmB,IAAI,CAAC,cAAc,CAAC;EAC3B;;EAEA;AACF;AACA;EACEY,iBAAiBA,CAAA,EAAG;IAClBhC,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;IACrE;EACF;;EAEA;AACF;AACA;EACEwB,kBAAkBA,CAACyB,YAAY,EAAE;IAC/BlD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEiD,YAAY,CAAC;;IAEtD;IACA,IAAI,CAAC9B,IAAI,CAAC8B,YAAY,CAACR,IAAI,EAAEQ,YAAY,CAAC;;IAE1C;IACA,IAAI,CAAC9B,IAAI,CAAC,cAAc,EAAE8B,YAAY,CAAC;;IAEvC;IACA,IAAI,CAACC,uBAAuB,CAACD,YAAY,CAAC;EAC5C;;EAEA;AACF;AACA;EACEC,uBAAuBA,CAACD,YAAY,EAAE;IACpC,IAAI,cAAc,IAAItE,MAAM,IAAIwE,YAAY,CAACC,UAAU,KAAK,SAAS,EAAE;MACrE,MAAMC,OAAO,GAAG;QACdC,IAAI,EAAEL,YAAY,CAACM,OAAO;QAC1BC,IAAI,EAAE,cAAc;QACpBC,KAAK,EAAE,cAAc;QACrBC,GAAG,EAAE,gBAAgBT,YAAY,CAACU,EAAE,EAAE;QACtCC,kBAAkB,EAAEX,YAAY,CAACY,QAAQ,KAAK,MAAM,IAAIZ,YAAY,CAACY,QAAQ,KAAK;MACpF,CAAC;MAED,MAAMC,mBAAmB,GAAG,IAAIX,YAAY,CAACF,YAAY,CAACc,KAAK,EAAEV,OAAO,CAAC;MAEzES,mBAAmB,CAACE,OAAO,GAAG,MAAM;QAClCrF,MAAM,CAACsF,KAAK,CAAC,CAAC;QACd,IAAI,CAAC9C,IAAI,CAAC,oBAAoB,EAAE8B,YAAY,CAAC;QAC7Ca,mBAAmB,CAACf,KAAK,CAAC,CAAC;MAC7B,CAAC;;MAED;MACA,IAAIE,YAAY,CAACY,QAAQ,KAAK,MAAM,IAAIZ,YAAY,CAACY,QAAQ,KAAK,QAAQ,EAAE;QAC1EK,UAAU,CAAC,MAAM;UACfJ,mBAAmB,CAACf,KAAK,CAAC,CAAC;QAC7B,CAAC,EAAE,IAAI,CAAC;MACV;IACF;EACF;;EAEA;AACF;AACA;EACE,MAAMoB,6BAA6BA,CAAA,EAAG;IACpC,IAAI,cAAc,IAAIxF,MAAM,EAAE;MAC5B,MAAMyE,UAAU,GAAG,MAAMD,YAAY,CAACiB,iBAAiB,CAAC,CAAC;MACzD,OAAOhB,UAAU,KAAK,SAAS;IACjC;IACA,OAAO,KAAK;EACd;;EAEA;AACF;AACA;EACEiB,EAAEA,CAACnD,KAAK,EAAEoD,QAAQ,EAAE;IAClB,IAAI,CAAC,IAAI,CAACxF,SAAS,CAACyF,GAAG,CAACrD,KAAK,CAAC,EAAE;MAC9B,IAAI,CAACpC,SAAS,CAAC0F,GAAG,CAACtD,KAAK,EAAE,IAAIuD,GAAG,CAAC,CAAC,CAAC;IACtC;IACA,IAAI,CAAC3F,SAAS,CAAC4F,GAAG,CAACxD,KAAK,CAAC,CAACyD,GAAG,CAACL,QAAQ,CAAC;EACzC;;EAEA;AACF;AACA;EACEM,GAAGA,CAAC1D,KAAK,EAAEoD,QAAQ,EAAE;IACnB,IAAI,IAAI,CAACxF,SAAS,CAACyF,GAAG,CAACrD,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACpC,SAAS,CAAC4F,GAAG,CAACxD,KAAK,CAAC,CAAC2D,MAAM,CAACP,QAAQ,CAAC;IAC5C;EACF;;EAEA;AACF;AACA;EACEnD,IAAIA,CAACD,KAAK,EAAEG,IAAI,GAAG,IAAI,EAAE;IACvB,IAAI,IAAI,CAACvC,SAAS,CAACyF,GAAG,CAACrD,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACpC,SAAS,CAAC4F,GAAG,CAACxD,KAAK,CAAC,CAAC4D,OAAO,CAACR,QAAQ,IAAI;QAC5C,IAAI;UACFA,QAAQ,CAACjD,IAAI,CAAC;QAChB,CAAC,CAAC,OAAOI,KAAK,EAAE;UACd1B,OAAO,CAAC0B,KAAK,CAAC,sCAAsCP,KAAK,GAAG,EAAEO,KAAK,CAAC;QACtE;MACF,CAAC,CAAC;IACJ;EACF;;EAEA;AACF;AACA;EACE,MAAMsD,gBAAgBA,CAACC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,EAAE,EAAEC,UAAU,GAAG,KAAK,EAAE;IAC/D,IAAI;MACF,MAAMC,OAAO,GAAG,CAAC,CAAC3E,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MACpD,MAAMF,KAAK,GAAG4E,OAAO,GACjB3E,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAClCD,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MAEvC,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;;MAEA;MACA,MAAMuB,QAAQ,GAAG,MAAMzD,KAAK,CAACkG,GAAG,CAAC,GAAG,IAAI,CAACrF,OAAO,gBAAgB,EAAE;QAChE+F,MAAM,EAAE;UAAEJ,IAAI;UAAEC,KAAK;UAAEI,WAAW,EAAEH;QAAW,CAAC;QAChD/C,OAAO,EAAE;UACP,eAAe,EAAE,UAAU5B,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAO0B,QAAQ,CAACZ,IAAI;IACtB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd1B,OAAO,CAAC0B,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM6D,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAMH,OAAO,GAAG,CAAC,CAAC3E,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MACpD,MAAMF,KAAK,GAAG4E,OAAO,GACjB3E,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAClCD,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MAEvC,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;;MAEA;MACA,MAAMuB,QAAQ,GAAG,MAAMzD,KAAK,CAACkG,GAAG,CAAC,GAAG,IAAI,CAACrF,OAAO,6BAA6B,EAAE;QAC7E8C,OAAO,EAAE;UACP,eAAe,EAAE,UAAU5B,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAO0B,QAAQ,CAACZ,IAAI,CAACA,IAAI,CAACiB,KAAK;IACjC,CAAC,CAAC,OAAOb,KAAK,EAAE;MACd1B,OAAO,CAAC0B,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM8D,UAAUA,CAACC,cAAc,EAAE;IAC/B,IAAI;MACF;MACA,IAAI,CAACA,cAAc,IAAIA,cAAc,KAAK,WAAW,EAAE;QACrD,MAAM,IAAI9E,KAAK,CAAC,kCAAkC,CAAC;MACrD;MAEA,MAAMyE,OAAO,GAAG,CAAC,CAAC3E,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MACpD,MAAMF,KAAK,GAAG4E,OAAO,GACjB3E,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAClCD,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MAEvC,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEAX,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEwF,cAAc,CAAC;;MAE5D;MACA,MAAMvD,QAAQ,GAAG,MAAMzD,KAAK,CAACiH,GAAG,CAAC,GAAG,IAAI,CAACpG,OAAO,kBAAkBmG,cAAc,OAAO,EAAE,CAAC,CAAC,EAAE;QAC3FrD,OAAO,EAAE;UACP,eAAe,EAAE,UAAU5B,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAO0B,QAAQ,CAACZ,IAAI;IACtB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd1B,OAAO,CAAC0B,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMiE,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMP,OAAO,GAAG,CAAC,CAAC3E,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MACpD,MAAMF,KAAK,GAAG4E,OAAO,GACjB3E,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAClCD,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MAEvC,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIG,KAAK,CAAC,+BAA+B,CAAC;MAClD;;MAEA;MACA,MAAMuB,QAAQ,GAAG,MAAMzD,KAAK,CAACiH,GAAG,CAAC,GAAG,IAAI,CAACpG,OAAO,8BAA8B,EAAE,CAAC,CAAC,EAAE;QAClF8C,OAAO,EAAE;UACP,eAAe,EAAE,UAAU5B,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAO0B,QAAQ,CAACZ,IAAI;IACtB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd1B,OAAO,CAAC0B,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMkE,oBAAoBA,CAACtE,IAAI,EAAE;IAC/B,IAAI;MACF,MAAMd,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,MAAMwB,QAAQ,GAAG,MAAMzD,KAAK,CAACoH,IAAI,CAAC,GAAG,IAAI,CAACvG,OAAO,qBAAqB,EAAEgC,IAAI,EAAE;QAC5Ec,OAAO,EAAE;UACP,eAAe,EAAE,UAAU5B,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAO0B,QAAQ,CAACZ,IAAI;IACtB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd1B,OAAO,CAAC0B,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMoE,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMtF,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,MAAMwB,QAAQ,GAAG,MAAMzD,KAAK,CAACkG,GAAG,CAAC,GAAG,IAAI,CAACrF,OAAO,2BAA2B,EAAE;QAC3E8C,OAAO,EAAE;UACP,eAAe,EAAE,UAAU5B,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAO0B,QAAQ,CAACZ,IAAI;IACtB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd1B,OAAO,CAAC0B,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMqE,cAAcA,CAAA,EAAG;IACrB/F,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAE3C,IAAI;MACF;MACA,IAAI,IAAI,CAACnB,WAAW,EAAE;QACpBkB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,IAAI,CAACnB,WAAW,CAACkE,KAAK,CAAC,CAAC;QACxB,IAAI,CAAClE,WAAW,GAAG,IAAI;QACvB,IAAI,CAACG,WAAW,GAAG,KAAK;QACxB,IAAI,CAACW,YAAY,GAAG,KAAK;MAC3B;;MAEA;MACA,IAAI,CAACF,cAAc,GAAG,CAAC;;MAEvB;MACA,MAAM,IAAI,CAACQ,OAAO,CAAC,OAAO,CAAC;MAE3BF,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACAkE,UAAU,CAAC,MAAM;QACfnE,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACtD,CAAC,EAAE,KAAK,CAAC;IAEX,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACd1B,OAAO,CAAC0B,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF;;EAEA;AACF;AACA;EACE,MAAMsE,uBAAuBA,CAACC,IAAI,GAAG,EAAE,EAAE;IACvC,IAAI;MACF,MAAMzF,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,MAAMwB,QAAQ,GAAG,MAAMzD,KAAK,CAACqG,MAAM,CAAC,GAAG,IAAI,CAACxF,OAAO,wBAAwB,EAAE;QAC3E+F,MAAM,EAAE;UAAEY;QAAK,CAAC;QAChB7D,OAAO,EAAE;UACP,eAAe,EAAE,UAAU5B,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAO0B,QAAQ,CAACZ,IAAI;IACtB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd1B,OAAO,CAAC0B,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb;EACF;AACF;;AAEA;AACA,MAAMwE,mBAAmB,GAAG,IAAIxH,mBAAmB,CAAC,CAAC;;AAErD;AACAE,MAAM,CAACuH,qBAAqB,GAAGD,mBAAmB;;AAElD;AACAtH,MAAM,CAACwH,iBAAiB,GAAG,MAAM;EAC/BpG,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;EACzDiG,mBAAmB,CAAChG,OAAO,CAAC,OAAO,CAAC;AACtC,CAAC;AAEDtB,MAAM,CAACyH,iBAAiB,GAAG,MAAM;EAC/BrG,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;;EAEpE;EACA,IAAIiG,mBAAmB,CAACrG,eAAe,EAAE;IACvCoD,aAAa,CAACiD,mBAAmB,CAACrG,eAAe,CAAC;IAClDqG,mBAAmB,CAACrG,eAAe,GAAG,IAAI;EAC5C;;EAEA;EACA,KAAK,IAAIyG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,KAAK,EAAEA,CAAC,EAAE,EAAE;IAC9BrD,aAAa,CAACqD,CAAC,CAAC;EAClB;EAEAJ,mBAAmB,CAACjH,WAAW,GAAG,KAAK;EACvCe,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;AACtC,CAAC;AAED,eAAeiG,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}