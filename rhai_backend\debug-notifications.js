const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function debugNotifications() {
  console.log('🔍 Debugging Notification System...\n');

  try {
    // Login as admin
    const adminLoginResponse = await axios.post(`${BASE_URL}/admin/auth/login`, {
      username: 'admin12345',
      password: 'admin123'
    });

    if (!adminLoginResponse.data.success) {
      throw new Error('Admin login failed');
    }

    const adminToken = adminLoginResponse.data.data.token;
    console.log('✅ Admin logged in successfully');

    // Get notifications and check structure
    console.log('\n📋 Checking notification response structure...');
    
    const notificationsResponse = await axios.get(`${BASE_URL}/notifications?limit=5`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    
    console.log('📊 Full response structure:');
    console.log(JSON.stringify(notificationsResponse.data, null, 2));
    
    if (notificationsResponse.data.data && notificationsResponse.data.data.length > 0) {
      console.log('\n📝 Sample notification structure:');
      console.log(JSON.stringify(notificationsResponse.data.data[0], null, 2));
      
      const sampleNotification = notificationsResponse.data.data[0];
      console.log('\n🔑 Notification ID:', sampleNotification.id);
      console.log('📄 Notification Title:', sampleNotification.title);
      console.log('👁️ Is Read:', sampleNotification.is_read);
      
      // Test marking as read
      if (sampleNotification.id && !sampleNotification.is_read) {
        console.log('\n🔄 Testing mark as read...');
        try {
          const markReadResponse = await axios.put(`${BASE_URL}/notifications/${sampleNotification.id}/read`, {}, {
            headers: { 'Authorization': `Bearer ${adminToken}` }
          });
          console.log('✅ Mark as read successful:', markReadResponse.data.success);
        } catch (error) {
          console.log('❌ Mark as read failed:', error.response?.data || error.message);
        }
      }
    } else {
      console.log('ℹ️ No notifications found. Creating a test notification...');
      
      // Create a test notification
      const testNotificationResponse = await axios.post(`${BASE_URL}/notifications/test`, {
        user_type: 'admin',
        title: 'Debug Test Notification',
        message: 'This is a test notification for debugging',
        type: 'test',
        priority: 'normal'
      }, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      });
      
      if (testNotificationResponse.data.success) {
        console.log('✅ Test notification created');
        
        // Get notifications again
        const newNotificationsResponse = await axios.get(`${BASE_URL}/notifications?limit=1`, {
          headers: { 'Authorization': `Bearer ${adminToken}` }
        });
        
        console.log('\n📝 New notification structure:');
        console.log(JSON.stringify(newNotificationsResponse.data, null, 2));
      }
    }

    // Test unread count
    console.log('\n📊 Testing unread count...');
    const unreadCountResponse = await axios.get(`${BASE_URL}/notifications/unread-count`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    
    console.log('📈 Unread count response:');
    console.log(JSON.stringify(unreadCountResponse.data, null, 2));

  } catch (error) {
    console.error('❌ Debug failed:', error.response?.data || error.message);
  }
}

// Test client authentication and notifications
async function debugClientNotifications() {
  console.log('\n👤 Testing Client Notifications...');
  
  try {
    // Try to login as a client (this might fail if no client exists)
    console.log('🔄 Attempting client login...');
    
    // First, let's check if we can create a test client or use existing one
    // For now, we'll skip client testing since we don't have client credentials
    console.log('ℹ️ Skipping client test - no test client account available');
    console.log('💡 To test client notifications:');
    console.log('   1. Create a client account through the registration process');
    console.log('   2. Login as client and test the notification bell');
    console.log('   3. Send notifications to client user type');
    
  } catch (error) {
    console.log('ℹ️ Client testing skipped:', error.message);
  }
}

async function runDebug() {
  await debugNotifications();
  await debugClientNotifications();
  
  console.log('\n🎯 Debug Summary:');
  console.log('- Check the notification response structure above');
  console.log('- Verify that notification objects have valid IDs');
  console.log('- Test the mark as read functionality');
  console.log('- Frontend should handle the response structure correctly');
}

runDebug();
