const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';
let adminToken = '';

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

function logTest(name, success, message = '') {
  const status = success ? '✅ PASS' : '❌ FAIL';
  console.log(`${status} ${name}${message ? ': ' + message : ''}`);
  
  testResults.tests.push({ name, success, message });
  if (success) testResults.passed++;
  else testResults.failed++;
}

async function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function testAdminLogin() {
  console.log('\n🔐 Testing Admin Authentication...');
  
  try {
    const response = await axios.post(`${BASE_URL}/admin/auth/login`, {
      username: 'admin12345',
      password: '12345QWERTqwert'
    });
    
    if (response.data.success && response.data.data.token) {
      adminToken = response.data.data.token;
      logTest('Admin Login', true, 'Token received');
      return true;
    } else {
      logTest('Admin Login', false, 'No token in response');
      return false;
    }
  } catch (error) {
    logTest('Admin Login', false, error.response?.data?.message || error.message);
    return false;
  }
}

async function testDashboardStats() {
  console.log('\n📊 Testing Dashboard APIs...');
  
  try {
    const response = await axios.get(`${BASE_URL}/admin/documents/dashboard/stats`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    if (response.data.success && response.data.data) {
      logTest('Dashboard Stats', true, `Found ${response.data.data.overview.total_requests} total requests`);
      return response.data.data;
    } else {
      logTest('Dashboard Stats', false, 'Invalid response structure');
      return null;
    }
  } catch (error) {
    logTest('Dashboard Stats', false, error.response?.data?.message || error.message);
    return null;
  }
}

async function testGetRequests() {
  console.log('\n📋 Testing Request Management APIs...');
  
  try {
    const response = await axios.get(`${BASE_URL}/admin/documents/requests?page=1&limit=5`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    if (response.data.success && response.data.data.requests) {
      const requests = response.data.data.requests;
      logTest('Get Requests', true, `Found ${requests.length} requests`);
      return requests;
    } else {
      logTest('Get Requests', false, 'Invalid response structure');
      return [];
    }
  } catch (error) {
    logTest('Get Requests', false, error.response?.data?.message || error.message);
    return [];
  }
}

async function testApprovalWorkflow(requests) {
  console.log('\n✅ Testing Approval/Rejection Workflow...');
  
  // Find a pending request to test with
  const pendingRequest = requests.find(r => r.status_name === 'pending');
  
  if (!pendingRequest) {
    logTest('Approval Workflow', false, 'No pending requests found for testing');
    return;
  }
  
  console.log(`Testing with request ${pendingRequest.request_number} (ID: ${pendingRequest.id})`);
  
  // Test approval
  try {
    await delay(1000); // Avoid rate limiting
    
    const approveResponse = await axios.post(
      `${BASE_URL}/admin/documents/requests/${pendingRequest.id}/approve`,
      { reason: 'API test approval' },
      { headers: { Authorization: `Bearer ${adminToken}` } }
    );
    
    if (approveResponse.data.success) {
      logTest('Request Approval', true, `Request ${pendingRequest.request_number} approved`);
      
      // Verify status change
      await delay(1000);
      const verifyResponse = await axios.get(
        `${BASE_URL}/admin/documents/requests/${pendingRequest.id}`,
        { headers: { Authorization: `Bearer ${adminToken}` } }
      );
      
      if (verifyResponse.data.success && verifyResponse.data.data.status_name === 'approved') {
        logTest('Status Verification', true, 'Status updated to approved');
      } else {
        logTest('Status Verification', false, 'Status not updated correctly');
      }
    } else {
      logTest('Request Approval', false, 'Approval failed');
    }
  } catch (error) {
    logTest('Request Approval', false, error.response?.data?.message || error.message);
  }
}

async function testNotificationEndpoints() {
  console.log('\n🔔 Testing Notification APIs...');
  
  try {
    await delay(1000);
    
    const response = await axios.get(`${BASE_URL}/notifications/unread-count`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    if (response.data.success) {
      logTest('Notification Count', true, `Unread count: ${response.data.data?.count || 0}`);
    } else {
      logTest('Notification Count', false, 'Invalid response');
    }
  } catch (error) {
    logTest('Notification Count', false, error.response?.data?.message || error.message);
  }
  
  // Test SSE endpoint (just check if it connects)
  try {
    await delay(1000);
    
    const sseResponse = await axios.get(
      `${BASE_URL}/notifications/stream?token=${adminToken}`,
      { 
        timeout: 3000,
        responseType: 'stream'
      }
    );
    
    if (sseResponse.status === 200) {
      logTest('SSE Connection', true, 'SSE endpoint accessible');
    } else {
      logTest('SSE Connection', false, 'SSE endpoint not accessible');
    }
  } catch (error) {
    if (error.code === 'ECONNABORTED') {
      logTest('SSE Connection', true, 'SSE connection established (timeout expected)');
    } else {
      logTest('SSE Connection', false, error.response?.data?.message || error.message);
    }
  }
}

async function testClientEndpoints() {
  console.log('\n👤 Testing Client APIs...');
  
  // Test client registration
  try {
    const clientData = {
      first_name: 'Test',
      last_name: 'Client',
      email: `test.client.${Date.now()}@example.com`,
      phone: '09123456789',
      password: 'TestPassword123!',
      address: 'Test Address'
    };
    
    const response = await axios.post(`${BASE_URL}/client/auth/register`, clientData);
    
    if (response.data.success) {
      logTest('Client Registration', true, 'Client registered successfully');
    } else {
      logTest('Client Registration', false, 'Registration failed');
    }
  } catch (error) {
    logTest('Client Registration', false, error.response?.data?.message || error.message);
  }
}

async function runComprehensiveTest() {
  console.log('🚀 Starting Comprehensive API Backend Testing...');
  console.log('=' .repeat(60));
  
  // Test admin authentication first
  const loginSuccess = await testAdminLogin();
  if (!loginSuccess) {
    console.log('\n❌ Cannot proceed without admin authentication');
    return;
  }
  
  // Test dashboard APIs
  const dashboardData = await testDashboardStats();
  
  // Test request management
  const requests = await testGetRequests();
  
  // Test approval workflow
  if (requests.length > 0) {
    await testApprovalWorkflow(requests);
  }
  
  // Test notification system
  await testNotificationEndpoints();
  
  // Test client endpoints
  await testClientEndpoints();
  
  // Print summary
  console.log('\n' + '=' .repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log('=' .repeat(60));
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📊 Total: ${testResults.tests.length}`);
  console.log(`🎯 Success Rate: ${((testResults.passed / testResults.tests.length) * 100).toFixed(1)}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.tests
      .filter(t => !t.success)
      .forEach(t => console.log(`   - ${t.name}: ${t.message}`));
  }
  
  console.log('\n🏁 Testing completed!');
}

// Run the tests
runComprehensiveTest().catch(console.error);
