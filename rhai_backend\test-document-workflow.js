const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';
let adminToken = '';
let clientToken = '';

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

function logTest(name, success, message = '') {
  const status = success ? '✅ PASS' : '❌ FAIL';
  console.log(`${status} ${name}${message ? ': ' + message : ''}`);
  
  testResults.tests.push({ name, success, message });
  if (success) testResults.passed++;
  else testResults.failed++;
}

async function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function loginAdmin() {
  console.log('\n🔐 Admin Login...');
  
  try {
    const response = await axios.post(`${BASE_URL}/admin/auth/login`, {
      username: 'admin12345',
      password: '12345QWERTqwert'
    });
    
    if (response.data.success && response.data.data.token) {
      adminToken = response.data.data.token;
      logTest('Admin Login', true);
      return true;
    } else {
      logTest('Admin Login', false, 'No token received');
      return false;
    }
  } catch (error) {
    logTest('Admin Login', false, error.response?.data?.message || error.message);
    return false;
  }
}

async function loginClient() {
  console.log('\n👤 Client Login...');
  
  try {
    // Try to login with test client credentials
    const response = await axios.post(`${BASE_URL}/client/auth/login`, {
      username: 'testapi',  // Test client we just created
      password: 'testpass123'
    });
    
    if (response.data.success && response.data.data.token) {
      clientToken = response.data.data.token;
      logTest('Client Login', true);
      return true;
    } else {
      logTest('Client Login', false, 'No token received');
      return false;
    }
  } catch (error) {
    logTest('Client Login', false, error.response?.data?.message || error.message);
    return false;
  }
}

async function testGetPendingRequests() {
  console.log('\n📋 Testing Document Request Management...');
  
  try {
    const response = await axios.get(`${BASE_URL}/admin/documents/requests?status=1&limit=10`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    if (response.data.success && response.data.data.requests) {
      const pendingRequests = response.data.data.requests;
      logTest('Get Pending Requests', true, `Found ${pendingRequests.length} pending requests`);
      return pendingRequests;
    } else {
      logTest('Get Pending Requests', false, 'Invalid response structure');
      return [];
    }
  } catch (error) {
    logTest('Get Pending Requests', false, error.response?.data?.message || error.message);
    return [];
  }
}

async function testApprovalWorkflow(requests) {
  console.log('\n✅ Testing Approval Workflow...');
  
  const pendingRequest = requests.find(r => r.status_name === 'pending');
  
  if (!pendingRequest) {
    logTest('Approval Workflow', false, 'No pending requests available for testing');
    return null;
  }
  
  console.log(`Testing approval with request ${pendingRequest.request_number} (ID: ${pendingRequest.id})`);
  
  try {
    // Step 1: Move to under_review (required workflow)
    await delay(1000);
    const reviewResponse = await axios.put(
      `${BASE_URL}/admin/documents/requests/${pendingRequest.id}/status`,
      { status_id: 2, reason: 'Moving to review for approval test' },
      { headers: { Authorization: `Bearer ${adminToken}` } }
    );
    
    if (reviewResponse.data.success) {
      logTest('Move to Under Review', true);
      
      // Step 2: Approve the request
      await delay(1000);
      const approveResponse = await axios.post(
        `${BASE_URL}/admin/documents/requests/${pendingRequest.id}/approve`,
        { reason: 'Approved for testing purposes' },
        { headers: { Authorization: `Bearer ${adminToken}` } }
      );
      
      if (approveResponse.data.success) {
        logTest('Request Approval', true, `Request ${pendingRequest.request_number} approved`);
        
        // Step 3: Verify status change
        await delay(1000);
        const verifyResponse = await axios.get(
          `${BASE_URL}/admin/documents/requests/${pendingRequest.id}`,
          { headers: { Authorization: `Bearer ${adminToken}` } }
        );
        
        if (verifyResponse.data.success && verifyResponse.data.data.status_name === 'approved') {
          logTest('Status Verification', true, 'Status correctly updated to approved');
          return { ...pendingRequest, status_name: 'approved' };
        } else {
          logTest('Status Verification', false, 'Status not updated correctly');
        }
      } else {
        logTest('Request Approval', false, 'Approval failed');
      }
    } else {
      logTest('Move to Under Review', false, 'Failed to move to review');
    }
  } catch (error) {
    logTest('Approval Workflow', false, error.response?.data?.message || error.message);
  }
  
  return null;
}

async function testRejectionWorkflow(requests) {
  console.log('\n❌ Testing Rejection Workflow...');
  
  const pendingRequest = requests.find(r => r.status_name === 'pending' && r.id !== 13); // Skip the one we just approved
  
  if (!pendingRequest) {
    logTest('Rejection Workflow', false, 'No pending requests available for rejection testing');
    return;
  }
  
  console.log(`Testing rejection with request ${pendingRequest.request_number} (ID: ${pendingRequest.id})`);
  
  try {
    const rejectResponse = await axios.post(
      `${BASE_URL}/admin/documents/requests/${pendingRequest.id}/reject`,
      { reason: 'Missing required documents - test rejection' },
      { headers: { Authorization: `Bearer ${adminToken}` } }
    );
    
    if (rejectResponse.data.success) {
      logTest('Request Rejection', true, `Request ${pendingRequest.request_number} rejected`);
      
      // Verify status change
      await delay(1000);
      const verifyResponse = await axios.get(
        `${BASE_URL}/admin/documents/requests/${pendingRequest.id}`,
        { headers: { Authorization: `Bearer ${adminToken}` } }
      );
      
      if (verifyResponse.data.success && verifyResponse.data.data.status_name === 'rejected') {
        logTest('Rejection Status Verification', true, 'Status correctly updated to rejected');
      } else {
        logTest('Rejection Status Verification', false, 'Status not updated correctly');
      }
    } else {
      logTest('Request Rejection', false, 'Rejection failed');
    }
  } catch (error) {
    logTest('Rejection Workflow', false, error.response?.data?.message || error.message);
  }
}

async function testClientSideAPIs() {
  console.log('\n👤 Testing Client-Side APIs...');
  
  if (!clientToken) {
    logTest('Client APIs', false, 'No client token available');
    return;
  }
  
  try {
    // Test getting client's own requests
    const myRequestsResponse = await axios.get(`${BASE_URL}/client/document-requests`, {
      headers: { Authorization: `Bearer ${clientToken}` }
    });
    
    if (myRequestsResponse.data.success) {
      const clientRequests = myRequestsResponse.data.data.requests || myRequestsResponse.data.data || [];
      logTest('Get Client Requests', true, `Found ${clientRequests.length} client requests`);
      
      // Test getting specific request details
      if (clientRequests.length > 0) {
        const firstRequest = clientRequests[0];
        const detailResponse = await axios.get(
          `${BASE_URL}/client/document-requests/${firstRequest.id}`,
          { headers: { Authorization: `Bearer ${clientToken}` } }
        );
        
        if (detailResponse.data.success) {
          logTest('Get Request Details', true, `Retrieved details for request ${firstRequest.id}`);
        } else {
          logTest('Get Request Details', false, 'Failed to get request details');
        }
      }
    } else {
      logTest('Get Client Requests', false, 'Failed to get client requests');
    }
    
    // Test client notifications
    await delay(1000);
    const notificationsResponse = await axios.get(`${BASE_URL}/notifications/unread-count`, {
      headers: { Authorization: `Bearer ${clientToken}` }
    });
    
    if (notificationsResponse.data.success) {
      logTest('Client Notifications', true, `Unread count: ${notificationsResponse.data.data?.count || 0}`);
    } else {
      logTest('Client Notifications', false, 'Failed to get notification count');
    }
    
  } catch (error) {
    logTest('Client APIs', false, error.response?.data?.message || error.message);
  }
}

async function testNotificationSystem() {
  console.log('\n🔔 Testing Notification System...');
  
  try {
    // Test admin notifications
    const adminNotifResponse = await axios.get(`${BASE_URL}/notifications/unread-count`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    if (adminNotifResponse.data.success) {
      logTest('Admin Notifications', true, `Admin unread count: ${adminNotifResponse.data.data?.count || 0}`);
    } else {
      logTest('Admin Notifications', false, 'Failed to get admin notifications');
    }
    
    // Test SSE connection (just verify endpoint is accessible)
    await delay(1000);
    try {
      const sseResponse = await axios.get(
        `${BASE_URL}/notifications/stream?token=${adminToken}`,
        { 
          timeout: 2000,
          responseType: 'stream'
        }
      );
      
      if (sseResponse.status === 200) {
        logTest('SSE Connection', true, 'SSE endpoint accessible');
      }
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        logTest('SSE Connection', true, 'SSE connection established (timeout expected)');
      } else {
        logTest('SSE Connection', false, error.message);
      }
    }
    
  } catch (error) {
    logTest('Notification System', false, error.response?.data?.message || error.message);
  }
}

async function runDocumentWorkflowTest() {
  console.log('🚀 Starting Document Workflow & Client-Side Testing...');
  console.log('=' .repeat(70));
  
  // Login as admin
  const adminLoginSuccess = await loginAdmin();
  if (!adminLoginSuccess) {
    console.log('\n❌ Cannot proceed without admin authentication');
    return;
  }
  
  // Login as client (optional)
  await loginClient();
  
  // Get pending requests
  const pendingRequests = await testGetPendingRequests();
  
  // Test approval workflow
  if (pendingRequests.length > 0) {
    await testApprovalWorkflow(pendingRequests);
    
    // Test rejection workflow with different request
    await testRejectionWorkflow(pendingRequests);
  } else {
    console.log('\n⚠️ No pending requests found for testing approval/rejection workflow');
  }
  
  // Test client-side APIs
  await testClientSideAPIs();
  
  // Test notification system
  await testNotificationSystem();
  
  // Print summary
  console.log('\n' + '=' .repeat(70));
  console.log('📊 DOCUMENT WORKFLOW TEST SUMMARY');
  console.log('=' .repeat(70));
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📊 Total: ${testResults.tests.length}`);
  console.log(`🎯 Success Rate: ${((testResults.passed / testResults.tests.length) * 100).toFixed(1)}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.tests
      .filter(t => !t.success)
      .forEach(t => console.log(`   - ${t.name}: ${t.message}`));
  }
  
  console.log('\n🏁 Document workflow testing completed!');
}

// Run the tests
runDocumentWorkflowTest().catch(console.error);
