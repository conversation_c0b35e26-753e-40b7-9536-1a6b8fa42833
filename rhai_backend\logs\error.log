{"timestamp":"2025-06-19T07:40:59.567Z","level":"ERROR","message":"Failed to initialize email transporter:","error":"nodemailer.createTransporter is not a function"}
{"timestamp":"2025-06-19T09:01:43.747Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password"}
{"timestamp":"2025-06-19T09:01:43.754Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-19T09:33:00.384Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password"}
{"timestamp":"2025-06-19T09:33:00.395Z","level":"ERROR","message":"Client login failed","username":"albert44<PERSON>","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-19T09:33:24.344Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password"}
{"timestamp":"2025-06-19T09:33:24.347Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-19T10:40:48.392Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password"}
{"timestamp":"2025-06-19T10:40:48.411Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-19T10:41:18.288Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password"}
{"timestamp":"2025-06-19T10:41:18.294Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-19T10:41:24.456Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password"}
{"timestamp":"2025-06-19T10:41:24.458Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-19T10:42:24.863Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password"}
{"timestamp":"2025-06-19T10:42:24.865Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-19T10:42:32.497Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password"}
{"timestamp":"2025-06-19T10:42:32.499Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-19T10:46:03.565Z","level":"ERROR","message":"Client account registration failed","username":"albert4438","error":"Email already registered"}
{"timestamp":"2025-06-19T10:46:03.571Z","level":"ERROR","message":"Client account registration failed","username":"albert4438","error":"Email already registered","ip":"::1"}
{"timestamp":"2025-06-19T12:07:36.815Z","level":"ERROR","message":"Client account registration failed","username":"aldfff","error":"Email already registered"}
{"timestamp":"2025-06-19T12:07:36.820Z","level":"ERROR","message":"Client account registration failed","username":"aldfff","error":"Email already registered","ip":"::1"}
{"timestamp":"2025-06-19T12:35:12.558Z","level":"ERROR","message":"Client login failed","username":"fdgfdgfd","error":"Invalid username or password"}
{"timestamp":"2025-06-19T12:35:12.582Z","level":"ERROR","message":"Client login failed","username":"fdgfdgfd","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-22T11:11:43.445Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password"}
{"timestamp":"2025-06-22T11:11:43.451Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-22T11:11:55.858Z","level":"ERROR","message":"Client login failed","username":"albert12345","error":"Invalid username or password"}
{"timestamp":"2025-06-22T11:11:55.859Z","level":"ERROR","message":"Client login failed","username":"albert12345","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-22T11:11:56.528Z","level":"ERROR","message":"Client login failed","username":"albert12345","error":"Invalid username or password"}
{"timestamp":"2025-06-22T11:11:56.529Z","level":"ERROR","message":"Client login failed","username":"albert12345","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-22T11:11:57.098Z","level":"ERROR","message":"Client login failed","username":"albert12345","error":"Invalid username or password"}
{"timestamp":"2025-06-22T11:11:57.099Z","level":"ERROR","message":"Client login failed","username":"albert12345","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-22T11:11:57.277Z","level":"ERROR","message":"Client login failed","username":"albert12345","error":"Invalid username or password"}
{"timestamp":"2025-06-22T11:11:57.278Z","level":"ERROR","message":"Client login failed","username":"albert12345","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-22T12:49:24.158Z","level":"ERROR","message":"Failed to send OTP:","email":"<EMAIL>","purpose":"admin_registration","error":"ApiResponse.error is not a function","ip":"::1"}
{"timestamp":"2025-06-22T12:52:44.427Z","level":"ERROR","message":"Failed to send OTP:","email":"<EMAIL>","purpose":"admin_registration","error":"ApiResponse.error is not a function","ip":"::1"}
{"timestamp":"2025-06-22T12:58:09.060Z","level":"ERROR","message":"Failed to send OTP:","email":"<EMAIL>","purpose":"admin_registration","error":"ApiResponse.error is not a function","ip":"::1"}
{"timestamp":"2025-06-22T13:05:28.068Z","level":"ERROR","message":"Failed to send OTP:","email":"<EMAIL>","purpose":"admin_registration","error":"ApiResponse.error is not a function","ip":"::1"}
{"timestamp":"2025-06-22T13:07:22.378Z","level":"ERROR","message":"Client account registration failed","username":"albert4438","error":"ApiResponse.created is not a function","ip":"::1"}
{"timestamp":"2025-06-22T13:07:34.880Z","level":"ERROR","message":"Client account registration failed","username":"albert4438","error":"Username already exists"}
{"timestamp":"2025-06-22T13:07:34.880Z","level":"ERROR","message":"Client account registration failed","username":"albert4438","error":"Username already exists","ip":"::1"}
{"timestamp":"2025-06-22T13:09:20.487Z","level":"ERROR","message":"Client account registration failed","username":"albert4438","error":"Username already exists"}
{"timestamp":"2025-06-22T13:09:20.487Z","level":"ERROR","message":"Client account registration failed","username":"albert4438","error":"Username already exists","ip":"::1"}
{"timestamp":"2025-06-22T13:09:54.768Z","level":"ERROR","message":"Client account registration failed","username":"albert4438","error":"Username already exists"}
{"timestamp":"2025-06-22T13:09:54.769Z","level":"ERROR","message":"Client account registration failed","username":"albert4438","error":"Username already exists","ip":"::1"}
{"timestamp":"2025-06-22T13:11:37.302Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Account is pending verification. Please complete your registration."}
{"timestamp":"2025-06-22T13:11:37.304Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Account is pending verification. Please complete your registration.","ip":"::1"}
{"timestamp":"2025-06-22T13:30:39.338Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:30:39.342Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:31:47.173Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:31:47.174Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:32:00.667Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:32:00.668Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:32:01.794Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:32:01.796Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:32:01.957Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:32:01.962Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:32:20.093Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:32:20.098Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:32:21.789Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:32:21.790Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:32:22.009Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:32:22.011Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:32:22.205Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:32:22.207Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:32:22.389Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:32:22.390Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:36:35.560Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-06-22T13:36:35.562Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"admin_registration","error":"Invalid or expired OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:46:51.065Z","level":"ERROR","message":"Failed to generate and send OTP:","email":"<EMAIL>","purpose":"registration","error":"Please wait 10 minutes before requesting a new OTP"}
{"timestamp":"2025-06-22T13:46:51.066Z","level":"ERROR","message":"Failed to send OTP:","email":"<EMAIL>","purpose":"registration","error":"Please wait 10 minutes before requesting a new OTP","ip":"::1"}
{"timestamp":"2025-06-22T13:57:15.018Z","level":"ERROR","message":"Failed to generate and send OTP:","email":"<EMAIL>","purpose":"registration","error":"Please wait 10 minutes before requesting a new OTP"}
{"timestamp":"2025-06-22T13:57:15.020Z","level":"ERROR","message":"Failed to send OTP:","email":"<EMAIL>","purpose":"registration","error":"Please wait 10 minutes before requesting a new OTP","ip":"::1"}
{"timestamp":"2025-06-22T14:03:10.039Z","level":"ERROR","message":"Client login failed","username":"nonexistent","error":"Invalid username or password"}
{"timestamp":"2025-06-22T14:03:10.040Z","level":"ERROR","message":"Client login failed","username":"nonexistent","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-22T14:03:10.162Z","level":"ERROR","message":"Client login failed","username":"nonexistent","error":"Invalid username or password"}
{"timestamp":"2025-06-22T14:03:10.163Z","level":"ERROR","message":"Client login failed","username":"nonexistent","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-06-22T14:03:10.285Z","level":"ERROR","message":"Client login failed","username":"nonexistent","error":"Invalid username or password"}
{"timestamp":"2025-06-22T14:03:10.286Z","level":"ERROR","message":"Client login failed","username":"nonexistent","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-07T13:19:52.168Z","level":"ERROR","message":"Client login failed","username":"client","error":"Invalid username or password"}
{"timestamp":"2025-07-07T13:19:52.169Z","level":"ERROR","message":"Client login failed","username":"client","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-07T13:20:21.167Z","level":"ERROR","message":"Client login failed","username":"Client","error":"Invalid username or password"}
{"timestamp":"2025-07-07T13:20:21.169Z","level":"ERROR","message":"Client login failed","username":"Client","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-07T13:20:21.790Z","level":"ERROR","message":"Client login failed","username":"Client","error":"Invalid username or password"}
{"timestamp":"2025-07-07T13:20:21.792Z","level":"ERROR","message":"Client login failed","username":"Client","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-07T13:20:21.959Z","level":"ERROR","message":"Client login failed","username":"Client","error":"Invalid username or password"}
{"timestamp":"2025-07-07T13:20:21.959Z","level":"ERROR","message":"Client login failed","username":"Client","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-07T13:20:22.123Z","level":"ERROR","message":"Client login failed","username":"Client","error":"Invalid username or password"}
{"timestamp":"2025-07-07T13:20:22.124Z","level":"ERROR","message":"Client login failed","username":"Client","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-07T13:20:22.285Z","level":"ERROR","message":"Client login failed","username":"Client","error":"Invalid username or password"}
{"timestamp":"2025-07-07T13:20:22.286Z","level":"ERROR","message":"Client login failed","username":"Client","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-07T13:26:45.508Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password"}
{"timestamp":"2025-07-07T13:26:45.509Z","level":"ERROR","message":"Client login failed","username":"albert4438","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-07T13:29:30.982Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:29:30.984Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:30:03.081Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Account not found"}
{"timestamp":"2025-07-07T13:30:04.434Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:30:04.435Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:38.458Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Account not found"}
{"timestamp":"2025-07-07T13:31:42.346Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:42.347Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:42.531Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:42.532Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:42.731Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:42.731Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:42.914Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:42.915Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:43.097Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:43.098Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:43.363Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:43.363Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:43.542Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:43.543Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:43.693Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:43.694Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:43.863Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:31:43.864Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:46:08.071Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:46:08.072Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:48:45.772Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:48:45.773Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:48:45.799Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:48:45.799Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:49:18.777Z","level":"ERROR","message":"Failed to resend admin verification email","email":"<EMAIL>","error":"Account not found"}
{"timestamp":"2025-07-07T13:49:21.098Z","level":"ERROR","message":"Failed to send email:","to":"<EMAIL>","subject":"Your OTP Code - Barangay Management System","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-07T13:49:21.099Z","level":"ERROR","message":"Failed to send OTP email:","email":"<EMAIL>","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-07T13:49:21.100Z","level":"ERROR","message":"Failed to generate and send OTP:","email":"<EMAIL>","purpose":"registration","error":"Missing credentials for \"PLAIN\""}
{"timestamp":"2025-07-07T13:49:50.456Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:49:50.457Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:56:58.259Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:56:58.260Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:11.120Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Account not found"}
{"timestamp":"2025-07-07T13:57:13.308Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:13.308Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:13.489Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:13.490Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:13.672Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:13.672Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:13.838Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:13.839Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.016Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.017Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.201Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.202Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.373Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.374Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.551Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.552Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.710Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.710Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.889Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:14.890Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:15.057Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:15.058Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:15.224Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:15.224Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:38.008Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Account not found"}
{"timestamp":"2025-07-07T13:57:39.536Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:39.537Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:39.759Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:39.759Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:39.943Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:39.944Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:40.120Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:40.120Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:40.288Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:40.289Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:40.470Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:40.470Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:40.669Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:40.670Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:40.839Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:40.840Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.040Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.041Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.225Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.226Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.406Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.407Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.586Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.587Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.760Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.765Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.939Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:41.940Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:42.125Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:42.126Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:42.305Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:57:42.306Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:58:05.226Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:58:05.228Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:58:16.931Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:58:16.933Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:59:46.493Z","level":"ERROR","message":"OTP verification failed:","email":"<EMAIL>","purpose":"registration","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:59:46.504Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Invalid or expired OTP"}
{"timestamp":"2025-07-07T13:59:55.123Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Account not found"}
{"timestamp":"2025-07-07T14:00:18.574Z","level":"ERROR","message":"Admin email verification failed","email":"<EMAIL>","error":"Account not found"}
{"timestamp":"2025-07-07T20:59:33.575Z","level":"ERROR","message":"Client login failed","username":"testclient","error":"Invalid username or password"}
{"timestamp":"2025-07-07T20:59:33.579Z","level":"ERROR","message":"Client login failed","username":"testclient","error":"Invalid username or password","ip":"::1"}
{"timestamp":"2025-07-07T21:02:38.936Z","level":"ERROR","message":"Client account registration failed","username":"testclient","error":"Username already exists"}
{"timestamp":"2025-07-07T21:02:38.937Z","level":"ERROR","message":"Client account registration failed","username":"testclient","error":"Username already exists","ip":"::1"}
{"timestamp":"2025-07-07T21:09:39.094Z","level":"ERROR","message":"Error submitting document request","error":"queries is not iterable","clientId":12}
{"timestamp":"2025-07-07T21:09:39.097Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:238:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":2,"purpose_category_id":1,"purpose_details":"For job application at ABC Company","payment_method_id":1,"delivery_method":"pickup","priority":"normal","has_pending_cases":false,"voter_registration_number":"VR123456789","precinct_number":"001A","emergency_contact_name":"Juan Dela Cruz","emergency_contact_relationship":"Father","emergency_contact_phone":"09123456789","emergency_contact_address":"123 Main St, Barangay Test, Test City"}}
{"timestamp":"2025-07-07T21:11:52.768Z","level":"ERROR","message":"Error submitting document request","error":"queries is not iterable","clientId":12}
{"timestamp":"2025-07-07T21:11:52.770Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:239:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":1,"purpose_category_id":3,"purpose_details":"For job application at ABC Company","payment_method_id":6,"delivery_method":"pickup","priority":"normal","has_pending_cases":false,"voter_registration_number":"VR123456789","precinct_number":"001A","emergency_contact_name":"Juan Dela Cruz","emergency_contact_relationship":"Father","emergency_contact_phone":"09123456789","emergency_contact_address":"123 Main St, Barangay Test, Test City"}}
{"timestamp":"2025-07-07T21:14:43.619Z","level":"ERROR","message":"Error submitting document request","error":"queries is not iterable","clientId":12}
{"timestamp":"2025-07-07T21:14:43.621Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:246:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":1,"purpose_category_id":3,"purpose_details":"For job application at ABC Company","payment_method_id":6,"delivery_method":"pickup","priority":"normal","has_pending_cases":false,"voter_registration_number":"VR123456789","precinct_number":"001A","emergency_contact_name":"Juan Dela Cruz","emergency_contact_relationship":"Father","emergency_contact_phone":"09123456789","emergency_contact_address":"123 Main St, Barangay Test, Test City"}}
{"timestamp":"2025-07-07T21:18:04.841Z","level":"ERROR","message":"Error in document request submission transaction","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-07T21:18:04.846Z","level":"ERROR","message":"Error submitting document request","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-07T21:18:04.848Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:246:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":1,"purpose_category_id":3,"purpose_details":"For job application at ABC Company","payment_method_id":6,"delivery_method":"pickup","priority":"normal","has_pending_cases":false,"voter_registration_number":"VR123456789","precinct_number":"001A","emergency_contact_name":"Juan Dela Cruz","emergency_contact_relationship":"Father","emergency_contact_phone":"09123456789","emergency_contact_address":"123 Main St, Barangay Test, Test City"}}
{"timestamp":"2025-07-07T21:18:58.578Z","level":"ERROR","message":"Error in document request submission transaction","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-07T21:18:58.581Z","level":"ERROR","message":"Error submitting document request","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-07T21:18:58.583Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:246:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":2,"purpose_category_id":1,"purpose_details":"For job application at ABC Company","payment_method_id":1,"delivery_method":"pickup","priority":"normal","has_pending_cases":false,"voter_registration_number":"VR123456789","precinct_number":"001A","emergency_contact_name":"Juan Dela Cruz","emergency_contact_relationship":"Father","emergency_contact_phone":"09123456789","emergency_contact_address":"123 Main St, Barangay Test, Test City"}}
{"timestamp":"2025-07-07T21:20:19.374Z","level":"ERROR","message":"Error in document request submission transaction","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-07T21:20:19.376Z","level":"ERROR","message":"Error submitting document request","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-07T21:20:19.377Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:246:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":2,"purpose_category_id":1,"purpose_details":"For job application at ABC Company","payment_method_id":1,"delivery_method":"pickup","priority":"normal","has_pending_cases":false,"voter_registration_number":"VR123456789","precinct_number":"001A","emergency_contact_name":"Juan Dela Cruz","emergency_contact_relationship":"Father","emergency_contact_phone":"09123456789","emergency_contact_address":"123 Main St, Barangay Test, Test City"}}
{"timestamp":"2025-07-07T21:21:21.968Z","level":"ERROR","message":"Error in document request submission transaction","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-07T21:21:21.970Z","level":"ERROR","message":"Error submitting document request","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-07T21:21:21.971Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:246:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":2,"purpose_category_id":1,"purpose_details":"For job application at ABC Company","payment_method_id":1,"delivery_method":"pickup","priority":"normal","has_pending_cases":false,"voter_registration_number":"VR123456789","precinct_number":"001A","emergency_contact_name":"Juan Dela Cruz","emergency_contact_relationship":"Father","emergency_contact_phone":"09123456789","emergency_contact_address":"123 Main St, Barangay Test, Test City"}}
{"timestamp":"2025-07-07T21:22:52.616Z","level":"ERROR","message":"Error in document request submission transaction","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-07T21:22:52.626Z","level":"ERROR","message":"Error submitting document request","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-07T21:22:52.629Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:246:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":2,"purpose_category_id":1,"purpose_details":"For job application at ABC Company","payment_method_id":1,"delivery_method":"pickup","priority":"normal","has_pending_cases":false,"voter_registration_number":"VR123456789","precinct_number":"001A","emergency_contact_name":"Juan Dela Cruz","emergency_contact_relationship":"Father","emergency_contact_phone":"09123456789","emergency_contact_address":"123 Main St, Barangay Test, Test City"}}
{"timestamp":"2025-07-07T21:39:59.177Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:39:59.180Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:39:59.275Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-07T21:39:59.277Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-07T21:39:59.304Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-07T21:39:59.305Z","level":"ERROR","message":"Approve request error:"}
{"timestamp":"2025-07-07T21:39:59.395Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:39:59.396Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:41:02.461Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:41:02.462Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:41:02.562Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-07T21:41:02.563Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-07T21:41:02.589Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-07T21:41:02.590Z","level":"ERROR","message":"Approve request error:"}
{"timestamp":"2025-07-07T21:41:02.782Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:41:02.787Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:43:50.155Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:43:50.157Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:43:50.231Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-07T21:43:50.233Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-07T21:43:50.256Z","level":"ERROR","message":"Update request status error:"}
{"timestamp":"2025-07-07T21:43:50.258Z","level":"ERROR","message":"Approve request error:"}
{"timestamp":"2025-07-07T21:43:50.339Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T21:43:50.340Z","level":"ERROR","message":"Unknown column 'cp.address' in 'field list'","code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"\n        SELECT\n          dr.*,\n          dt.type_name as document_type,\n          dt.description as document_type_description,\n          pc.category_name as purpose_category,\n          rs.status_name,\n          rs.description as status_description,\n          pm.method_name as payment_method,\n          pm.is_online as is_online_payment,\n          CONCAT(cp.first_name, ' ', cp.last_name) as client_name,\n          cp.email as client_email,\n          cp.phone_number as client_phone,\n          cp.address as client_address,\n          COALESCE(\n            CONCAT(processed_aep.first_name, ' ', processed_aep.last_name),\n            NULL\n          ) as processed_by_name,\n          COALESCE(\n            CONCAT(approved_aep.first_name, ' ', approved_aep.last_name),\n            NULL\n          ) as approved_by_name\n        FROM document_requests dr\n        JOIN document_types dt ON dr.document_type_id = dt.id\n        JOIN purpose_categories pc ON dr.purpose_category_id = pc.id\n        JOIN request_status rs ON dr.status_id = rs.id\n        LEFT JOIN payment_methods pm ON dr.payment_method_id = pm.id\n        LEFT JOIN client_accounts ca ON dr.client_id = ca.id\n        LEFT JOIN client_profiles cp ON ca.id = cp.account_id\n        LEFT JOIN admin_employee_accounts processed_aea ON dr.processed_by = processed_aea.id\n        LEFT JOIN admin_employee_profiles processed_aep ON processed_aea.id = processed_aep.account_id\n        LEFT JOIN admin_employee_accounts approved_aea ON dr.approved_by = approved_aea.id\n        LEFT JOIN admin_employee_profiles approved_aep ON approved_aea.id = approved_aep.account_id\n        WHERE dr.id = ?\n      ","sqlState":"42S22","sqlMessage":"Unknown column 'cp.address' in 'field list'"}
{"timestamp":"2025-07-07T23:34:25.865Z","level":"ERROR","message":"Failed to get user notifications:"}
{"timestamp":"2025-07-07T23:34:25.867Z","level":"ERROR","message":"Get notifications error:"}
{"timestamp":"2025-07-07T23:34:25.877Z","level":"ERROR","message":"Failed to get unread notification count:"}
{"timestamp":"2025-07-07T23:34:25.878Z","level":"ERROR","message":"Get unread count error:"}
{"timestamp":"2025-07-07T23:34:25.901Z","level":"ERROR","message":"Get notification statistics error:"}
{"timestamp":"2025-07-07T23:42:57.343Z","level":"ERROR","message":"Failed to get user notifications:"}
{"timestamp":"2025-07-07T23:42:57.351Z","level":"ERROR","message":"Get notifications error:"}
{"timestamp":"2025-07-07T23:42:57.383Z","level":"ERROR","message":"Failed to get unread notification count:"}
{"timestamp":"2025-07-07T23:42:57.386Z","level":"ERROR","message":"Get unread count error:"}
{"timestamp":"2025-07-07T23:42:57.439Z","level":"ERROR","message":"Get notification statistics error:"}
{"timestamp":"2025-07-08T00:06:00.309Z","level":"ERROR","message":"Failed to get unread notification count:"}
{"timestamp":"2025-07-08T00:06:00.317Z","level":"ERROR","message":"Get unread count error:"}
{"timestamp":"2025-07-08T00:07:05.500Z","level":"ERROR","message":"Failed to get user notifications:"}
{"timestamp":"2025-07-08T00:07:05.507Z","level":"ERROR","message":"Get notifications error:"}
{"timestamp":"2025-07-08T00:07:05.546Z","level":"ERROR","message":"Failed to get unread notification count:"}
{"timestamp":"2025-07-08T00:07:05.547Z","level":"ERROR","message":"Get unread count error:"}
{"timestamp":"2025-07-08T00:13:16.938Z","level":"ERROR","message":"Failed to get unread notification count:"}
{"timestamp":"2025-07-08T00:13:16.943Z","level":"ERROR","message":"Get unread count error:"}
{"timestamp":"2025-07-08T01:30:32.025Z","level":"ERROR","message":"Error in document request submission transaction","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-08T01:30:32.095Z","level":"ERROR","message":"Error submitting document request","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-08T01:30:32.152Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:246:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":1,"purpose_category_id":1,"purpose_details":"It serves as proof that you are a resident of a certain city or municipality, which is often needed when applying for a job or processing government documents.","annual_income":10000,"income_source":"Employment","business_name":"","business_address":"","business_nature":"","has_real_property":false,"property_value":0,"property_location":"","payment_method_id":2,"agree_to_terms":true,"monthly_income":0,"business_income":0,"property_assessed_value":0,"computed_tax":55,"total_fee":60}}
{"timestamp":"2025-07-08T03:05:35.241Z","level":"ERROR","message":"Error in document request submission transaction","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-08T03:05:35.247Z","level":"ERROR","message":"Error submitting document request","error":"Bind parameters must not contain undefined. To pass SQL NULL specify JS null","clientId":12}
{"timestamp":"2025-07-08T03:05:35.248Z","level":"ERROR","message":"Controller error - submitRequest","error":"Failed to submit document request","stack":"Error: Failed to submit document request\n    at DocumentRequestService.submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\services\\documentRequestService.js:246:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async submitRequest (D:\\rhai_front_and_back\\rhai_backend\\src\\controllers\\documentRequestController.js:61:22)","clientId":12,"requestData":{"document_type_id":2,"purpose_category_id":2,"purpose_details":"For business, and automotive shop","emergency_contact_name":"Roco Joma Manalo","emergency_contact_relationship":"Friend","emergency_contact_phone":"***********","emergency_contact_address":"291 Lacumbre Street. Caloocan City","has_pending_cases":false,"pending_cases_details":"","is_registered_voter":true,"additional_notes":"","payment_method_id":6,"agree_to_terms":true,"total_fee":50}}
