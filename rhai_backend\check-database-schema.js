const { executeQuery } = require('./src/config/database');

async function checkDatabaseSchema() {
  console.log('🔍 Checking database schema...');
  
  try {
    // Show all tables
    console.log('\n📋 Available tables:');
    const tablesQuery = `SHOW TABLES`;
    const tables = await executeQuery(tablesQuery);
    tables.forEach(table => {
      const tableName = Object.values(table)[0];
      console.log(`  - ${tableName}`);
    });
    
    // Check document_requests table structure
    console.log('\n📄 Document requests table structure:');
    try {
      const requestStructure = await executeQuery(`DESCRIBE document_requests`);
      requestStructure.forEach(col => {
        console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(nullable)' : '(not null)'}`);
      });
    } catch (error) {
      console.log('  ❌ document_requests table not found');
    }
    
    // Check for status-related tables
    console.log('\n📊 Looking for status tables:');
    const statusTables = tables.filter(table => {
      const tableName = Object.values(table)[0].toLowerCase();
      return tableName.includes('status');
    });
    
    if (statusTables.length > 0) {
      statusTables.forEach(table => {
        const tableName = Object.values(table)[0];
        console.log(`  - Found: ${tableName}`);
      });
    } else {
      console.log('  ❌ No status tables found');
    }
    
    // Check client_accounts structure
    console.log('\n👤 Client accounts table structure:');
    try {
      const clientStructure = await executeQuery(`DESCRIBE client_accounts`);
      clientStructure.forEach(col => {
        console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(nullable)' : '(not null)'}`);
      });
    } catch (error) {
      console.log('  ❌ client_accounts table not found');
    }
    
  } catch (error) {
    console.error('❌ Error checking database schema:', error);
  }
  
  process.exit(0);
}

checkDatabaseSchema();
