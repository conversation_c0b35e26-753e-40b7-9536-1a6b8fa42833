import axios from 'axios';

class NotificationService {
  constructor() {
    // CRITICAL: Prevent multiple instances
    if (window.__notificationServiceInstance) {
      return window.__notificationServiceInstance;
    }

    this.eventSource = null;
    this.listeners = new Map();
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000; // Start with 1 second
    this.maxReconnectDelay = 30000; // Max 30 seconds
    this.baseURL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api';
    this.connectionRefs = 0; // Track how many components are using the connection
    this.currentUserType = null; // Track current connection type
    this.isConnecting = false; // Prevent multiple simultaneous connection attempts
    this.pollingInterval = null; // For polling-based notifications
    this.reconnectTimer = null; // For reconnection scheduling

    // Store global reference
    window.__notificationServiceInstance = this;
  }

  /**
   * Initialize connection (simplified)
   */
  init(userType = 'admin') {
    console.log('🚀 Initializing notification service');
    if (!this.eventSource) {
      this.connect(userType);
    }
    return Promise.resolve();
  }

  /**
   * Cleanup (simplified)
   */
  cleanup() {
    console.log('🧹 Notification service cleanup');
    // Don't disconnect - let connection persist
  }

  /**
   * Connect using PROPER SSE (Google/Mozilla Standard)
   */
  connect(userType = 'admin') {
    // Clean up any existing connections
    this.disconnect();

    // Prevent multiple calls
    if (this.isConnected || this.isConnecting) {
      console.log('🔗 Already connected or connecting');
      return Promise.resolve();
    }

    this.isConnecting = true;
    console.log('🔗 Establishing SSE connection (Google standard)');

    return new Promise((resolve, reject) => {
      try {
        const token = userType === 'admin'
          ? localStorage.getItem('adminToken')
          : localStorage.getItem('clientToken');

        if (!token) {
          this.isConnecting = false;
          reject(new Error('No authentication token'));
          return;
        }

        // Create EventSource with proper URL
        const url = `${this.baseURL}/notifications/stream?token=${encodeURIComponent(token)}`;
        console.log('🔗 SSE URL:', url.replace(/token=[^&]+/, 'token=***'));

        this.eventSource = new EventSource(url);
        this.currentUserType = userType;

        // Store references to prevent garbage collection
        window.__sseConnection = this.eventSource;
        window.__notificationEventSource = this.eventSource;

        // PROPER event handlers (Google/Mozilla standard)
        this.eventSource.onopen = (event) => {
          console.log('✅ SSE Connection established');
          this.isConnected = true;
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.emit('connected');
          resolve();
        };

        this.eventSource.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            console.log('📨 SSE Message:', data);
            this.handleNotification(data);
          } catch (error) {
            console.error('SSE message parse error:', error);
          }
        };

        // Handle specific event types
        this.eventSource.addEventListener('connected', (event) => {
          console.log('🎯 Connected event received');
        });

        this.eventSource.addEventListener('heartbeat', (event) => {
          console.log('💓 Heartbeat received');
        });

        this.eventSource.onerror = (event) => {
          console.error('❌ SSE Error:', event);
          this.isConnected = false;
          this.isConnecting = false;

          // Handle different error states
          if (this.eventSource.readyState === EventSource.CLOSED) {
            console.log('🔌 SSE Connection closed by server');
            this.emit('disconnected');
          } else if (this.eventSource.readyState === EventSource.CONNECTING) {
            console.log('🔄 SSE Reconnecting...');
          }

          // Auto-reconnect with exponential backoff
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          } else {
            console.error('🚫 Max reconnection attempts reached');
            this.emit('max_reconnect_attempts');
            reject(new Error('Max reconnection attempts reached'));
          }
        };

      } catch (error) {
        console.error('Failed to create SSE connection:', error);
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  /**
   * Poll for new notifications and updates
   */
  async pollNotifications(userType = 'admin') {
    try {
      const token = userType === 'admin'
        ? localStorage.getItem('adminToken')
        : localStorage.getItem('clientToken');

      if (!token) return;

      // Get unread count
      const response = await fetch(`${this.baseURL}/notifications/unread-count`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        const count = data.data?.count || data.count || 0;

        // Only emit if count changed or every 10th poll
        if (!this.lastCount || this.lastCount !== count || this.pollCount % 10 === 0) {
          this.emit('notification', {
            type: 'unread_count_update',
            count: count,
            timestamp: new Date().toISOString()
          });
          this.lastCount = count;
        }

        this.pollCount = (this.pollCount || 0) + 1;
      }
    } catch (error) {
      console.error('Failed to poll notifications:', error);
    }
  }

  /**
   * Disconnect from SSE (Google/Mozilla standard)
   */
  disconnect() {
    console.log('🔌 Disconnecting SSE connection');

    this.isConnected = false;
    this.isConnecting = false;

    // Clear any reconnection timers
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    // Close EventSource properly
    if (this.eventSource) {
      console.log('🔌 Closing EventSource');
      this.eventSource.close();
      this.eventSource = null;
    }

    // Clear global references
    if (window.__sseConnection) {
      delete window.__sseConnection;
    }
    if (window.__notificationEventSource) {
      delete window.__notificationEventSource;
    }

    // Clean up any polling fallback
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }

    console.log('✅ SSE Disconnected cleanly');
    this.emit('disconnected');
  }

  /**
   * Schedule reconnection with exponential backoff (Google/Mozilla standard)
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('🚫 Max reconnection attempts reached');
      this.emit('max_reconnect_attempts');
      return;
    }

    this.reconnectAttempts++;

    // Exponential backoff: 1s, 2s, 4s, 8s, 16s, max 30s
    const delay = Math.min(
      this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1),
      this.maxReconnectDelay
    );

    console.log(`🔄 Scheduling reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);

    this.reconnectTimer = setTimeout(() => {
      if (!this.isConnected && !this.isConnecting) {
        console.log(`🔄 Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
        this.connect(this.currentUserType || 'admin').catch(error => {
          console.error('Reconnection failed:', error);
        });
      }
    }, delay);
  }

  /**
   * Handle incoming notification
   */
  handleNotification(notification) {
    console.log('📢 Received notification:', notification);
    
    // Emit to specific type listeners
    this.emit(notification.type, notification);
    
    // Emit to general notification listeners
    this.emit('notification', notification);
    
    // Show browser notification if permission granted
    this.showBrowserNotification(notification);
  }

  /**
   * Show browser notification
   */
  showBrowserNotification(notification) {
    if ('Notification' in window && Notification.permission === 'granted') {
      const options = {
        body: notification.message,
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: `notification-${notification.id}`,
        requireInteraction: notification.priority === 'high' || notification.priority === 'urgent'
      };

      const browserNotification = new Notification(notification.title, options);
      
      browserNotification.onclick = () => {
        window.focus();
        this.emit('notification_click', notification);
        browserNotification.close();
      };

      // Auto close after 5 seconds for normal priority
      if (notification.priority !== 'high' && notification.priority !== 'urgent') {
        setTimeout(() => {
          browserNotification.close();
        }, 5000);
      }
    }
  }

  /**
   * Request browser notification permission
   */
  async requestNotificationPermission() {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return false;
  }

  /**
   * Subscribe to notification events
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event).add(callback);
  }

  /**
   * Unsubscribe from notification events
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).delete(callback);
    }
  }

  /**
   * Emit event to listeners
   */
  emit(event, data = null) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in notification listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Get user notifications
   */
  async getNotifications(page = 1, limit = 20, unreadOnly = false) {
    try {
      const isAdmin = !!localStorage.getItem('adminToken');
      const token = isAdmin
        ? localStorage.getItem('adminToken')
        : localStorage.getItem('clientToken');

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Use unified endpoint for both admin and client
      const response = await axios.get(`${this.baseURL}/notifications`, {
        params: { page, limit, unread_only: unreadOnly },
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get notifications:', error);
      throw error;
    }
  }

  /**
   * Get unread notification count
   */
  async getUnreadCount() {
    try {
      const isAdmin = !!localStorage.getItem('adminToken');
      const token = isAdmin
        ? localStorage.getItem('adminToken')
        : localStorage.getItem('clientToken');

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Use unified endpoint for both admin and client
      const response = await axios.get(`${this.baseURL}/notifications/unread-count`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data.data.count;
    } catch (error) {
      console.error('Failed to get unread count:', error);
      throw error;
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId) {
    try {
      // Validate notification ID
      if (!notificationId || notificationId === 'undefined') {
        throw new Error('Invalid notification ID provided');
      }

      const isAdmin = !!localStorage.getItem('adminToken');
      const token = isAdmin
        ? localStorage.getItem('adminToken')
        : localStorage.getItem('clientToken');

      if (!token) {
        throw new Error('No authentication token found');
      }

      console.log('Marking notification as read:', notificationId);

      // Use unified endpoint for both admin and client
      const response = await axios.put(`${this.baseURL}/notifications/${notificationId}/read`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      throw error;
    }
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead() {
    try {
      const isAdmin = !!localStorage.getItem('adminToken');
      const token = isAdmin
        ? localStorage.getItem('adminToken')
        : localStorage.getItem('clientToken');

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Use unified endpoint for both admin and client
      const response = await axios.put(`${this.baseURL}/notifications/mark-all-read`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
      throw error;
    }
  }

  /**
   * Send test notification (admin only)
   */
  async sendTestNotification(data) {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.post(`${this.baseURL}/notifications/test`, data, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to send test notification:', error);
      throw error;
    }
  }

  /**
   * Get notification statistics (admin only)
   */
  async getStatistics() {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.get(`${this.baseURL}/notifications/statistics`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get notification statistics:', error);
      throw error;
    }
  }

  /**
   * Test SSE connection (for debugging)
   */
  async testConnection() {
    console.log('🧪 Testing SSE connection...');

    try {
      // Clear any existing connection
      if (this.eventSource) {
        console.log('🧪 Clearing existing connection');
        this.eventSource.close();
        this.eventSource = null;
        this.isConnected = false;
        this.isConnecting = false;
      }

      // Reset state
      this.connectionRefs = 1;

      // Test connection
      await this.connect('admin');

      console.log('🧪 Test connection established');

      // Keep connection alive for 10 seconds
      setTimeout(() => {
        console.log('🧪 Test completed, keeping connection');
      }, 10000);

    } catch (error) {
      console.error('🧪 Test connection failed:', error);
    }
  }

  /**
   * Clean up old notifications (admin only)
   */
  async cleanupOldNotifications(days = 90) {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.delete(`${this.baseURL}/notifications/cleanup`, {
        params: { days },
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to cleanup old notifications:', error);
      throw error;
    }
  }
}

// Create singleton instance
const notificationService = new NotificationService();

// Make available globally for debugging
window.__notificationService = notificationService;

// Add global test function
window.testSSE = () => {
  console.log('🧪 Testing SSE connection (Google standard)...');
  notificationService.connect('admin');
};

window.stopSSE = () => {
  console.log('🛑 Stopping SSE connection...');
  notificationService.disconnect();
};

export default notificationService;
