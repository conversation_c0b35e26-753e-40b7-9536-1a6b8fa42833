import axios from 'axios';

class NotificationService {
  constructor() {
    this.eventSource = null;
    this.listeners = new Map();
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000; // Start with 1 second
    this.maxReconnectDelay = 30000; // Max 30 seconds
    this.baseURL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api';
    this.connectionRefs = 0; // Track how many components are using the connection
    this.currentUserType = null; // Track current connection type
  }

  /**
   * Add a reference to the connection (called by components)
   */
  addConnectionRef(userType = 'admin') {
    this.connectionRefs++;
    console.log(`📊 Connection refs: ${this.connectionRefs} (added by ${userType})`);

    // Connect if not already connected
    if (!this.isConnected && !this.eventSource) {
      this.currentUserType = userType;
      return this.connect(userType);
    }

    return Promise.resolve();
  }

  /**
   * Remove a reference to the connection (called by components on unmount)
   */
  removeConnectionRef() {
    if (this.connectionRefs > 0) {
      this.connectionRefs--;
      console.log(`📊 Connection refs: ${this.connectionRefs} (removed)`);
    }

    // Only disconnect if no components are using the connection
    if (this.connectionRefs === 0) {
      console.log('🔌 No more refs, disconnecting...');
      this.disconnect();
    }
  }

  /**
   * Connect to SSE stream (singleton pattern)
   */
  connect(userType = 'admin') {
    // If already connected or connecting, just return
    if (this.eventSource && (this.isConnected || this.eventSource.readyState === EventSource.CONNECTING)) {
      console.log('Already connected or connecting to notification stream');
      return Promise.resolve();
    }

    // If there's an existing connection in a bad state, clean it up first
    if (this.eventSource) {
      console.log('Cleaning up existing connection before reconnecting');
      this.eventSource.close();
      this.eventSource = null;
      this.isConnected = false;
    }

    return new Promise((resolve, reject) => {
      try {
        const token = userType === 'admin'
          ? localStorage.getItem('adminToken')
          : localStorage.getItem('clientToken');

        if (!token) {
          console.error('No authentication token found');
          reject(new Error('No authentication token found'));
          return;
        }

        // EventSource doesn't support custom headers, so we pass the token as a query parameter
        const url = `${this.baseURL}/notifications/stream?token=${encodeURIComponent(token)}`;
        console.log('🔗 Attempting SSE connection to:', url.replace(/token=[^&]+/, 'token=***'));

        this.eventSource = new EventSource(url);

        this.eventSource.onopen = () => {
          console.log('✅ Connected to notification stream');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          this.reconnectDelay = 1000;
          this.emit('connected');
          resolve();
        };

        this.eventSource.onmessage = (event) => {
          try {
            const notification = JSON.parse(event.data);
            this.handleNotification(notification);
          } catch (error) {
            console.error('Failed to parse notification:', error);
          }
        };

        this.eventSource.onerror = (error) => {
          console.error('❌ SSE connection error:', error);
          console.error('EventSource readyState:', this.eventSource?.readyState);
          this.isConnected = false;
          this.emit('error', error);

          // Only reject if this is the initial connection attempt
          if (this.reconnectAttempts === 0) {
            reject(error);
          }

          // Attempt to reconnect
          this.scheduleReconnect();
        };

      } catch (error) {
        console.error('Failed to establish SSE connection:', error);
        reject(error);
        this.scheduleReconnect();
      }
    });
  }

  /**
   * Disconnect from SSE stream
   */
  disconnect() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
      this.isConnected = false;
      console.log('Disconnected from notification stream');
      this.emit('disconnected');
    }
  }

  /**
   * Schedule reconnection attempt
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      this.emit('max_reconnect_attempts');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), this.maxReconnectDelay);
    
    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      if (!this.isConnected) {
        this.connect();
      }
    }, delay);
  }

  /**
   * Handle incoming notification
   */
  handleNotification(notification) {
    console.log('📢 Received notification:', notification);
    
    // Emit to specific type listeners
    this.emit(notification.type, notification);
    
    // Emit to general notification listeners
    this.emit('notification', notification);
    
    // Show browser notification if permission granted
    this.showBrowserNotification(notification);
  }

  /**
   * Show browser notification
   */
  showBrowserNotification(notification) {
    if ('Notification' in window && Notification.permission === 'granted') {
      const options = {
        body: notification.message,
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: `notification-${notification.id}`,
        requireInteraction: notification.priority === 'high' || notification.priority === 'urgent'
      };

      const browserNotification = new Notification(notification.title, options);
      
      browserNotification.onclick = () => {
        window.focus();
        this.emit('notification_click', notification);
        browserNotification.close();
      };

      // Auto close after 5 seconds for normal priority
      if (notification.priority !== 'high' && notification.priority !== 'urgent') {
        setTimeout(() => {
          browserNotification.close();
        }, 5000);
      }
    }
  }

  /**
   * Request browser notification permission
   */
  async requestNotificationPermission() {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return false;
  }

  /**
   * Subscribe to notification events
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event).add(callback);
  }

  /**
   * Unsubscribe from notification events
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).delete(callback);
    }
  }

  /**
   * Emit event to listeners
   */
  emit(event, data = null) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in notification listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Get user notifications
   */
  async getNotifications(page = 1, limit = 20, unreadOnly = false) {
    try {
      const token = localStorage.getItem('adminToken') || localStorage.getItem('clientToken');
      const response = await axios.get(`${this.baseURL}/notifications`, {
        params: { page, limit, unread_only: unreadOnly },
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get notifications:', error);
      throw error;
    }
  }

  /**
   * Get unread notification count
   */
  async getUnreadCount() {
    try {
      const token = localStorage.getItem('adminToken') || localStorage.getItem('clientToken');
      const response = await axios.get(`${this.baseURL}/notifications/unread-count`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data.data.count;
    } catch (error) {
      console.error('Failed to get unread count:', error);
      throw error;
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId) {
    try {
      const token = localStorage.getItem('adminToken') || localStorage.getItem('clientToken');
      const response = await axios.put(`${this.baseURL}/notifications/${notificationId}/read`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      throw error;
    }
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead() {
    try {
      const token = localStorage.getItem('adminToken') || localStorage.getItem('clientToken');
      const response = await axios.put(`${this.baseURL}/notifications/mark-all-read`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
      throw error;
    }
  }

  /**
   * Send test notification (admin only)
   */
  async sendTestNotification(data) {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.post(`${this.baseURL}/notifications/test`, data, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to send test notification:', error);
      throw error;
    }
  }

  /**
   * Get notification statistics (admin only)
   */
  async getStatistics() {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.get(`${this.baseURL}/notifications/statistics`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get notification statistics:', error);
      throw error;
    }
  }

  /**
   * Clean up old notifications (admin only)
   */
  async cleanupOldNotifications(days = 90) {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.delete(`${this.baseURL}/notifications/cleanup`, {
        params: { days },
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to cleanup old notifications:', error);
      throw error;
    }
  }
}

// Create singleton instance
const notificationService = new NotificationService();

export default notificationService;
