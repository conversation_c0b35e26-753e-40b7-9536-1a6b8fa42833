import axios from 'axios';

class NotificationService {
  constructor() {
    this.eventSource = null;
    this.listeners = new Map();
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000; // Start with 1 second
    this.maxReconnectDelay = 30000; // Max 30 seconds
    this.baseURL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api';
    this.connectionRefs = 0; // Track how many components are using the connection
    this.currentUserType = null; // Track current connection type
    this.isConnecting = false; // Prevent multiple simultaneous connection attempts
    this.pollingInterval = null; // For polling-based notifications
  }

  /**
   * Initialize connection (simplified)
   */
  init(userType = 'admin') {
    console.log('🚀 Initializing notification service');
    if (!this.eventSource) {
      this.connect(userType);
    }
    return Promise.resolve();
  }

  /**
   * Cleanup (simplified)
   */
  cleanup() {
    console.log('🧹 Notification service cleanup');
    // Don't disconnect - let connection persist
  }

  /**
   * Connect using POLLING instead of SSE (EMERGENCY FIX)
   */
  connect(userType = 'admin') {
    // Don't create multiple polling intervals
    if (this.pollingInterval) {
      console.log('📊 Polling already active');
      return Promise.resolve();
    }

    console.log('📊 Notification system active (polling mode)');

    this.isConnected = true;
    this.currentUserType = userType;
    this.emit('connected');

    // Poll for notifications every 3 seconds
    this.pollingInterval = setInterval(async () => {
      try {
        await this.pollNotifications(userType);
      } catch (error) {
        console.error('Polling error:', error);
      }
    }, 3000);

    // Initial poll
    this.pollNotifications(userType);

    return Promise.resolve();
  }

  /**
   * Poll for new notifications and updates
   */
  async pollNotifications(userType = 'admin') {
    try {
      const token = userType === 'admin'
        ? localStorage.getItem('adminToken')
        : localStorage.getItem('clientToken');

      if (!token) return;

      // Get unread count
      const response = await fetch(`${this.baseURL}/notifications/unread-count`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();

        // Emit notification event to update UI with proper count
        this.emit('notification', {
          type: 'unread_count_update',
          count: data.data?.count || data.count || 0,
          timestamp: new Date().toISOString()
        });

        // Only emit heartbeat occasionally to reduce noise
        if (Math.random() < 0.3) { // 30% chance
          this.emit('notification', {
            type: 'heartbeat',
            timestamp: new Date().toISOString()
          });
        }
      }
    } catch (error) {
      console.error('Failed to poll notifications:', error);
    }
  }

  /**
   * Disconnect from polling
   */
  disconnect() {
    console.log('🔌 disconnect() called');

    if (this.pollingInterval) {
      console.log('🔌 Stopping polling interval');
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
      this.isConnected = false;
      console.log('Disconnected from notification polling');
      this.emit('disconnected');
    }

    // Also clean up any SSE connections if they exist
    if (this.eventSource) {
      console.log('🔌 Closing any existing EventSource');
      this.eventSource.close();
      this.eventSource = null;
    }
  }

  /**
   * Schedule reconnection attempt
   */
  scheduleReconnect() {
    console.log('🚫 Auto-reconnect disabled to prevent connection loops');
    // Disabled to prevent connection issues during debugging
  }

  /**
   * Handle incoming notification
   */
  handleNotification(notification) {
    console.log('📢 Received notification:', notification);
    
    // Emit to specific type listeners
    this.emit(notification.type, notification);
    
    // Emit to general notification listeners
    this.emit('notification', notification);
    
    // Show browser notification if permission granted
    this.showBrowserNotification(notification);
  }

  /**
   * Show browser notification
   */
  showBrowserNotification(notification) {
    if ('Notification' in window && Notification.permission === 'granted') {
      const options = {
        body: notification.message,
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: `notification-${notification.id}`,
        requireInteraction: notification.priority === 'high' || notification.priority === 'urgent'
      };

      const browserNotification = new Notification(notification.title, options);
      
      browserNotification.onclick = () => {
        window.focus();
        this.emit('notification_click', notification);
        browserNotification.close();
      };

      // Auto close after 5 seconds for normal priority
      if (notification.priority !== 'high' && notification.priority !== 'urgent') {
        setTimeout(() => {
          browserNotification.close();
        }, 5000);
      }
    }
  }

  /**
   * Request browser notification permission
   */
  async requestNotificationPermission() {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return false;
  }

  /**
   * Subscribe to notification events
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event).add(callback);
  }

  /**
   * Unsubscribe from notification events
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).delete(callback);
    }
  }

  /**
   * Emit event to listeners
   */
  emit(event, data = null) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in notification listener for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Get user notifications
   */
  async getNotifications(page = 1, limit = 20, unreadOnly = false) {
    try {
      const isAdmin = !!localStorage.getItem('adminToken');
      const token = isAdmin
        ? localStorage.getItem('adminToken')
        : localStorage.getItem('clientToken');

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Use unified endpoint for both admin and client
      const response = await axios.get(`${this.baseURL}/notifications`, {
        params: { page, limit, unread_only: unreadOnly },
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get notifications:', error);
      throw error;
    }
  }

  /**
   * Get unread notification count
   */
  async getUnreadCount() {
    try {
      const isAdmin = !!localStorage.getItem('adminToken');
      const token = isAdmin
        ? localStorage.getItem('adminToken')
        : localStorage.getItem('clientToken');

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Use unified endpoint for both admin and client
      const response = await axios.get(`${this.baseURL}/notifications/unread-count`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data.data.count;
    } catch (error) {
      console.error('Failed to get unread count:', error);
      throw error;
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId) {
    try {
      // Validate notification ID
      if (!notificationId || notificationId === 'undefined') {
        throw new Error('Invalid notification ID provided');
      }

      const isAdmin = !!localStorage.getItem('adminToken');
      const token = isAdmin
        ? localStorage.getItem('adminToken')
        : localStorage.getItem('clientToken');

      if (!token) {
        throw new Error('No authentication token found');
      }

      console.log('Marking notification as read:', notificationId);

      // Use unified endpoint for both admin and client
      const response = await axios.put(`${this.baseURL}/notifications/${notificationId}/read`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      throw error;
    }
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead() {
    try {
      const isAdmin = !!localStorage.getItem('adminToken');
      const token = isAdmin
        ? localStorage.getItem('adminToken')
        : localStorage.getItem('clientToken');

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Use unified endpoint for both admin and client
      const response = await axios.put(`${this.baseURL}/notifications/mark-all-read`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
      throw error;
    }
  }

  /**
   * Send test notification (admin only)
   */
  async sendTestNotification(data) {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.post(`${this.baseURL}/notifications/test`, data, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to send test notification:', error);
      throw error;
    }
  }

  /**
   * Get notification statistics (admin only)
   */
  async getStatistics() {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.get(`${this.baseURL}/notifications/statistics`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to get notification statistics:', error);
      throw error;
    }
  }

  /**
   * Test SSE connection (for debugging)
   */
  async testConnection() {
    console.log('🧪 Testing SSE connection...');

    try {
      // Clear any existing connection
      if (this.eventSource) {
        console.log('🧪 Clearing existing connection');
        this.eventSource.close();
        this.eventSource = null;
        this.isConnected = false;
        this.isConnecting = false;
      }

      // Reset state
      this.connectionRefs = 1;

      // Test connection
      await this.connect('admin');

      console.log('🧪 Test connection established');

      // Keep connection alive for 10 seconds
      setTimeout(() => {
        console.log('🧪 Test completed, keeping connection');
      }, 10000);

    } catch (error) {
      console.error('🧪 Test connection failed:', error);
    }
  }

  /**
   * Clean up old notifications (admin only)
   */
  async cleanupOldNotifications(days = 90) {
    try {
      const token = localStorage.getItem('adminToken');
      const response = await axios.delete(`${this.baseURL}/notifications/cleanup`, {
        params: { days },
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to cleanup old notifications:', error);
      throw error;
    }
  }
}

// Create singleton instance
const notificationService = new NotificationService();

// Make available globally for debugging
window.__notificationService = notificationService;

// Add global test function
window.testNotifications = () => {
  console.log('🧪 Testing notification system manually...');
  notificationService.connect('admin');
};

window.stopNotifications = () => {
  console.log('🛑 Stopping notification system...');
  notificationService.disconnect();
};

export default notificationService;
