{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  class: \"header-content\"\n};\nconst _hoisted_2 = {\n  class: \"header-left\"\n};\nconst _hoisted_3 = {\n  class: \"page-title\"\n};\nconst _hoisted_4 = {\n  class: \"header-actions\"\n};\nconst _hoisted_5 = {\n  class: \"user-info\"\n};\nconst _hoisted_6 = {\n  class: \"user-name\"\n};\nconst _hoisted_7 = {\n  key: 0,\n  class: \"dropdown-menu\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_ClientNotifications = _resolveComponent(\"ClientNotifications\");\n  return _openBlock(), _createElementBlock(\"header\", {\n    class: _normalizeClass([\"dashboard-header\", {\n      'sidebar-collapsed': $props.sidebarCollapsed\n    }])\n  }, [_createElementVNode(\"div\", _hoisted_1, [_createCommentVNode(\" Left Section \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"button\", {\n    class: \"sidebar-toggle\",\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.handleSidebarToggle && $options.handleSidebarToggle(...args))\n  }, _cache[6] || (_cache[6] = [_createElementVNode(\"i\", {\n    class: \"fas fa-bars\"\n  }, null, -1 /* HOISTED */)])), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"h1\", null, _toDisplayString($options.getPageTitle()), 1 /* TEXT */)])]), _createCommentVNode(\" Header Actions \"), _createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" Notifications \"), _createVNode(_component_ClientNotifications, {\n    onNewNotification: $options.handleNewNotification,\n    onNotificationClick: $options.handleNotificationClick,\n    onError: $options.handleNotificationError\n  }, null, 8 /* PROPS */, [\"onNewNotification\", \"onNotificationClick\", \"onError\"]), _createCommentVNode(\" User Profile \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"user-dropdown\", {\n      active: $props.showUserDropdown\n    }])\n  }, [_createElementVNode(\"button\", {\n    class: \"user-btn\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.handleUserDropdownToggle && $options.handleUserDropdownToggle(...args))\n  }, [_cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n    class: \"user-avatar\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user-circle\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"span\", _hoisted_6, _toDisplayString($props.userName), 1 /* TEXT */), _cache[7] || (_cache[7] = _createElementVNode(\"span\", {\n    class: \"user-role\"\n  }, \"Client\", -1 /* HOISTED */))]), _cache[9] || (_cache[9] = _createElementVNode(\"i\", {\n    class: \"fas fa-chevron-down dropdown-arrow\"\n  }, null, -1 /* HOISTED */))]), $props.showUserDropdown ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[2] || (_cache[2] = $event => $options.handleMenuAction('profile'))\n  }, _cache[10] || (_cache[10] = [_createElementVNode(\"i\", {\n    class: \"fas fa-user me-2\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" My Profile \")])), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[3] || (_cache[3] = $event => $options.handleMenuAction('settings'))\n  }, _cache[11] || (_cache[11] = [_createElementVNode(\"i\", {\n    class: \"fas fa-cog me-2\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Settings \")])), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[4] || (_cache[4] = $event => $options.handleMenuAction('account'))\n  }, _cache[12] || (_cache[12] = [_createElementVNode(\"i\", {\n    class: \"fas fa-id-card me-2\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Account Info \")])), _cache[14] || (_cache[14] = _createElementVNode(\"div\", {\n    class: \"dropdown-divider\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"a\", {\n    href: \"#\",\n    class: \"dropdown-item\",\n    onClick: _cache[5] || (_cache[5] = (...args) => $options.handleLogout && $options.handleLogout(...args))\n  }, _cache[13] || (_cache[13] = [_createElementVNode(\"i\", {\n    class: \"fas fa-sign-out-alt me-2\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Logout \")]))])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)])])], 2 /* CLASS */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_normalizeClass", "$props", "sidebarCollapsed", "_createElementVNode", "_hoisted_1", "_createCommentVNode", "_hoisted_2", "onClick", "_cache", "args", "$options", "handleSidebarToggle", "_hoisted_3", "_toDisplayString", "getPageTitle", "_hoisted_4", "_createVNode", "_component_ClientNotifications", "onNewNotification", "handleNewNotification", "onNotificationClick", "handleNotificationClick", "onError", "handleNotificationError", "active", "showUserDropdown", "handleUserDropdownToggle", "_hoisted_5", "_hoisted_6", "userName", "_hoisted_7", "href", "$event", "handleMenuAction", "handleLogout"], "sources": ["D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientHeader.vue"], "sourcesContent": ["<template>\n  <header class=\"dashboard-header\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n    <div class=\"header-content\">\n      <!-- Left Section -->\n      <div class=\"header-left\">\n        <button class=\"sidebar-toggle\" @click=\"handleSidebarToggle\">\n          <i class=\"fas fa-bars\"></i>\n        </button>\n        <div class=\"page-title\">\n          <h1>{{ getPageTitle() }}</h1>\n        </div>\n      </div>\n\n      <!-- Header Actions -->\n      <div class=\"header-actions\">\n        <!-- Notifications -->\n        <ClientNotifications\n          @new-notification=\"handleNewNotification\"\n          @notification-click=\"handleNotificationClick\"\n          @error=\"handleNotificationError\"\n        />\n\n        <!-- User Profile -->\n        <div class=\"user-dropdown\" :class=\"{ active: showUserDropdown }\">\n          <button class=\"user-btn\" @click=\"handleUserDropdownToggle\">\n            <div class=\"user-avatar\">\n              <i class=\"fas fa-user-circle\"></i>\n            </div>\n            <div class=\"user-info\">\n              <span class=\"user-name\">{{ userName }}</span>\n              <span class=\"user-role\">Client</span>\n            </div>\n            <i class=\"fas fa-chevron-down dropdown-arrow\"></i>\n          </button>\n          \n          <div v-if=\"showUserDropdown\" class=\"dropdown-menu\">\n            <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('profile')\">\n              <i class=\"fas fa-user me-2\"></i>\n              My Profile\n            </a>\n            <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('settings')\">\n              <i class=\"fas fa-cog me-2\"></i>\n              Settings\n            </a>\n            <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('account')\">\n              <i class=\"fas fa-id-card me-2\"></i>\n              Account Info\n            </a>\n            <div class=\"dropdown-divider\"></div>\n            <a href=\"#\" class=\"dropdown-item\" @click=\"handleLogout\">\n              <i class=\"fas fa-sign-out-alt me-2\"></i>\n              Logout\n            </a>\n          </div>\n        </div>\n      </div>\n    </div>\n  </header>\n</template>\n\n<script>\nimport ClientNotifications from './ClientNotifications.vue';\n\nexport default {\n  name: 'ClientHeader',\n  components: {\n    ClientNotifications\n  },\n  props: {\n    userName: {\n      type: String,\n      default: 'User'\n    },\n    showUserDropdown: {\n      type: Boolean,\n      default: false\n    },\n    sidebarCollapsed: {\n      type: Boolean,\n      default: false\n    },\n    activeMenu: {\n      type: String,\n      default: 'dashboard'\n    }\n  },\n\n  emits: [\n    'sidebar-toggle',\n    'user-dropdown-toggle',\n    'menu-action',\n    'logout',\n    'error'\n  ],\n\n  mounted() {\n    // Setup event listeners for outside clicks\n    document.addEventListener('click', this.handleOutsideClick);\n  },\n\n  beforeUnmount() {\n    // Clean up event listeners\n    document.removeEventListener('click', this.handleOutsideClick);\n  },\n\n  methods: {\n    // Get page title based on active menu\n    getPageTitle() {\n      const titles = {\n        'dashboard': 'Dashboard',\n        'services': 'Services',\n        'requests': 'My Requests',\n        'documents': 'Documents',\n        'profile': 'Profile',\n        'notifications': 'Notifications',\n        'help': 'Help & Support'\n      };\n      return titles[this.activeMenu] || 'Dashboard';\n    },\n\n    // Handle sidebar toggle\n    handleSidebarToggle() {\n      this.$emit('sidebar-toggle');\n    },\n\n    // Handle user dropdown toggle\n    handleUserDropdownToggle() {\n      this.$emit('user-dropdown-toggle');\n    },\n\n    // Handle menu actions (profile, settings, etc.)\n    handleMenuAction(action) {\n      this.$emit('menu-action', action);\n    },\n\n    // Handle logout\n    handleLogout() {\n      this.$emit('logout');\n    },\n\n    // Handle outside clicks to close dropdowns\n    handleOutsideClick(event) {\n      // Check if click is outside user dropdown\n      if (!event.target.closest('.user-dropdown')) {\n        if (this.showUserDropdown) {\n          this.$emit('user-dropdown-toggle');\n        }\n      }\n    },\n\n    // Notification event handlers\n    handleNewNotification(notification) {\n      console.log('New notification received:', notification);\n      // Handle new notification - could show toast, update UI, etc.\n    },\n\n    handleNotificationClick(notification) {\n      console.log('Notification clicked:', notification);\n\n      // Ensure we have a valid notification object\n      if (!notification || typeof notification !== 'object') {\n        console.error('Invalid notification object received:', notification);\n        return;\n      }\n\n      // Handle notification click - could navigate to relevant page\n      if (notification.data && notification.data.request_id) {\n        // Navigate to request details if it's a request-related notification\n        this.$router.push(`/client/requests?highlight=${notification.data.request_id}`);\n      }\n    },\n\n    handleNotificationError(error) {\n      console.error('Notification error:', error);\n      this.$emit('error', error);\n    }\n  }\n};\n</script>\n\n<style scoped src=\"./css/clientHeader.css\"></style>\n"], "mappings": ";;EAESA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAa;;EAIjBA,KAAK,EAAC;AAAY;;EAMpBA,KAAK,EAAC;AAAgB;;EAchBA,KAAK,EAAC;AAAW;;EACdA,KAAK,EAAC;AAAW;;;EAMEA,KAAK,EAAC;;;;uBAlC3CC,mBAAA,CAwDS;IAxDDD,KAAK,EAAAE,eAAA,EAAC,kBAAkB;MAAA,qBAAgCC,MAAA,CAAAC;IAAgB;MAC9EC,mBAAA,CAsDM,OAtDNC,UAsDM,GArDJC,mBAAA,kBAAqB,EACrBF,mBAAA,CAOM,OAPNG,UAOM,GANJH,mBAAA,CAES;IAFDL,KAAK,EAAC,gBAAgB;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAC,mBAAA,IAAAD,QAAA,CAAAC,mBAAA,IAAAF,IAAA,CAAmB;gCACxDN,mBAAA,CAA2B;IAAxBL,KAAK,EAAC;EAAa,2B,IAExBK,mBAAA,CAEM,OAFNS,UAEM,GADJT,mBAAA,CAA6B,YAAAU,gBAAA,CAAtBH,QAAA,CAAAI,YAAY,mB,KAIvBT,mBAAA,oBAAuB,EACvBF,mBAAA,CAyCM,OAzCNY,UAyCM,GAxCJV,mBAAA,mBAAsB,EACtBW,YAAA,CAIEC,8BAAA;IAHCC,iBAAgB,EAAER,QAAA,CAAAS,qBAAqB;IACvCC,mBAAkB,EAAEV,QAAA,CAAAW,uBAAuB;IAC3CC,OAAK,EAAEZ,QAAA,CAAAa;oFAGVlB,mBAAA,kBAAqB,EACrBF,mBAAA,CA+BM;IA/BDL,KAAK,EAAAE,eAAA,EAAC,eAAe;MAAAwB,MAAA,EAAmBvB,MAAA,CAAAwB;IAAgB;MAC3DtB,mBAAA,CASS;IATDL,KAAK,EAAC,UAAU;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAgB,wBAAA,IAAAhB,QAAA,CAAAgB,wBAAA,IAAAjB,IAAA,CAAwB;gCACvDN,mBAAA,CAEM;IAFDL,KAAK,EAAC;EAAa,IACtBK,mBAAA,CAAkC;IAA/BL,KAAK,EAAC;EAAoB,G,sBAE/BK,mBAAA,CAGM,OAHNwB,UAGM,GAFJxB,mBAAA,CAA6C,QAA7CyB,UAA6C,EAAAf,gBAAA,CAAlBZ,MAAA,CAAA4B,QAAQ,kB,0BACnC1B,mBAAA,CAAqC;IAA/BL,KAAK,EAAC;EAAW,GAAC,QAAM,qB,6BAEhCK,mBAAA,CAAkD;IAA/CL,KAAK,EAAC;EAAoC,4B,GAGpCG,MAAA,CAAAwB,gBAAgB,I,cAA3B1B,mBAAA,CAkBM,OAlBN+B,UAkBM,GAjBJ3B,mBAAA,CAGI;IAHD4B,IAAI,EAAC,GAAG;IAACjC,KAAK,EAAC,eAAe;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAwB,MAAA,IAAEtB,QAAA,CAAAuB,gBAAgB;kCACxD9B,mBAAA,CAAgC;IAA7BL,KAAK,EAAC;EAAkB,4B,iBAAK,cAElC,E,IACAK,mBAAA,CAGI;IAHD4B,IAAI,EAAC,GAAG;IAACjC,KAAK,EAAC,eAAe;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAwB,MAAA,IAAEtB,QAAA,CAAAuB,gBAAgB;kCACxD9B,mBAAA,CAA+B;IAA5BL,KAAK,EAAC;EAAiB,4B,iBAAK,YAEjC,E,IACAK,mBAAA,CAGI;IAHD4B,IAAI,EAAC,GAAG;IAACjC,KAAK,EAAC,eAAe;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAwB,MAAA,IAAEtB,QAAA,CAAAuB,gBAAgB;kCACxD9B,mBAAA,CAAmC;IAAhCL,KAAK,EAAC;EAAqB,4B,iBAAK,gBAErC,E,gCACAK,mBAAA,CAAoC;IAA/BL,KAAK,EAAC;EAAkB,6BAC7BK,mBAAA,CAGI;IAHD4B,IAAI,EAAC,GAAG;IAACjC,KAAK,EAAC,eAAe;IAAES,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEC,QAAA,CAAAwB,YAAA,IAAAxB,QAAA,CAAAwB,YAAA,IAAAzB,IAAA,CAAY;kCACpDN,mBAAA,CAAwC;IAArCL,KAAK,EAAC;EAA0B,4B,iBAAK,UAE1C,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}