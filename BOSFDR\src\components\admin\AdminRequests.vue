<template>
  <div class="admin-requests">
    <AdminHeader
      :userName="adminData?.first_name || 'Admin'"
      :showUserDropdown="showUserDropdown"
      :sidebarCollapsed="sidebarCollapsed"
      :activeMenu="activeMenu"
      @sidebar-toggle="handleSidebarToggle"
      @user-dropdown-toggle="handleUserDropdownToggle"
      @menu-action="handleMenuAction"
      @logout="handleLogout"
    />

    <!-- Mobile Overlay -->
    <div
      class="mobile-overlay"
      :class="{ active: !sidebarCollapsed && isMobile }"
      @click="closeMobileSidebar"
    ></div>

    <div class="dashboard-container">
      <AdminSidebar
        :collapsed="sidebarCollapsed"
        :activeMenu="activeMenu"
        @menu-change="handleMenuChange"
        @logout="handleLogout"
        @toggle-sidebar="handleSidebarToggle"
      />

      <main class="main-content" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
        <!-- Loading State -->
        <div v-if="loading" class="d-flex justify-content-center align-items-center" style="min-height: 400px;">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>

        <!-- Main Content -->
        <div v-else class="container-fluid py-4">
          <!-- Error Message -->
          <div v-if="errorMessage" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {{ errorMessage }}
            <button type="button" class="btn-close" @click="errorMessage = ''" aria-label="Close"></button>
          </div>

          <!-- Page Header -->
          <div class="row mb-4">
            <div class="col-12">
              <div class="d-flex justify-content-between align-items-center flex-wrap">
                <div>
                  <h1 class="h3 mb-0 text-gray-800">Document Request Management</h1>
                  <p class="text-muted mb-0">
                    Manage and process document requests
                    <span v-if="lastRefresh" class="ms-2 small">
                      <i class="fas fa-clock text-muted"></i>
                      Last updated: {{ formatTime(lastRefresh) }}
                    </span>
                  </p>
                </div>
                <div class="d-flex gap-2 align-items-center">
                  <!-- Real-time status indicator -->
                  <div class="real-time-status me-2">
                    <span class="badge" :class="autoRefreshEnabled ? 'bg-success' : 'bg-secondary'">
                      <i class="fas fa-circle pulse" v-if="autoRefreshEnabled"></i>
                      <i class="fas fa-pause" v-else></i>
                      {{ autoRefreshEnabled ? 'Live' : 'Paused' }}
                    </span>
                  </div>

                  <button class="btn btn-outline-secondary btn-sm" @click="toggleAutoRefresh" :title="autoRefreshEnabled ? 'Disable auto-refresh' : 'Enable auto-refresh'">
                    <i class="fas" :class="autoRefreshEnabled ? 'fa-pause' : 'fa-play'"></i>
                  </button>
                  <button class="btn btn-outline-primary btn-sm" @click="showFilters = !showFilters">
                    <i class="fas fa-filter me-1"></i>
                    {{ showFilters ? 'Hide' : 'Show' }} Filters
                  </button>
                  <button class="btn btn-success btn-sm" @click="exportRequests" :disabled="loading">
                    <i class="fas fa-download me-1"></i>
                    Export CSV
                  </button>
                  <button class="btn btn-primary btn-sm" @click="refreshRequestsData" :disabled="loading">
                    <i class="fas fa-sync-alt me-1" :class="{ 'fa-spin': loading }"></i>
                    Refresh
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Request Statistics -->
          <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-3">
              <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                  <div class="row g-0 align-items-center">
                    <div class="col me-2">
                      <div class="text-xs fw-bold text-primary text-uppercase mb-1">
                        Total Requests
                      </div>
                      <div class="h5 mb-0 fw-bold text-dark">{{ requestStats.total || 0 }}</div>
                    </div>
                    <div class="col-auto">
                      <i class="fas fa-file-alt fa-2x text-muted"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
              <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                  <div class="row g-0 align-items-center">
                    <div class="col me-2">
                      <div class="text-xs fw-bold text-warning text-uppercase mb-1">
                        Pending
                      </div>
                      <div class="h5 mb-0 fw-bold text-dark">{{ requestStats.pending || 0 }}</div>
                    </div>
                    <div class="col-auto">
                      <i class="fas fa-clock fa-2x text-muted"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
              <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                  <div class="row g-0 align-items-center">
                    <div class="col me-2">
                      <div class="text-xs fw-bold text-success text-uppercase mb-1">
                        Completed
                      </div>
                      <div class="h5 mb-0 fw-bold text-dark">{{ requestStats.completed || 0 }}</div>
                    </div>
                    <div class="col-auto">
                      <i class="fas fa-check-circle fa-2x text-muted"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-xl-3 col-md-6 mb-3">
              <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                  <div class="row g-0 align-items-center">
                    <div class="col me-2">
                      <div class="text-xs fw-bold text-info text-uppercase mb-1">
                        Approved
                      </div>
                      <div class="h5 mb-0 fw-bold text-dark">{{ requestStats.approved || 0 }}</div>
                    </div>
                    <div class="col-auto">
                      <i class="fas fa-thumbs-up fa-2x text-muted"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Filters Panel -->
          <div v-if="showFilters" class="card shadow mb-4">
            <div class="card-header py-3">
              <h6 class="m-0 fw-bold text-primary">Filter Requests</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-3 mb-3">
                  <label class="form-label">Search</label>
                  <input
                    type="text"
                    class="form-control"
                    v-model="filters.search"
                    placeholder="Search by name, email, or request number"
                    @keyup.enter="applyFilters"
                  >
                </div>
                <div class="col-md-2 mb-3">
                  <label class="form-label">Status</label>
                  <select class="form-select" v-model="filters.status">
                    <option value="">All Statuses</option>
                    <option v-for="status in statusOptions" :key="status.id" :value="status.status_name">
                      {{ formatStatus(status.status_name) }}
                    </option>
                  </select>
                </div>
                <div class="col-md-2 mb-3">
                  <label class="form-label">Document Type</label>
                  <select class="form-select" v-model="filters.document_type">
                    <option value="">All Types</option>
                    <option value="barangay_clearance">Barangay Clearance</option>
                    <option value="cedula">Cedula</option>
                  </select>
                </div>
                <div class="col-md-2 mb-3">
                  <label class="form-label">Date From</label>
                  <input type="date" class="form-control" v-model="filters.date_from">
                </div>
                <div class="col-md-2 mb-3">
                  <label class="form-label">Date To</label>
                  <input type="date" class="form-control" v-model="filters.date_to">
                </div>
                <div class="col-md-1 mb-3 d-flex align-items-end">
                  <div class="d-flex gap-1 w-100">
                    <button class="btn btn-primary btn-sm" @click="applyFilters">
                      <i class="fas fa-search"></i>
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" @click="clearFilters">
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Bulk Actions Panel -->
          <div v-if="selectedRequests.length > 0" class="card shadow mb-4">
            <div class="card-header py-3 bg-warning">
              <h6 class="m-0 fw-bold text-dark">
                <i class="fas fa-tasks me-2"></i>
                Bulk Actions ({{ selectedRequests.length }} selected)
              </h6>
            </div>
            <div class="card-body">
              <div class="row align-items-end">
                <div class="col-md-3 mb-3">
                  <label class="form-label">Action</label>
                  <select class="form-select" v-model="bulkAction">
                    <option value="">Select Action</option>
                    <option v-for="status in statusOptions" :key="status.id" :value="status.id">
                      Change to {{ formatStatus(status.status_name) }}
                    </option>
                  </select>
                </div>
                <div class="col-md-6 mb-3">
                  <label class="form-label">Reason (Optional)</label>
                  <input
                    type="text"
                    class="form-control"
                    v-model="bulkReason"
                    placeholder="Enter reason for bulk action"
                  >
                </div>
                <div class="col-md-3 mb-3">
                  <div class="d-flex gap-2">
                    <button class="btn btn-warning" @click="performBulkAction" :disabled="!bulkAction">
                      <i class="fas fa-play me-1"></i>
                      Apply
                    </button>
                    <button class="btn btn-outline-secondary" @click="selectedRequests = []">
                      <i class="fas fa-times me-1"></i>
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- View Toggle -->
          <div class="d-flex justify-content-between align-items-center mb-4">
            <div class="d-flex align-items-center gap-3">
              <div class="btn-group" role="group" aria-label="View toggle">
                <input type="radio" class="btn-check" name="viewMode" id="cardView" v-model="viewMode" value="card" autocomplete="off">
                <label class="btn btn-outline-primary btn-sm" for="cardView">
                  <i class="fas fa-th-large me-1"></i>Cards
                </label>

                <input type="radio" class="btn-check" name="viewMode" id="tableView" v-model="viewMode" value="table" autocomplete="off">
                <label class="btn btn-outline-primary btn-sm" for="tableView">
                  <i class="fas fa-table me-1"></i>Table
                </label>
              </div>

              <div class="d-flex align-items-center gap-2">
                <span class="text-muted small">
                  Showing {{ ((pagination.currentPage - 1) * pagination.itemsPerPage) + 1 }} -
                  {{ Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems) }}
                  of {{ pagination.totalItems }} requests
                </span>
                <select class="form-select form-select-sm" style="width: auto;" v-model="pagination.itemsPerPage" @change="changeItemsPerPage(pagination.itemsPerPage)">
                  <option value="10">10 per page</option>
                  <option value="25">25 per page</option>
                  <option value="50">50 per page</option>
                  <option value="100">100 per page</option>
                </select>
              </div>
            </div>

            <div class="d-flex align-items-center gap-2">
              <button class="btn btn-sm btn-outline-secondary" @click="selectAllRequests" v-if="requests.length > 0">
                <i class="fas fa-check-square me-1"></i>
                {{ selectedRequests.length === requests.length ? 'Deselect All' : 'Select All' }}
              </button>
            </div>
          </div>

          <!-- Card View -->
          <div v-if="viewMode === 'card'" class="requests-grid">
            <!-- Empty State -->
            <div v-if="requests.length === 0" class="empty-state text-center py-5">
              <div class="empty-state-icon mb-3">
                <i class="fas fa-inbox fa-4x text-muted"></i>
              </div>
              <h5 class="text-muted mb-2">No Document Requests Found</h5>
              <p class="text-muted">There are no document requests matching your current filters.</p>
            </div>

            <!-- Request Cards -->
            <div v-else class="row g-4">
              <div v-for="request in requests" :key="request.id" class="col-xl-4 col-lg-6 col-md-6">
                <div class="request-card" :class="{ 'selected': selectedRequests.includes(request.id) }">
                  <!-- Card Header -->
                  <div class="request-card-header">
                    <div class="d-flex justify-content-between align-items-start">
                      <div class="d-flex align-items-center gap-2">
                        <input
                          type="checkbox"
                          class="form-check-input"
                          :checked="selectedRequests.includes(request.id)"
                          @change="toggleRequestSelection(request.id)"
                        >
                        <div class="request-number">
                          <span class="badge bg-primary">{{ request.request_number }}</span>
                        </div>
                      </div>
                      <div class="request-actions">
                        <div class="dropdown">
                          <button class="btn btn-sm btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-ellipsis-v"></i>
                          </button>
                          <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                              <a class="dropdown-item" href="#" @click.prevent="viewRequestDetails(request.id)">
                                <i class="fas fa-eye me-2"></i>View Details
                              </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                              <a class="dropdown-item text-success" href="#" @click.prevent="approveRequest(request.id)">
                                <i class="fas fa-check me-2"></i>Approve
                              </a>
                            </li>
                            <li>
                              <a class="dropdown-item text-danger" href="#" @click.prevent="rejectRequest(request.id, prompt('Rejection reason:'))">
                                <i class="fas fa-times me-2"></i>Reject
                              </a>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Card Body -->
                  <div class="request-card-body">
                    <!-- Client Info -->
                    <div class="client-info mb-3">
                      <div class="d-flex align-items-center gap-2 mb-2">
                        <div class="client-avatar">
                          <i class="fas fa-user-circle fa-2x text-primary"></i>
                        </div>
                        <div>
                          <h6 class="mb-0 fw-bold">{{ request.client_name }}</h6>
                          <small class="text-muted">{{ request.client_email }}</small>
                        </div>
                      </div>
                    </div>

                    <!-- Document Type -->
                    <div class="document-type mb-3">
                      <div class="d-flex align-items-center gap-2">
                        <i class="fas fa-file-alt text-info"></i>
                        <span class="badge bg-info-subtle text-info-emphasis px-3 py-2">
                          {{ request.document_type }}
                        </span>
                      </div>
                    </div>

                    <!-- Status and Amount -->
                    <div class="request-meta mb-3">
                      <div class="row g-2">
                        <div class="col-6">
                          <div class="meta-item">
                            <small class="text-muted d-block">Status</small>
                            <span class="badge" :class="`bg-${getStatusColor(request.status_name)}`">
                              {{ formatStatus(request.status_name) }}
                            </span>
                          </div>
                        </div>
                        <div class="col-6">
                          <div class="meta-item">
                            <small class="text-muted d-block">Amount</small>
                            <span class="fw-bold text-success">{{ formatCurrency(request.total_fee) }}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Date -->
                    <div class="request-date">
                      <small class="text-muted">
                        <i class="fas fa-calendar-alt me-1"></i>
                        Submitted {{ formatDate(request.requested_at) }}
                      </small>
                    </div>
                  </div>

                  <!-- Card Footer -->
                  <div class="request-card-footer">
                    <div class="d-flex gap-2">
                      <button class="btn btn-sm btn-outline-primary flex-fill" @click="viewRequestDetails(request.id)">
                        <i class="fas fa-eye me-1"></i>View
                      </button>
                      <button
                        v-if="canApprove(request)"
                        class="btn btn-sm btn-success"
                        @click="quickApprove(request)"
                        :title="'Approve Request'"
                        :disabled="loading"
                      >
                        <i class="fas fa-check"></i>
                      </button>
                      <button
                        v-if="canReject(request)"
                        class="btn btn-sm btn-danger"
                        @click="showQuickRejectModal(request)"
                        :title="'Reject Request'"
                        :disabled="loading"
                      >
                        <i class="fas fa-times"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Table View -->
          <div v-else class="modern-table-container">
            <!-- Table Header -->
            <div class="modern-table-header">
              <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0 fw-bold text-dark">
                  <i class="fas fa-file-alt me-2 text-primary"></i>
                  Document Requests
                </h5>
                <div class="table-actions d-flex gap-2">
                  <button class="btn btn-sm btn-outline-secondary" @click="selectAllRequests" v-if="requests.length > 0">
                    <i class="fas fa-check-square me-1"></i>
                    {{ selectedRequests.length === requests.length ? 'Deselect All' : 'Select All' }}
                  </button>
                </div>
              </div>
            </div>

            <!-- Empty State -->
            <div v-if="requests.length === 0" class="modern-table-empty">
              <div class="empty-content">
                <div class="empty-icon">
                  <i class="fas fa-inbox"></i>
                </div>
                <h6 class="empty-title">No Document Requests Found</h6>
                <p class="empty-text">There are no document requests matching your current filters.</p>
              </div>
            </div>

            <!-- Modern Table -->
            <div v-else class="modern-table">
              <!-- Table Row -->
              <div v-for="request in requests" :key="request.id" class="table-row" :class="{ 'selected': selectedRequests.includes(request.id) }">
                <!-- Selection Column -->
                <div class="table-cell selection-cell">
                  <input
                    type="checkbox"
                    class="form-check-input"
                    :checked="selectedRequests.includes(request.id)"
                    @change="toggleRequestSelection(request.id)"
                  >
                </div>

                <!-- Request Number Column -->
                <div class="table-cell request-number-cell">
                  <div class="request-number-content">
                    <span class="request-number">{{ request.request_number }}</span>
                    <span class="request-id">ID: {{ request.id }}</span>
                  </div>
                </div>

                <!-- Client Column -->
                <div class="table-cell client-cell">
                  <div class="client-info">
                    <div class="client-avatar-sm">
                      <i class="fas fa-user"></i>
                    </div>
                    <div class="client-details">
                      <div class="client-name">{{ request.client_name }}</div>
                      <div class="client-email">{{ request.client_email }}</div>
                    </div>
                  </div>
                </div>

                <!-- Document Type Column -->
                <div class="table-cell document-type-cell">
                  <div class="document-type-badge">
                    <i class="fas fa-file-alt me-2"></i>
                    <span>{{ request.document_type }}</span>
                  </div>
                </div>

                <!-- Status Column -->
                <div class="table-cell status-cell">
                  <span class="status-badge" :class="`status-${getStatusColor(request.status_name)}`">
                    <i class="fas fa-circle status-indicator"></i>
                    {{ formatStatus(request.status_name) }}
                  </span>
                </div>

                <!-- Amount Column -->
                <div class="table-cell amount-cell">
                  <div class="amount-content">
                    <span class="amount">{{ formatCurrency(request.total_fee) }}</span>
                    <span class="currency">PHP</span>
                  </div>
                </div>

                <!-- Date Column -->
                <div class="table-cell date-cell">
                  <div class="date-content">
                    <span class="date">{{ formatDate(request.requested_at) }}</span>
                    <span class="time">{{ formatTime(request.requested_at) }}</span>
                  </div>
                </div>

                <!-- Actions Column -->
                <div class="table-cell actions-cell">
                  <div class="action-buttons">
                    <button class="action-btn view-btn" @click="viewRequestDetails(request.id)" title="View Details">
                      <i class="fas fa-eye"></i>
                    </button>
                    <button
                      v-if="canApprove(request)"
                      class="action-btn approve-btn"
                      @click="quickApprove(request)"
                      title="Approve"
                      :disabled="loading"
                    >
                      <i class="fas fa-check"></i>
                    </button>
                    <button
                      v-if="canReject(request)"
                      class="action-btn reject-btn"
                      @click="showQuickRejectModal(request)"
                      title="Reject"
                      :disabled="loading"
                    >
                      <i class="fas fa-times"></i>
                    </button>
                    <div class="dropdown">
                      <button class="action-btn more-btn dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" title="More Actions">
                        <i class="fas fa-ellipsis-v"></i>
                      </button>
                      <ul class="dropdown-menu dropdown-menu-end">
                        <li>
                          <a class="dropdown-item" href="#" @click.prevent="viewRequestDetails(request.id)">
                            <i class="fas fa-eye me-2"></i>View Details
                          </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                          <a class="dropdown-item text-success" href="#" @click.prevent="approveRequest(request.id)">
                            <i class="fas fa-check me-2"></i>Approve
                          </a>
                        </li>
                        <li>
                          <a class="dropdown-item text-danger" href="#" @click.prevent="rejectRequest(request.id, prompt('Rejection reason:'))">
                            <i class="fas fa-times me-2"></i>Reject
                          </a>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Pagination -->
          <div v-if="pagination.totalPages > 1" class="pagination-container">
              <nav aria-label="Requests pagination">
                <ul class="pagination pagination-sm justify-content-center mb-0">
                  <li class="page-item" :class="{ disabled: pagination.currentPage === 1 }">
                    <a class="page-link" href="#" @click.prevent="changePage(pagination.currentPage - 1)">
                      <i class="fas fa-chevron-left"></i>
                    </a>
                  </li>
                  <li
                    v-for="page in Math.min(pagination.totalPages, 10)"
                    :key="page"
                    class="page-item"
                    :class="{ active: page === pagination.currentPage }"
                  >
                    <a class="page-link" href="#" @click.prevent="changePage(page)">{{ page }}</a>
                  </li>
                  <li class="page-item" :class="{ disabled: pagination.currentPage === pagination.totalPages }">
                    <a class="page-link" href="#" @click.prevent="changePage(pagination.currentPage + 1)">
                      <i class="fas fa-chevron-right"></i>
                    </a>
                  </li>
                </ul>
              </nav>
            </div>
          </div>

          <!-- Request Details Modal -->
          <div v-if="showRequestDetails && currentRequest" class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
            <div class="modal-dialog modal-xl modal-dialog-scrollable">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">
                    <i class="fas fa-file-alt me-2"></i>
                    Request Details - {{ currentRequest.request_number }}
                  </h5>
                  <button type="button" class="btn-close" @click="showRequestDetails = false"></button>
                </div>
                <div class="modal-body">
                  <div class="row">
                    <!-- Left Column - Request Information -->
                    <div class="col-lg-8">
                      <!-- Basic Information -->
                      <div class="card mb-4">
                        <div class="card-header">
                          <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Request Information</h6>
                        </div>
                        <div class="card-body">
                          <div class="row">
                            <div class="col-md-6">
                              <div class="mb-3">
                                <label class="form-label fw-bold">Request Number</label>
                                <p class="mb-0">{{ currentRequest.request_number }}</p>
                              </div>
                              <div class="mb-3">
                                <label class="form-label fw-bold">Document Type</label>
                                <p class="mb-0">
                                  <span class="badge bg-info">{{ currentRequest.document_type }}</span>
                                </p>
                              </div>
                              <div class="mb-3">
                                <label class="form-label fw-bold">Purpose Category</label>
                                <p class="mb-0">{{ currentRequest.purpose_category }}</p>
                              </div>
                              <div class="mb-3">
                                <label class="form-label fw-bold">Purpose Details</label>
                                <p class="mb-0">{{ currentRequest.purpose_details || 'Not specified' }}</p>
                              </div>
                            </div>
                            <div class="col-md-6">
                              <div class="mb-3">
                                <label class="form-label fw-bold">Current Status</label>
                                <p class="mb-0">
                                  <span class="badge" :class="`bg-${getStatusColor(currentRequest.status_name)}`">
                                    {{ formatStatus(currentRequest.status_name) }}
                                  </span>
                                </p>
                              </div>
                              <div class="mb-3">
                                <label class="form-label fw-bold">Priority</label>
                                <p class="mb-0">
                                  <span class="badge" :class="currentRequest.priority === 'high' ? 'bg-danger' : currentRequest.priority === 'medium' ? 'bg-warning' : 'bg-secondary'">
                                    {{ currentRequest.priority || 'Normal' }}
                                  </span>
                                </p>
                              </div>
                              <div class="mb-3">
                                <label class="form-label fw-bold">Delivery Method</label>
                                <p class="mb-0">{{ currentRequest.delivery_method || 'Pickup' }}</p>
                              </div>
                              <div class="mb-3">
                                <label class="form-label fw-bold">Date Submitted</label>
                                <p class="mb-0">{{ formatDateTime(currentRequest.requested_at) }}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Client Information -->
                      <div class="card mb-4">
                        <div class="card-header">
                          <h6 class="mb-0"><i class="fas fa-user me-2"></i>Client Information</h6>
                        </div>
                        <div class="card-body">
                          <div class="row">
                            <div class="col-md-6">
                              <div class="mb-3">
                                <label class="form-label fw-bold">Full Name</label>
                                <p class="mb-0">{{ currentRequest.client_name }}</p>
                              </div>
                              <div class="mb-3">
                                <label class="form-label fw-bold">Email Address</label>
                                <p class="mb-0">
                                  <a :href="`mailto:${currentRequest.client_email}`">{{ currentRequest.client_email }}</a>
                                </p>
                              </div>
                            </div>
                            <div class="col-md-6">
                              <div class="mb-3">
                                <label class="form-label fw-bold">Phone Number</label>
                                <p class="mb-0">
                                  <a :href="`tel:${currentRequest.client_phone}`">{{ currentRequest.client_phone || 'Not provided' }}</a>
                                </p>
                              </div>
                              <div class="mb-3">
                                <label class="form-label fw-bold">Address</label>
                                <p class="mb-0">{{ currentRequest.client_address || 'Not provided' }}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Document-Specific Details -->
                      <div v-if="currentRequest.specific_details" class="card mb-4">
                        <div class="card-header">
                          <h6 class="mb-0"><i class="fas fa-clipboard-list me-2"></i>Document-Specific Information</h6>
                        </div>
                        <div class="card-body">
                          <!-- Barangay Clearance Details -->
                          <div v-if="currentRequest.document_type === 'Barangay Clearance'">
                            <div class="row">
                              <div class="col-md-6">
                                <div class="mb-3">
                                  <label class="form-label fw-bold">Residency Period</label>
                                  <p class="mb-0">{{ currentRequest.specific_details.residency_period || 'Not specified' }}</p>
                                </div>
                                <div class="mb-3">
                                  <label class="form-label fw-bold">Civil Status</label>
                                  <p class="mb-0">{{ currentRequest.specific_details.civil_status || 'Not specified' }}</p>
                                </div>
                              </div>
                              <div class="col-md-6">
                                <div class="mb-3">
                                  <label class="form-label fw-bold">Occupation</label>
                                  <p class="mb-0">{{ currentRequest.specific_details.occupation || 'Not specified' }}</p>
                                </div>
                                <div class="mb-3">
                                  <label class="form-label fw-bold">Monthly Income</label>
                                  <p class="mb-0">{{ formatCurrency(currentRequest.specific_details.monthly_income) || 'Not specified' }}</p>
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- Cedula Details -->
                          <div v-else-if="currentRequest.document_type === 'Cedula'">
                            <div class="row">
                              <div class="col-md-6">
                                <div class="mb-3">
                                  <label class="form-label fw-bold">Birth Date</label>
                                  <p class="mb-0">{{ formatDate(currentRequest.specific_details.birth_date) || 'Not specified' }}</p>
                                </div>
                                <div class="mb-3">
                                  <label class="form-label fw-bold">Birth Place</label>
                                  <p class="mb-0">{{ currentRequest.specific_details.birth_place || 'Not specified' }}</p>
                                </div>
                                <div class="mb-3">
                                  <label class="form-label fw-bold">Civil Status</label>
                                  <p class="mb-0">{{ currentRequest.specific_details.civil_status || 'Not specified' }}</p>
                                </div>
                                <div class="mb-3">
                                  <label class="form-label fw-bold">Citizenship</label>
                                  <p class="mb-0">{{ currentRequest.specific_details.citizenship || 'Not specified' }}</p>
                                </div>
                              </div>
                              <div class="col-md-6">
                                <div class="mb-3">
                                  <label class="form-label fw-bold">Occupation</label>
                                  <p class="mb-0">{{ currentRequest.specific_details.occupation || 'Not specified' }}</p>
                                </div>
                                <div class="mb-3">
                                  <label class="form-label fw-bold">Annual Income</label>
                                  <p class="mb-0">{{ formatCurrency(currentRequest.specific_details.annual_income) || 'Not specified' }}</p>
                                </div>
                                <div class="mb-3">
                                  <label class="form-label fw-bold">Tax Amount</label>
                                  <p class="mb-0">{{ formatCurrency(currentRequest.specific_details.tax_amount) || 'Not calculated' }}</p>
                                </div>
                                <div class="mb-3">
                                  <label class="form-label fw-bold">Interest/Penalty</label>
                                  <p class="mb-0">{{ formatCurrency(currentRequest.specific_details.interest_penalty) || 'None' }}</p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Right Column - Status Management -->
                    <div class="col-lg-4">
                      <!-- Status Management -->
                      <div class="card mb-4">
                        <div class="card-header">
                          <h6 class="mb-0"><i class="fas fa-tasks me-2"></i>Status Management</h6>
                        </div>
                        <div class="card-body">
                          <div class="mb-3">
                            <label class="form-label fw-bold">Change Status</label>
                            <select class="form-select" v-model="statusUpdateForm.status_id">
                              <option value="">Select new status</option>
                              <option v-for="status in statusOptions" :key="status.id" :value="status.id">
                                {{ formatStatus(status.status_name) }}
                              </option>
                            </select>
                          </div>
                          <div class="mb-3">
                            <label class="form-label fw-bold">Reason/Notes</label>
                            <textarea
                              class="form-control"
                              rows="3"
                              v-model="statusUpdateForm.reason"
                              placeholder="Enter reason for status change (optional)"
                            ></textarea>
                          </div>
                          <div class="d-grid gap-2">
                            <button
                              class="btn btn-primary"
                              @click="updateRequestStatusFromModal"
                              :disabled="!statusUpdateForm.status_id"
                            >
                              <i class="fas fa-save me-1"></i>
                              Update Status
                            </button>
                            <button class="btn btn-success" @click="approveRequestFromModal">
                              <i class="fas fa-check me-1"></i>
                              Quick Approve
                            </button>
                            <button class="btn btn-danger" @click="showRejectForm = !showRejectForm">
                              <i class="fas fa-times me-1"></i>
                              {{ showRejectForm ? 'Cancel' : 'Reject Request' }}
                            </button>
                          </div>

                          <!-- Rejection Form -->
                          <div v-if="showRejectForm" class="mt-3 p-3 border rounded bg-light">
                            <div class="mb-3">
                              <label class="form-label fw-bold text-danger">Rejection Reason *</label>
                              <textarea
                                class="form-control"
                                rows="3"
                                v-model="rejectForm.reason"
                                placeholder="Please provide a detailed reason for rejection"
                                required
                              ></textarea>
                            </div>
                            <div class="d-grid">
                              <button
                                class="btn btn-danger"
                                @click="rejectRequestFromModal"
                                :disabled="!rejectForm.reason || rejectForm.reason.trim() === ''"
                              >
                                <i class="fas fa-times me-1"></i>
                                Confirm Rejection
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Payment Information -->
                      <div class="card mb-4">
                        <div class="card-header">
                          <h6 class="mb-0"><i class="fas fa-credit-card me-2"></i>Payment Information</h6>
                        </div>
                        <div class="card-body">
                          <div class="mb-3">
                            <label class="form-label fw-bold">Payment Method</label>
                            <p class="mb-0">{{ currentRequest.payment_method || 'Not specified' }}</p>
                          </div>
                          <div class="mb-3">
                            <label class="form-label fw-bold">Payment Status</label>
                            <p class="mb-0">
                              <span class="badge" :class="currentRequest.payment_status === 'paid' ? 'bg-success' : currentRequest.payment_status === 'pending' ? 'bg-warning' : 'bg-secondary'">
                                {{ currentRequest.payment_status || 'Unpaid' }}
                              </span>
                            </p>
                          </div>
                          <div class="row">
                            <div class="col-6">
                              <div class="mb-2">
                                <label class="form-label fw-bold small">Base Fee</label>
                                <p class="mb-0">{{ formatCurrency(currentRequest.base_fee) }}</p>
                              </div>
                            </div>
                            <div class="col-6">
                              <div class="mb-2">
                                <label class="form-label fw-bold small">Additional Fees</label>
                                <p class="mb-0">{{ formatCurrency(currentRequest.additional_fees) }}</p>
                              </div>
                            </div>
                            <div class="col-6">
                              <div class="mb-2">
                                <label class="form-label fw-bold small">Processing Fee</label>
                                <p class="mb-0">{{ formatCurrency(currentRequest.processing_fee) }}</p>
                              </div>
                            </div>
                            <div class="col-6">
                              <div class="mb-2">
                                <label class="form-label fw-bold small">Total Amount</label>
                                <p class="mb-0 fw-bold text-primary">{{ formatCurrency(currentRequest.total_fee) }}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Status History Timeline -->
                  <div class="card">
                    <div class="card-header">
                      <h6 class="mb-0"><i class="fas fa-history me-2"></i>Status History</h6>
                    </div>
                    <div class="card-body">
                      <div v-if="currentRequest.status_history && currentRequest.status_history.length > 0" class="timeline">
                        <div
                          v-for="(history, index) in currentRequest.status_history"
                          :key="history.id"
                          class="timeline-item"
                          :class="{ 'timeline-item-last': index === currentRequest.status_history.length - 1 }"
                        >
                          <div class="timeline-marker" :class="`bg-${getStatusColor(history.new_status_name)}`">
                            <i class="fas fa-circle"></i>
                          </div>
                          <div class="timeline-content">
                            <div class="timeline-header">
                              <span class="badge" :class="`bg-${getStatusColor(history.new_status_name)}`">
                                {{ formatStatus(history.new_status_name) }}
                              </span>
                              <small class="text-muted ms-2">{{ formatDateTime(history.changed_at) }}</small>
                            </div>
                            <div class="timeline-body">
                              <p class="mb-1">
                                <strong>Changed by:</strong> {{ history.changed_by_name }}
                              </p>
                              <p v-if="history.old_status_name" class="mb-1">
                                <strong>From:</strong> {{ formatStatus(history.old_status_name) }}
                              </p>
                              <p v-if="history.change_reason" class="mb-0">
                                <strong>Reason:</strong> {{ history.change_reason }}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div v-else class="text-center text-muted py-3">
                        <i class="fas fa-history fa-2x mb-2"></i>
                        <p>No status history available</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" @click="showRequestDetails = false">
                    <i class="fas fa-times me-1"></i>
                    Close
                  </button>
                  <button type="button" class="btn btn-primary" @click="refreshRequestDetails">
                    <i class="fas fa-sync-alt me-1"></i>
                    Refresh
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Reject Modal -->
          <div v-if="showQuickReject && selectedRequestForReject" class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
            <div class="modal-dialog">
              <div class="modal-content">
                <div class="modal-header">
                  <h5 class="modal-title">
                    <i class="fas fa-times-circle text-danger me-2"></i>
                    Reject Request
                  </h5>
                  <button type="button" class="btn-close" @click="closeQuickRejectModal"></button>
                </div>
                <div class="modal-body">
                  <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    You are about to reject this document request. This action will notify the client immediately.
                  </div>

                  <div class="mb-3">
                    <strong>Request Details:</strong>
                    <ul class="list-unstyled mt-2">
                      <li><strong>Request Number:</strong> {{ selectedRequestForReject.request_number }}</li>
                      <li><strong>Document Type:</strong> {{ selectedRequestForReject.document_type }}</li>
                      <li><strong>Client:</strong> {{ selectedRequestForReject.client_name }}</li>
                    </ul>
                  </div>

                  <div class="mb-3">
                    <label for="rejectionReason" class="form-label">
                      <strong>Rejection Reason <span class="text-danger">*</span></strong>
                    </label>
                    <textarea
                      id="rejectionReason"
                      v-model="quickRejectForm.reason"
                      class="form-control"
                      rows="4"
                      placeholder="Please provide a clear reason for rejecting this request..."
                      :class="{ 'is-invalid': quickRejectForm.error }"
                    ></textarea>
                    <div v-if="quickRejectForm.error" class="invalid-feedback">
                      {{ quickRejectForm.error }}
                    </div>
                    <div class="form-text">
                      This reason will be visible to the client and included in their notification.
                    </div>
                  </div>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-secondary" @click="closeQuickRejectModal" :disabled="quickRejectForm.loading">
                    <i class="fas fa-times me-1"></i>
                    Cancel
                  </button>
                  <button type="button" class="btn btn-danger" @click="confirmQuickReject" :disabled="quickRejectForm.loading || !quickRejectForm.reason.trim()">
                    <i class="fas fa-times-circle me-1"></i>
                    <span v-if="quickRejectForm.loading">
                      <i class="fas fa-spinner fa-spin me-1"></i>
                      Rejecting...
                    </span>
                    <span v-else>Reject Request</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  </template>

<script>
import AdminHeader from './AdminHeader.vue';
import AdminSidebar from './AdminSidebar.vue';
import adminAuthService from '@/services/adminAuthService';
import adminDocumentService from '@/services/adminDocumentService';
import notificationService from '@/services/notificationService';

export default {
  name: 'AdminRequests',
  components: {
    AdminHeader,
    AdminSidebar
  },

  data() {
    return {
      // UI State
      loading: true,
      sidebarCollapsed: false,
      showUserDropdown: false,
      isMobile: false,
      adminData: null,
      errorMessage: '',
      viewMode: 'table', // 'card' or 'table' - default to table view

      // Request Management Data
      requests: [],
      selectedRequests: [],
      currentRequest: null,
      statusOptions: [],

      // Pagination
      pagination: {
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        itemsPerPage: 10
      },

      // Filters
      filters: {
        status: '',
        document_type: '',
        priority: '',
        search: '',
        date_from: '',
        date_to: ''
      },

      // Statistics
      requestStats: {
        total: 0,
        pending: 0,
        approved: 0,
        completed: 0,
        thisMonth: 0
      },

      // UI State
      showFilters: false,
      showBulkActions: false,
      showRequestDetails: false,
      showRejectForm: false,
      showQuickReject: false,
      bulkAction: '',
      bulkReason: '',

      // Status Update Forms
      statusUpdateForm: {
        status_id: '',
        reason: ''
      },
      rejectForm: {
        reason: ''
      },
      quickRejectForm: {
        reason: '',
        loading: false,
        error: ''
      },
      selectedRequestForReject: null,

      // Real-time features
      refreshInterval: null,
      autoRefreshEnabled: true,
      refreshRate: 30000, // 30 seconds
      lastRefresh: null
    };
  },

  async mounted() {
    // Check authentication
    if (!adminAuthService.isLoggedIn()) {
      this.$router.push('/admin/login');
      return;
    }

    // Initialize UI state
    this.initializeUI();

    // Load component data
    await this.loadComponentData();

    // Initialize real-time features
    this.initializeRealTimeFeatures();
  },

  beforeUnmount() {
    if (this.handleResize) {
      window.removeEventListener('resize', this.handleResize);
    }

    // Clean up real-time features
    this.cleanupRealTimeFeatures();
  },

  computed: {
    activeMenu() {
      const path = this.$route.path;
      if (path.includes('/admin/users')) return 'users';
      if (path.includes('/admin/requests')) return 'requests';
      if (path.includes('/admin/reports')) return 'reports';
      if (path.includes('/admin/settings')) return 'settings';
      if (path.includes('/admin/activity-logs')) return 'activity';
      if (path.includes('/admin/profile')) return 'profile';
      return 'dashboard';
    }
  },

  methods: {
    // Initialize UI state
    initializeUI() {
      this.isMobile = window.innerWidth <= 768;

      // Load saved sidebar state (only on desktop)
      if (!this.isMobile) {
        const saved = localStorage.getItem('adminSidebarCollapsed');
        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;
      } else {
        this.sidebarCollapsed = true; // Always collapsed on mobile
      }

      // Setup resize listener
      this.handleResize = () => {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= 768;

        if (this.isMobile && !wasMobile) {
          this.sidebarCollapsed = true; // Collapse when switching to mobile
        } else if (!this.isMobile && wasMobile) {
          // Restore saved state when switching to desktop
          const saved = localStorage.getItem('adminSidebarCollapsed');
          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;
        }
      };
      window.addEventListener('resize', this.handleResize);
    },

    // Sidebar toggle
    handleSidebarToggle() {
      this.sidebarCollapsed = !this.sidebarCollapsed;
      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));
    },

    // Menu navigation
    handleMenuChange(menu) {
      const routes = {
        'dashboard': '/admin/dashboard',
        'users': '/admin/users',
        'requests': '/admin/requests',
        'reports': '/admin/reports',
        'settings': '/admin/settings',
        'activity': '/admin/activity-logs',
        'profile': '/admin/profile'
      };

      // Close sidebar on mobile after navigation
      if (this.isMobile) {
        this.sidebarCollapsed = true;
      }

      if (routes[menu]) {
        this.$router.push(routes[menu]);
      }
    },

    // User dropdown toggle
    handleUserDropdownToggle() {
      this.showUserDropdown = !this.showUserDropdown;
    },

    // Menu actions
    handleMenuAction(action) {
      if (action === 'profile') {
        this.$router.push('/admin/profile');
      } else if (action === 'settings') {
        this.$router.push('/admin/settings');
      }
      this.showUserDropdown = false;
    },

    // Close mobile sidebar
    closeMobileSidebar() {
      if (this.isMobile) {
        this.sidebarCollapsed = true;
      }
    },

    // Logout
    handleLogout() {
      adminAuthService.logout();
      this.$router.push('/admin/login');
    },

    // Load admin profile
    async loadAdminProfile() {
      try {
        const response = await adminAuthService.getProfile();
        if (response.success) {
          this.adminData = response.data;
        }
      } catch (error) {
        console.error('Failed to load admin profile:', error);
        this.adminData = adminAuthService.getAdminData();
      }
    },

    // Load all component data
    async loadComponentData() {
      this.loading = true;
      try {
        await Promise.all([
          this.loadAdminProfile(),
          this.loadStatusOptions(),
          this.loadRequests(),
          this.loadDashboardStats()
        ]);
      } catch (error) {
        console.error('Failed to load component data:', error);
        const errorData = adminDocumentService.parseError(error);
        this.errorMessage = errorData.message || 'Failed to load request data';

        if (errorData.status === 401) {
          adminAuthService.logout();
          this.$router.push('/admin/login');
        }
      } finally {
        this.loading = false;
      }
    },

    // Load status options
    async loadStatusOptions() {
      try {
        const response = await adminDocumentService.getStatusOptions();
        if (response.success) {
          this.statusOptions = response.data || [];
        }
      } catch (error) {
        console.error('Failed to load status options:', error);
        this.statusOptions = [];
      }
    },

    // Load dashboard statistics
    async loadDashboardStats() {
      try {
        const response = await adminDocumentService.getDashboardStats();
        if (response.success) {
          this.requestStats = {
            total: response.data.totalRequests || 0,
            pending: response.data.pendingRequests || 0,
            approved: response.data.approvedRequests || 0,
            completed: response.data.completedRequests || 0,
            thisMonth: response.data.todayRequests || 0
          };
        }
      } catch (error) {
        console.error('Failed to load dashboard stats:', error);
      }
    },

    // Load requests with current filters and pagination
    async loadRequests() {
      try {
        const params = {
          page: this.pagination.currentPage,
          limit: this.pagination.itemsPerPage,
          ...this.filters
        };

        const response = await adminDocumentService.getAllRequests(params);
        if (response.success) {
          this.requests = response.data.requests || [];
          this.pagination = {
            currentPage: response.data.pagination?.current_page || 1,
            totalPages: response.data.pagination?.total_pages || 1,
            totalItems: response.data.pagination?.total_records || 0,
            itemsPerPage: response.data.pagination?.per_page || 10
          };
        }
      } catch (error) {
        console.error('Failed to load requests:', error);
        const errorData = adminDocumentService.parseError(error);
        this.errorMessage = errorData.message || 'Failed to load requests';
        this.requests = [];
      }
    },

    // Filter and search methods
    applyFilters() {
      this.pagination.currentPage = 1;
      this.loadRequests();
    },

    clearFilters() {
      this.filters = {
        status: '',
        document_type: '',
        priority: '',
        search: '',
        date_from: '',
        date_to: ''
      };
      this.applyFilters();
    },

    // Pagination methods
    changePage(page) {
      if (page >= 1 && page <= this.pagination.totalPages) {
        this.pagination.currentPage = page;
        this.loadRequests();
      }
    },

    changeItemsPerPage(itemsPerPage) {
      this.pagination.itemsPerPage = itemsPerPage;
      this.pagination.currentPage = 1;
      this.loadRequests();
    },

    goBack() {
      this.$router.push('/admin/dashboard');
    },

    // Request selection methods
    toggleRequestSelection(requestId) {
      const index = this.selectedRequests.indexOf(requestId);
      if (index > -1) {
        this.selectedRequests.splice(index, 1);
      } else {
        this.selectedRequests.push(requestId);
      }
    },

    selectAllRequests() {
      if (this.selectedRequests.length === this.requests.length) {
        this.selectedRequests = [];
      } else {
        this.selectedRequests = this.requests.map(r => r.id);
      }
    },

    // Request details
    async viewRequestDetails(requestId) {
      try {
        const response = await adminDocumentService.getRequestDetails(requestId);
        if (response.success) {
          this.currentRequest = response.data;
          this.showRequestDetails = true;
          // Reset forms
          this.statusUpdateForm = { status_id: '', reason: '' };
          this.rejectForm = { reason: '' };
          this.showRejectForm = false;
        }
      } catch (error) {
        console.error('Failed to load request details:', error);
        const errorData = adminDocumentService.parseError(error);
        this.errorMessage = errorData.message || 'Failed to load request details';
      }
    },

    // Refresh request details
    async refreshRequestDetails() {
      if (this.currentRequest) {
        await this.viewRequestDetails(this.currentRequest.id);
      }
    },

    // Update request status from modal
    async updateRequestStatusFromModal() {
      if (!this.statusUpdateForm.status_id || !this.currentRequest) return;

      try {
        const response = await adminDocumentService.updateRequestStatus(
          this.currentRequest.id,
          {
            status_id: parseInt(this.statusUpdateForm.status_id),
            reason: this.statusUpdateForm.reason
          }
        );

        if (response.success) {
          // Refresh the request details
          await this.refreshRequestDetails();
          // Refresh the main requests list
          await this.loadRequests();
          // Reset form
          this.statusUpdateForm = { status_id: '', reason: '' };

          // Show success message
          this.errorMessage = '';
          // You could add a success message here if needed
        }
      } catch (error) {
        console.error('Failed to update request status:', error);
        const errorData = adminDocumentService.parseError(error);
        this.errorMessage = errorData.message || 'Failed to update request status';
      }
    },

    // Approve request from modal
    async approveRequestFromModal() {
      if (!this.currentRequest) return;

      try {
        const response = await adminDocumentService.approveRequest(
          this.currentRequest.id,
          { reason: 'Approved from request details' }
        );

        if (response.success) {
          await this.refreshRequestDetails();
          await this.loadRequests();
        }
      } catch (error) {
        console.error('Failed to approve request:', error);
        const errorData = adminDocumentService.parseError(error);
        this.errorMessage = errorData.message || 'Failed to approve request';
      }
    },

    // Reject request from modal
    async rejectRequestFromModal() {
      if (!this.currentRequest || !this.rejectForm.reason.trim()) return;

      try {
        const response = await adminDocumentService.rejectRequest(
          this.currentRequest.id,
          { reason: this.rejectForm.reason }
        );

        if (response.success) {
          await this.refreshRequestDetails();
          await this.loadRequests();
          this.rejectForm = { reason: '' };
          this.showRejectForm = false;
        }
      } catch (error) {
        console.error('Failed to reject request:', error);
        const errorData = adminDocumentService.parseError(error);
        this.errorMessage = errorData.message || 'Failed to reject request';
      }
    },

    // Status update methods
    async updateRequestStatus(requestId, statusId, reason = '') {
      try {
        const response = await adminDocumentService.updateRequestStatus(requestId, {
          status_id: statusId,
          reason: reason
        });

        if (response.success) {
          await this.loadRequests();
          await this.loadDashboardStats();
          this.errorMessage = '';
        }
      } catch (error) {
        console.error('Failed to update request status:', error);
        const errorData = adminDocumentService.parseError(error);
        this.errorMessage = errorData.message || 'Failed to update request status';
      }
    },

    async approveRequest(requestId, reason = '') {
      try {
        const response = await adminDocumentService.approveRequest(requestId, { reason });
        if (response.success) {
          await this.loadRequests();
          await this.loadDashboardStats();
          this.errorMessage = '';
        }
      } catch (error) {
        console.error('Failed to approve request:', error);
        const errorData = adminDocumentService.parseError(error);
        this.errorMessage = errorData.message || 'Failed to approve request';
      }
    },

    async rejectRequest(requestId, reason) {
      if (!reason || reason.trim() === '') {
        this.errorMessage = 'Rejection reason is required';
        return;
      }

      try {
        const response = await adminDocumentService.rejectRequest(requestId, { reason });
        if (response.success) {
          await this.loadRequests();
          await this.loadDashboardStats();
          this.errorMessage = '';
        }
      } catch (error) {
        console.error('Failed to reject request:', error);
        const errorData = adminDocumentService.parseError(error);
        this.errorMessage = errorData.message || 'Failed to reject request';
      }
    },

    // Quick approval/rejection methods
    canApprove(request) {
      return ['pending', 'under_review', 'additional_info_required'].includes(request.status_name);
    },

    canReject(request) {
      return ['pending', 'under_review', 'additional_info_required', 'approved'].includes(request.status_name);
    },

    async quickApprove(request) {
      try {
        const response = await adminDocumentService.approveRequest(request.id, {
          reason: 'Quick approval from admin interface'
        });

        if (response.success) {
          await this.loadRequests();
          await this.loadDashboardStats();
          this.showToast('Success', `Request ${request.request_number} approved successfully`, 'success');
        }
      } catch (error) {
        console.error('Failed to approve request:', error);
        const errorData = adminDocumentService.parseError(error);
        this.showToast('Error', errorData.message || 'Failed to approve request', 'error');
      }
    },

    showQuickRejectModal(request) {
      this.selectedRequestForReject = request;
      this.quickRejectForm = {
        reason: '',
        loading: false,
        error: ''
      };
      this.showQuickReject = true;
    },

    closeQuickRejectModal() {
      this.showQuickReject = false;
      this.selectedRequestForReject = null;
      this.quickRejectForm = {
        reason: '',
        loading: false,
        error: ''
      };
    },

    async confirmQuickReject() {
      if (!this.quickRejectForm.reason.trim()) {
        this.quickRejectForm.error = 'Rejection reason is required';
        return;
      }

      this.quickRejectForm.loading = true;
      this.quickRejectForm.error = '';

      try {
        const response = await adminDocumentService.rejectRequest(
          this.selectedRequestForReject.id,
          { reason: this.quickRejectForm.reason }
        );

        if (response.success) {
          await this.loadRequests();
          await this.loadDashboardStats();
          this.showToast('Success', `Request ${this.selectedRequestForReject.request_number} rejected successfully`, 'success');
          this.closeQuickRejectModal();
        }
      } catch (error) {
        console.error('Failed to reject request:', error);
        const errorData = adminDocumentService.parseError(error);
        this.quickRejectForm.error = errorData.message || 'Failed to reject request';
      } finally {
        this.quickRejectForm.loading = false;
      }
    },

    // Bulk operations
    async performBulkAction() {
      if (this.selectedRequests.length === 0) {
        this.errorMessage = 'Please select at least one request';
        return;
      }

      if (!this.bulkAction) {
        this.errorMessage = 'Please select a bulk action';
        return;
      }

      try {
        const response = await adminDocumentService.bulkUpdateRequests({
          request_ids: this.selectedRequests,
          status_id: parseInt(this.bulkAction),
          reason: this.bulkReason
        });

        if (response.success) {
          await this.loadRequests();
          await this.loadDashboardStats();
          this.selectedRequests = [];
          this.bulkAction = '';
          this.bulkReason = '';
          this.showBulkActions = false;
          this.errorMessage = '';
        }
      } catch (error) {
        console.error('Failed to perform bulk action:', error);
        const errorData = adminDocumentService.parseError(error);
        this.errorMessage = errorData.message || 'Failed to perform bulk action';
      }
    },

    // Export functionality
    async exportRequests() {
      try {
        const csvData = await adminDocumentService.exportRequests(this.filters);
        const filename = `document_requests_${new Date().toISOString().split('T')[0]}.csv`;
        adminDocumentService.downloadCSV(csvData, filename);
      } catch (error) {
        console.error('Failed to export requests:', error);
        const errorData = adminDocumentService.parseError(error);
        this.errorMessage = errorData.message || 'Failed to export requests';
      }
    },

    // Utility methods
    formatStatus(status) {
      return adminDocumentService.formatStatus(status);
    },

    getStatusColor(status) {
      return adminDocumentService.getStatusColor(status);
    },

    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    },

    formatCurrency(amount) {
      return new Intl.NumberFormat('en-PH', {
        style: 'currency',
        currency: 'PHP'
      }).format(amount || 0);
    },

    formatDateTime(dateString) {
      if (!dateString) return 'N/A';
      return new Date(dateString).toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    formatTime(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    },

    // Real-time features
    async initializeRealTimeFeatures() {
      console.log('Initializing real-time features for AdminRequests');

      try {
        // Initialize notification service
        await notificationService.init('admin');

        // Listen for request-related notifications
        notificationService.on('notification', this.handleRealTimeNotification);
        notificationService.on('request_status_changed', this.handleStatusChange);
        notificationService.on('new_request', this.handleNewRequest);

        // Start auto-refresh if enabled
        if (this.autoRefreshEnabled) {
          this.startAutoRefresh();
        }
      } catch (error) {
        console.error('Failed to initialize real-time features:', error);
      }
    },

    cleanupRealTimeFeatures() {
      console.log('Cleaning up real-time features for AdminRequests');

      // Remove notification listeners
      notificationService.off('notification', this.handleRealTimeNotification);
      notificationService.off('request_status_changed', this.handleStatusChange);
      notificationService.off('new_request', this.handleNewRequest);

      // Cleanup (simplified)
      notificationService.cleanup();

      // Stop auto-refresh
      this.stopAutoRefresh();
    },

    startAutoRefresh() {
      if (this.refreshInterval) {
        clearInterval(this.refreshInterval);
      }

      this.refreshInterval = setInterval(() => {
        if (this.autoRefreshEnabled && !this.loading) {
          console.log('Auto-refreshing requests data...');
          this.refreshRequestsData();
        }
      }, this.refreshRate);

      console.log(`Auto-refresh started with ${this.refreshRate / 1000}s interval`);
    },

    stopAutoRefresh() {
      if (this.refreshInterval) {
        clearInterval(this.refreshInterval);
        this.refreshInterval = null;
        console.log('Auto-refresh stopped');
      }
    },

    toggleAutoRefresh() {
      this.autoRefreshEnabled = !this.autoRefreshEnabled;

      if (this.autoRefreshEnabled) {
        this.startAutoRefresh();
      } else {
        this.stopAutoRefresh();
      }
    },

    async refreshRequestsData() {
      try {
        this.lastRefresh = new Date();

        // Refresh requests list
        await this.loadRequests();

        // Refresh statistics
        await this.loadDashboardStats();

        // If request details modal is open, refresh it
        if (this.showRequestDetails && this.currentRequest) {
          await this.refreshRequestDetails();
        }

        console.log('Requests data refreshed successfully');
      } catch (error) {
        console.error('Failed to refresh requests data:', error);
      }
    },

    handleRealTimeNotification(notification) {
      console.log('Real-time notification received:', notification);

      // Handle different notification types
      switch (notification.type) {
        case 'request_status_changed':
          this.handleStatusChange(notification.data);
          break;
        case 'new_request':
          this.handleNewRequest(notification.data);
          break;
        case 'request_updated':
          this.handleRequestUpdate(notification.data);
          break;
        case 'unread_count_update':
        case 'heartbeat':
          // Polling system notifications - handled by notification service
          break;
        default:
          // Only log unknown types, not system types
          if (!['unread_count_update', 'heartbeat'].includes(notification.type)) {
            console.log('Unhandled notification type:', notification.type);
          }
      }
    },

    handleStatusChange(data) {
      console.log('Request status changed:', data);

      // Update the request in the list if it exists
      const requestIndex = this.requests.findIndex(req => req.id === data.request_id);
      if (requestIndex !== -1) {
        // Refresh the specific request or reload all requests
        this.refreshRequestsData();
      }

      // Show toast notification
      this.showToast('Request status updated', `Request #${data.request_id} status changed to ${data.new_status}`, 'info');
    },

    handleNewRequest(data) {
      console.log('New request received:', data);

      // Refresh requests to show the new request
      this.refreshRequestsData();

      // Show toast notification
      this.showToast('New Request', `New ${data.document_type} request received`, 'success');
    },

    handleRequestUpdate(data) {
      console.log('Request updated:', data);

      // If the updated request is currently being viewed, refresh details
      if (this.currentRequest && this.currentRequest.id === data.request_id) {
        this.refreshRequestDetails();
      }

      // Refresh the requests list
      this.refreshRequestsData();
    },

    showToast(title, message, type = 'info') {
      // Simple toast notification - you can enhance this with a proper toast library
      console.log(`[${type.toUpperCase()}] ${title}: ${message}`);

      // You can implement a proper toast notification system here
      // For now, we'll just log to console and could show a temporary alert
    }
  }
};
</script>

<style scoped>
@import './css/adminDashboard.css';

/* Additional styles specific to AdminRequests */
.admin-requests {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
}

/* Ensure proper spacing for request statistics cards */
.card.border-left-primary {
  border-left: 4px solid #3b82f6 !important;
}

.card.border-left-warning {
  border-left: 4px solid #f59e0b !important;
}

.card.border-left-success {
  border-left: 4px solid #059669 !important;
}

.card.border-left-info {
  border-left: 4px solid #06b6d4 !important;
}

/* Bootstrap utility classes for compatibility */
.text-xs {
  font-size: 0.75rem !important;
}

.text-gray-800 {
  color: #1f2937 !important;
}

.text-gray-300 {
  color: #d1d5db !important;
}

.text-muted {
  color: #6c757d !important;
}

.fw-bold {
  font-weight: 700 !important;
}

.g-0 {
  --bs-gutter-x: 0;
  --bs-gutter-y: 0;
}

.me-2 {
  margin-right: 0.5rem !important;
}

/* Improve button spacing */
.d-flex.gap-2 {
  gap: 0.5rem !important;
}

/* Timeline Styles */
.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e3e6f0;
}

.timeline-item {
  position: relative;
  margin-bottom: 2rem;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-item.timeline-item-last::after {
  display: none;
}

.timeline-marker {
  position: absolute;
  left: -2rem;
  top: 0.25rem;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.75rem;
  z-index: 1;
}

.timeline-content {
  background: #f8f9fc;
  border-radius: 8px;
  padding: 1rem;
  border-left: 3px solid #e3e6f0;
}

.timeline-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.timeline-body p {
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.timeline-body p:last-child {
  margin-bottom: 0;
}

/* Modal Styles */
.modal-xl {
  max-width: 1200px;
}

.modal-dialog-scrollable .modal-body {
  max-height: 70vh;
  overflow-y: auto;
}

/* Real-time status indicator styles */
.real-time-status .badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 1rem;
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Card View Styles */
.requests-grid {
  min-height: 400px;
}

.empty-state {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 3rem 2rem;
  margin: 2rem 0;
}

.empty-state-icon {
  opacity: 0.5;
}

.request-card {
  background: #ffffff;
  border: 1px solid #e3e6f0;
  border-radius: 12px;
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
  transition: all 0.3s ease;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.request-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
  border-color: #5a5c69;
}

.request-card.selected {
  border-color: #4e73df;
  box-shadow: 0 0 0 2px rgba(78, 115, 223, 0.25);
}

.request-card-header {
  padding: 1rem 1.25rem 0.5rem;
  border-bottom: 1px solid #f1f1f1;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.request-card-body {
  padding: 1.25rem;
  flex-grow: 1;
}

.request-card-footer {
  padding: 0.75rem 1.25rem 1.25rem;
  background: #f8f9fa;
  border-top: 1px solid #e3e6f0;
}

.client-avatar {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  color: white;
  font-size: 1.2rem;
}

.client-info h6 {
  color: #2c3e50;
  font-weight: 600;
}

.document-type {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 0.75rem;
  border-left: 4px solid #17a2b8;
}

.document-type .badge {
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 20px;
}

.request-meta {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 0.75rem;
}

.meta-item {
  text-align: center;
}

.meta-item small {
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.request-date {
  padding-top: 0.75rem;
  border-top: 1px solid #e9ecef;
  margin-top: 0.75rem;
}

.request-actions .dropdown-toggle {
  border: none;
  background: transparent;
  color: #6c757d;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
}

.request-actions .dropdown-toggle:hover {
  background: #e9ecef;
  color: #495057;
}

/* View Toggle Styles */
.btn-check:checked + .btn-outline-primary {
  background-color: #4e73df;
  border-color: #4e73df;
  color: white;
}

/* Badge Enhancements */
.badge.bg-info-subtle {
  background-color: #cff4fc !important;
  color: #055160 !important;
  border: 1px solid #b6effb;
}

/* Button Enhancements */
.request-card-footer .btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.request-card-footer .btn:hover {
  transform: translateY(-1px);
}

/* Modern Table Styles */
.modern-table-container {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: visible;
  border: 1px solid #e8ecef;
}

.modern-table-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modern-table-header h5 {
  color: white;
  margin: 0;
  font-weight: 600;
}

.table-actions .btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(10px);
}

.table-actions .btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  color: white;
}

.modern-table-empty {
  padding: 4rem 2rem;
  text-align: center;
  background: #f8f9fa;
}

.empty-content {
  max-width: 400px;
  margin: 0 auto;
}

.empty-icon {
  font-size: 4rem;
  color: #6c757d;
  margin-bottom: 1.5rem;
  opacity: 0.5;
}

.empty-title {
  color: #495057;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.empty-text {
  color: #6c757d;
  margin: 0;
}

.modern-table {
  background: white;
}

.table-row {
  display: grid;
  grid-template-columns: 50px 180px 1fr 160px 140px 120px 140px 160px;
  align-items: center;
  padding: 1.25rem 2rem;
  border-bottom: 1px solid #f1f3f4;
  transition: all 0.2s ease;
  position: relative;
}

.table-row:hover {
  background: #f8f9fa;
  transform: translateX(4px);
  box-shadow: 4px 0 0 #667eea;
}

.table-row.selected {
  background: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  display: flex;
  align-items: center;
  min-height: 60px;
}

/* Selection Cell */
.selection-cell {
  justify-content: center;
}

.selection-cell .form-check-input {
  width: 18px;
  height: 18px;
  border-radius: 4px;
  border: 2px solid #dee2e6;
}

.selection-cell .form-check-input:checked {
  background-color: #667eea;
  border-color: #667eea;
}

/* Request Number Cell */
.request-number-content {
  display: flex;
  flex-direction: column;
}

.request-number {
  font-weight: 700;
  color: #667eea;
  font-size: 1rem;
  letter-spacing: 0.5px;
}

.request-id {
  font-size: 0.75rem;
  color: #6c757d;
  margin-top: 2px;
}

/* Client Cell */
.client-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.client-avatar-sm {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
  flex-shrink: 0;
}

.client-details {
  min-width: 0;
  flex: 1;
}

.client-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.95rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.client-email {
  font-size: 0.8rem;
  color: #6c757d;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}

/* Document Type Cell */
.document-type-badge {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
}

/* Status Cell */
.status-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-indicator {
  font-size: 0.6rem;
  animation: pulse 2s infinite;
}

.status-success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.status-warning {
  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
  color: #212529;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
}

.status-danger {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.status-info {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);
}

.status-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

/* Amount Cell */
.amount-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.amount {
  font-weight: 700;
  color: #28a745;
  font-size: 1.1rem;
}

.currency {
  font-size: 0.75rem;
  color: #6c757d;
  margin-top: 2px;
}

/* Date Cell */
.date-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.date {
  font-weight: 500;
  color: #495057;
  font-size: 0.9rem;
}

.time {
  font-size: 0.75rem;
  color: #6c757d;
  margin-top: 2px;
}

/* Actions Cell */
.action-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.action-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  cursor: pointer;
}

.view-btn {
  background: #e3f2fd;
  color: #1976d2;
}

.view-btn:hover {
  background: #bbdefb;
  transform: translateY(-2px);
}

.approve-btn {
  background: #e8f5e8;
  color: #2e7d32;
}

.approve-btn:hover {
  background: #c8e6c9;
  transform: translateY(-2px);
}

.reject-btn {
  background: #ffebee;
  color: #c62828;
}

.reject-btn:hover {
  background: #ffcdd2;
  transform: translateY(-2px);
}

.more-btn {
  background: #f5f5f5;
  color: #666;
}

.more-btn:hover {
  background: #e0e0e0;
  transform: translateY(-2px);
}

.more-btn::after {
  display: none;
}

/* Dropdown positioning fixes */
.modern-table {
  overflow: visible;
}

.table-row {
  overflow: visible;
}

.action-buttons .dropdown {
  position: static;
}

.action-buttons .dropdown-menu {
  position: absolute !important;
  top: 100% !important;
  right: 0 !important;
  left: auto !important;
  z-index: 1050 !important;
  transform: none !important;
  margin-top: 0.25rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  background: white;
  min-width: 160px;
}

.action-buttons .dropdown-menu.show {
  display: block !important;
}

/* Ensure dropdown appears above other elements */
.action-buttons .dropdown.show {
  z-index: 1051;
}

/* Pagination Container */
.pagination-container {
  background: white;
  border-radius: 0 0 16px 16px;
  padding: 1.5rem 2rem;
  border-top: 1px solid #f1f3f4;
  margin-top: -1px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.pagination-container .pagination {
  margin: 0;
}

.pagination-container .page-link {
  border: 1px solid #e3e6f0;
  color: #667eea;
  padding: 0.5rem 0.75rem;
  margin: 0 2px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.pagination-container .page-link:hover {
  background: #667eea;
  border-color: #667eea;
  color: white;
  transform: translateY(-1px);
}

.pagination-container .page-item.active .page-link {
  background: #667eea;
  border-color: #667eea;
  color: white;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.pagination-container .page-item.disabled .page-link {
  color: #6c757d;
  background: #f8f9fa;
  border-color: #e3e6f0;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .d-flex.gap-2 {
    flex-direction: column;
    align-items: stretch;
  }

  .d-flex.gap-2 .btn {
    margin-bottom: 0.5rem;
  }

  .modal-xl {
    max-width: 95%;
    margin: 1rem auto;
  }

  .timeline {
    padding-left: 1.5rem;
  }

  .timeline-marker {
    left: -1.5rem;
    width: 1.5rem;
    height: 1.5rem;
    font-size: 0.625rem;
  }

  /* Modern table mobile adjustments */
  .modern-table-header {
    padding: 1rem;
  }

  .table-row {
    grid-template-columns: 1fr;
    padding: 1rem;
    gap: 1rem;
  }

  .table-cell {
    min-height: auto;
    flex-direction: column;
    align-items: flex-start;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f3f4;
  }

  .table-cell:last-child {
    border-bottom: none;
  }

  .selection-cell {
    flex-direction: row;
    justify-content: flex-start;
  }

  .client-info {
    width: 100%;
  }

  .client-details {
    flex: 1;
  }

  .document-type-badge,
  .status-badge {
    align-self: flex-start;
  }

  .amount-content,
  .date-content {
    align-items: flex-start;
  }

  .action-buttons {
    width: 100%;
    justify-content: space-between;
  }

  .action-btn {
    flex: 1;
    max-width: 60px;
  }

  /* Card view mobile adjustments */
  .request-card {
    margin-bottom: 1rem;
  }

  .request-card-header,
  .request-card-body,
  .request-card-footer {
    padding: 1rem;
  }

  .client-info .d-flex {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .client-avatar {
    align-self: center;
  }

  .request-meta .row {
    text-align: center;
  }

  .request-card-footer .d-flex {
    flex-direction: column;
    gap: 0.5rem;
  }

  .request-card-footer .btn {
    width: 100%;
  }

  /* View toggle mobile */
  .btn-group {
    width: 100%;
  }

  .btn-group .btn {
    flex: 1;
  }
}

@media (max-width: 576px) {
  .empty-state {
    padding: 2rem 1rem;
  }

  .empty-state-icon {
    font-size: 3rem;
  }

  .request-card-body {
    padding: 1rem;
  }

  .document-type,
  .request-meta {
    padding: 0.5rem;
  }
}
</style>
