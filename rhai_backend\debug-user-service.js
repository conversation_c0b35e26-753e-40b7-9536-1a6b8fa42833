const UserService = require('./src/services/userService');

async function debugUserService() {
  console.log('🔍 Debugging User Service...');
  
  try {
    console.log('\n1. Testing getAllUsers...');
    const users = await UserService.getAllUsers(1, 10);
    console.log('✅ getAllUsers result:', users);
    
    console.log('\n2. Testing getUserStats...');
    const stats = await UserService.getUserStats();
    console.log('✅ getUserStats result:', stats);
    
    console.log('\n3. Testing searchUsers...');
    const searchResults = await UserService.searchUsers('test', 5);
    console.log('✅ searchUsers result:', searchResults);
    
  } catch (error) {
    console.error('❌ Error in user service:', error);
    console.error('Stack trace:', error.stack);
  }
  
  process.exit(0);
}

debugUserService();
