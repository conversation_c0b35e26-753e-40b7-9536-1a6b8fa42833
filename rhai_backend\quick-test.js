const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function quickTest() {
  console.log('🔧 Quick Notification Test...\n');

  try {
    // Login as admin
    const adminLoginResponse = await axios.post(`${BASE_URL}/admin/auth/login`, {
      username: 'admin12345',
      password: 'admin123'
    });

    if (!adminLoginResponse.data.success) {
      throw new Error('Admin login failed');
    }

    const adminToken = adminLoginResponse.data.data.token;
    console.log('✅ Admin logged in successfully');

    // Get notifications to check structure
    console.log('\n📋 Getting notifications...');
    
    const notificationsResponse = await axios.get(`${BASE_URL}/notifications?limit=3`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    
    console.log('📊 Response structure:');
    console.log('- Success:', notificationsResponse.data.success);
    console.log('- Message:', notificationsResponse.data.message);
    console.log('- Data type:', typeof notificationsResponse.data.data);
    
    if (notificationsResponse.data.data) {
      console.log('- Notifications array:', Array.isArray(notificationsResponse.data.data.notifications));
      console.log('- Notifications count:', notificationsResponse.data.data.notifications?.length || 0);
      console.log('- Pagination:', !!notificationsResponse.data.data.pagination);
      
      if (notificationsResponse.data.data.notifications && notificationsResponse.data.data.notifications.length > 0) {
        const firstNotification = notificationsResponse.data.data.notifications[0];
        console.log('\n📝 First notification:');
        console.log('- ID:', firstNotification.id);
        console.log('- Title:', firstNotification.title);
        console.log('- Type:', firstNotification.type);
        console.log('- Is Read:', firstNotification.is_read);
        console.log('- User Type:', firstNotification.user_type);
      }
    }

    // Test unread count
    console.log('\n📊 Getting unread count...');
    const unreadCountResponse = await axios.get(`${BASE_URL}/notifications/unread-count`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    
    console.log('📈 Unread count:', unreadCountResponse.data.data.count);

    console.log('\n✅ Quick test completed successfully!');
    console.log('\n💡 Frontend should now be able to:');
    console.log('- Load notifications from response.data.data.notifications');
    console.log('- Access notification IDs for mark as read functionality');
    console.log('- Handle pagination from response.data.data.pagination');

  } catch (error) {
    if (error.response?.status === 429) {
      console.log('⏳ Rate limited - please wait a moment and try again');
    } else {
      console.error('❌ Test failed:', error.response?.data?.message || error.message);
    }
  }
}

quickTest();
