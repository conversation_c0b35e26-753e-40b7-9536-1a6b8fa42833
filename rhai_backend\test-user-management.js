const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';
let adminToken = '';

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

function logTest(name, success, message = '') {
  const status = success ? '✅ PASS' : '❌ FAIL';
  console.log(`${status} ${name}${message ? ': ' + message : ''}`);
  
  testResults.tests.push({ name, success, message });
  if (success) testResults.passed++;
  else testResults.failed++;
}

async function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function loginAdmin() {
  console.log('\n🔐 Admin Login...');
  
  try {
    const response = await axios.post(`${BASE_URL}/admin/auth/login`, {
      username: 'admin12345',
      password: '12345QWERTqwert'
    });
    
    if (response.data.success && response.data.data.token) {
      adminToken = response.data.data.token;
      logTest('Admin Login', true);
      return true;
    } else {
      logTest('Admin Login', false, 'No token received');
      return false;
    }
  } catch (error) {
    logTest('Admin Login', false, error.response?.data?.message || error.message);
    return false;
  }
}

async function testGetUsers() {
  console.log('\n👥 Testing User Management APIs...');
  
  try {
    const response = await axios.get(`${BASE_URL}/users`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    if (response.data.success && response.data.data.users) {
      const users = response.data.data.users;
      logTest('Get All Users', true, `Found ${users.length} users`);
      return users;
    } else {
      logTest('Get All Users', false, 'Invalid response structure');
      return [];
    }
  } catch (error) {
    logTest('Get All Users', false, error.response?.data?.message || error.message);
    return [];
  }
}

async function testGetUserStats() {
  try {
    const response = await axios.get(`${BASE_URL}/users/stats`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    if (response.data.success && response.data.data) {
      const stats = response.data.data;
      logTest('Get User Stats', true, `Total: ${stats.total}, Active: ${stats.active}, Admins: ${stats.admins}`);
      return stats;
    } else {
      logTest('Get User Stats', false, 'Invalid response structure');
      return null;
    }
  } catch (error) {
    logTest('Get User Stats', false, error.response?.data?.message || error.message);
    return null;
  }
}

async function testCreateUser() {
  try {
    const testUser = {
      username: `testuser_${Date.now()}`,
      email: `testuser_${Date.now()}@example.com`,
      password: 'TestPassword123!',
      first_name: 'Test',
      last_name: 'User',
      role: 'client',
      phone_number: '09123456789'
    };
    
    const response = await axios.post(`${BASE_URL}/users`, testUser, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    if (response.data.success && response.data.data) {
      logTest('Create User', true, `Created user: ${testUser.username}`);
      return response.data.data;
    } else {
      logTest('Create User', false, 'Failed to create user');
      return null;
    }
  } catch (error) {
    logTest('Create User', false, error.response?.data?.message || error.message);
    return null;
  }
}

async function testUpdateUser(userId) {
  try {
    const updateData = {
      first_name: 'Updated',
      last_name: 'Name',
      email: `updated_${Date.now()}@example.com`
    };
    
    const response = await axios.put(`${BASE_URL}/users/${userId}`, updateData, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    if (response.data.success) {
      logTest('Update User', true, `Updated user ID: ${userId}`);
      return true;
    } else {
      logTest('Update User', false, 'Failed to update user');
      return false;
    }
  } catch (error) {
    logTest('Update User', false, error.response?.data?.message || error.message);
    return false;
  }
}

async function testToggleUserStatus(userId) {
  try {
    const response = await axios.patch(`${BASE_URL}/users/${userId}/toggle-status`, {}, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    if (response.data.success) {
      logTest('Toggle User Status', true, `Toggled status for user ID: ${userId}`);
      return true;
    } else {
      logTest('Toggle User Status', false, 'Failed to toggle user status');
      return false;
    }
  } catch (error) {
    logTest('Toggle User Status', false, error.response?.data?.message || error.message);
    return false;
  }
}

async function testGetUserById(userId) {
  try {
    const response = await axios.get(`${BASE_URL}/users/${userId}`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    if (response.data.success && response.data.data) {
      logTest('Get User By ID', true, `Retrieved user ID: ${userId}`);
      return response.data.data;
    } else {
      logTest('Get User By ID', false, 'Failed to get user details');
      return null;
    }
  } catch (error) {
    logTest('Get User By ID', false, error.response?.data?.message || error.message);
    return null;
  }
}

async function testSearchUsers() {
  try {
    const response = await axios.get(`${BASE_URL}/users/search?q=test`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    if (response.data.success) {
      const users = response.data.data || [];
      logTest('Search Users', true, `Found ${users.length} users matching "test"`);
      return users;
    } else {
      logTest('Search Users', false, 'Failed to search users');
      return [];
    }
  } catch (error) {
    logTest('Search Users', false, error.response?.data?.message || error.message);
    return [];
  }
}

async function runUserManagementTest() {
  console.log('🚀 Starting User Management API Testing...');
  console.log('=' .repeat(70));
  
  // Login as admin
  const adminLoginSuccess = await loginAdmin();
  if (!adminLoginSuccess) {
    console.log('\n❌ Cannot proceed without admin authentication');
    return;
  }
  
  // Test basic user operations
  const users = await testGetUsers();
  await testGetUserStats();
  await testSearchUsers();
  
  // Test CRUD operations
  const createdUser = await testCreateUser();
  if (createdUser) {
    await delay(1000);
    await testGetUserById(createdUser.id);
    await testUpdateUser(createdUser.id);
    await testToggleUserStatus(createdUser.id);
  }
  
  // Test filtering
  try {
    const response = await axios.get(`${BASE_URL}/users?role=admin&limit=5`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    if (response.data.success) {
      logTest('Filter Users by Role', true, `Found ${response.data.data.users.length} admin users`);
    } else {
      logTest('Filter Users by Role', false, 'Failed to filter users');
    }
  } catch (error) {
    logTest('Filter Users by Role', false, error.response?.data?.message || error.message);
  }
  
  // Print summary
  console.log('\n' + '=' .repeat(70));
  console.log('📊 USER MANAGEMENT TEST SUMMARY');
  console.log('=' .repeat(70));
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📊 Total: ${testResults.tests.length}`);
  console.log(`🎯 Success Rate: ${((testResults.passed / testResults.tests.length) * 100).toFixed(1)}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.tests
      .filter(t => !t.success)
      .forEach(t => console.log(`   - ${t.name}: ${t.message}`));
  }
  
  console.log('\n🏁 User management testing completed!');
}

// Run the tests
runUserManagementTest().catch(console.error);
