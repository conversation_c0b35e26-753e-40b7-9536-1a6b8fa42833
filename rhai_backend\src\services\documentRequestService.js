const DocumentRequest = require('../models/DocumentRequest');
const BarangayClearanceApplication = require('../models/BarangayClearanceApplication');
const CedulaApplication = require('../models/CedulaApplication');
const { executeQuery, executeTransaction, executeTransactionCallback } = require('../config/database');
const logger = require('../utils/logger');

class DocumentRequestService {
  // Get document types
  static async getDocumentTypes() {
    try {
      const query = `
        SELECT id, type_name, description, base_fee, is_active 
        FROM document_types 
        WHERE is_active = 1 
        ORDER BY type_name
      `;
      
      const results = await executeQuery(query);
      
      logger.info('Document types retrieved successfully', {
        count: results.length
      });
      
      return {
        success: true,
        data: results,
        message: 'Document types retrieved successfully'
      };
    } catch (error) {
      logger.error('Error retrieving document types', { error: error.message });
      throw new Error('Failed to retrieve document types');
    }
  }

  // Get purpose categories
  static async getPurposeCategories() {
    try {
      const query = `
        SELECT id, category_name, description, is_active 
        FROM purpose_categories 
        WHERE is_active = 1 
        ORDER BY category_name
      `;
      
      const results = await executeQuery(query);
      
      logger.info('Purpose categories retrieved successfully', {
        count: results.length
      });
      
      return {
        success: true,
        data: results,
        message: 'Purpose categories retrieved successfully'
      };
    } catch (error) {
      logger.error('Error retrieving purpose categories', { error: error.message });
      throw new Error('Failed to retrieve purpose categories');
    }
  }

  // Get payment methods
  static async getPaymentMethods() {
    try {
      const query = `
        SELECT id, method_name, method_code, description, is_online, is_active,
               processing_fee_percentage, processing_fee_fixed
        FROM payment_methods 
        WHERE is_active = 1 
        ORDER BY is_online DESC, method_name
      `;
      
      const results = await executeQuery(query);
      
      logger.info('Payment methods retrieved successfully', {
        count: results.length
      });
      
      return {
        success: true,
        data: results,
        message: 'Payment methods retrieved successfully'
      };
    } catch (error) {
      logger.error('Error retrieving payment methods', { error: error.message });
      throw new Error('Failed to retrieve payment methods');
    }
  }

  // Submit document request
  static async submitRequest(requestData, clientId) {
    console.log('Service: submitRequest called with:', { requestData, clientId });
    console.log('Service: Fixed undefined parameters issue');
    const transaction = async (connection) => {
      try {
        const {
          document_type_id,
          purpose_category_id,
          purpose_details,
          payment_method_id,
          delivery_method = 'pickup',
          delivery_address,
          priority = 'normal',
          // Document-specific data
          ...specificData
        } = requestData;

        // Get document type to determine base fee
        console.log('Service: Getting document type for ID:', document_type_id);
        const docTypeQuery = 'SELECT type_name, base_fee FROM document_types WHERE id = ?';
        const docTypeResults = await connection.execute(docTypeQuery, [document_type_id]);
        console.log('Service: Document type query result:', docTypeResults[0]);

        if (docTypeResults[0].length === 0) {
          throw new Error('Invalid document type');
        }

        const documentType = docTypeResults[0][0];
        let base_fee = parseFloat(documentType.base_fee);
        let additional_fees = 0;
        let processing_fee = 0;
        let delivery_fee = 0;
        console.log('Service: Document type found:', documentType);
        console.log('Service: Base fee:', base_fee);

        // Calculate delivery fee if delivery is selected
        if (delivery_method === 'delivery') {
          delivery_fee = 50.00; // Standard delivery fee
        }

        // Get payment method processing fees
        if (payment_method_id) {
          const paymentMethodQuery = `
            SELECT processing_fee_percentage, processing_fee_fixed 
            FROM payment_methods 
            WHERE id = ?
          `;
          const paymentMethodResults = await connection.execute(paymentMethodQuery, [payment_method_id]);
          
          if (paymentMethodResults[0].length > 0) {
            const paymentMethod = paymentMethodResults[0][0];
            const subtotal = base_fee + additional_fees + delivery_fee;
            
            processing_fee = parseFloat(paymentMethod.processing_fee_fixed || 0) + 
                           (subtotal * parseFloat(paymentMethod.processing_fee_percentage || 0) / 100);
          }
        }

        // For Cedula, calculate tax and update base fee
        if (documentType.type_name === 'Cedula' && specificData.annual_income) {
          const taxCalculation = CedulaApplication.calculateTax(
            parseFloat(specificData.annual_income || 0),
            parseFloat(specificData.property_assessed_value || 0)
          );
          base_fee = taxCalculation.total_tax;
        }

        // Create main document request
        const requestParams = {
          client_id: clientId,
          document_type_id,
          purpose_category_id,
          purpose_details,
          base_fee,
          additional_fees,
          processing_fee,
          payment_method_id,
          delivery_method,
          delivery_address,
          delivery_fee,
          priority
        };

        // Use the model's create method but within transaction
        console.log('Service: Creating document request with params:', requestParams);
        const docRequest = await DocumentRequestService.createDocumentRequestInTransaction(connection, requestParams);

        // Create document-specific application
        if (documentType.type_name === 'Barangay Clearance') {
          console.log('Service: Creating Barangay Clearance application');
          await DocumentRequestService.createBarangayClearanceApplicationInTransaction(connection, {
            request_id: docRequest.id,
            has_pending_cases: specificData.has_pending_cases || false,
            pending_cases_details: specificData.pending_cases_details || null,
            voter_registration_number: specificData.voter_registration_number || null,
            precinct_number: specificData.precinct_number || null,
            emergency_contact_name: specificData.emergency_contact_name || null,
            emergency_contact_relationship: specificData.emergency_contact_relationship || null,
            emergency_contact_phone: specificData.emergency_contact_phone || null,
            emergency_contact_address: specificData.emergency_contact_address || null
          });
        } else if (documentType.type_name === 'Cedula') {
          console.log('Service: Creating Cedula application');
          await DocumentRequestService.createCedulaApplicationInTransaction(connection, {
            request_id: docRequest.id,
            occupation: specificData.occupation || specificData.income_source || 'Not specified',
            employer_name: specificData.employer_name || null,
            employer_address: specificData.employer_address || null,
            monthly_income: specificData.monthly_income || 0,
            annual_income: specificData.annual_income || 0,
            business_name: specificData.business_name || null,
            business_address: specificData.business_address || null,
            business_type: specificData.business_type || specificData.business_nature || null,
            business_income: specificData.business_income || 0,
            has_real_property: specificData.has_real_property || false,
            property_assessed_value: specificData.property_assessed_value || specificData.property_value || 0,
            property_location: specificData.property_location || null,
            tin_number: specificData.tin_number || null,
            previous_ctc_number: specificData.previous_ctc_number || null,
            previous_ctc_date_issued: specificData.previous_ctc_date_issued || null,
            previous_ctc_place_issued: specificData.previous_ctc_place_issued || null,
            computed_tax: base_fee
          });
        }

        logger.info('Document request submitted successfully', {
          requestId: docRequest.id,
          requestNumber: docRequest.request_number,
          clientId,
          documentType: documentType.type_name
        });

        return docRequest;

      } catch (error) {
        logger.error('Error in document request submission transaction', {
          error: error.message,
          clientId
        });
        throw error;
      }
    };

    try {
      const docRequest = await executeTransactionCallback(transaction);
      
      return {
        success: true,
        data: docRequest,
        message: 'Document request submitted successfully'
      };
    } catch (error) {
      logger.error('Error submitting document request', {
        error: error.message,
        clientId
      });
      throw new Error('Failed to submit document request');
    }
  }

  // Helper method to create document request within transaction
  static async createDocumentRequestInTransaction(connection, requestData) {
    console.log('Service: createDocumentRequestInTransaction called with:', requestData);
    const {
      client_id, document_type_id, purpose_category_id, purpose_details,
      base_fee, additional_fees, processing_fee, payment_method_id,
      delivery_method, delivery_address, delivery_fee, priority
    } = requestData;

    // Generate request number
    console.log('Service: Generating request number...');
    const documentTypeQuery = 'SELECT type_name FROM document_types WHERE id = ?';
    const docTypeResults = await connection.execute(documentTypeQuery, [document_type_id]);
    const docTypeCode = docTypeResults[0][0].type_name === 'Cedula' ? 'CED' : 'BC';
    console.log('Service: Document type code:', docTypeCode);

    const requestNumberQuery = 'SELECT GenerateRequestNumber(?) as request_number';
    const requestNumberResults = await connection.execute(requestNumberQuery, [docTypeCode]);
    const request_number = requestNumberResults[0][0].request_number;
    console.log('Service: Generated request number:', request_number);

    const query = `
      INSERT INTO document_requests (
        request_number, client_id, document_type_id, purpose_category_id, 
        purpose_details, status_id, priority, base_fee, additional_fees, 
        processing_fee, payment_method_id, delivery_method, delivery_address, 
        delivery_fee
      ) VALUES (?, ?, ?, ?, ?, 1, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      request_number,
      client_id,
      document_type_id,
      purpose_category_id,
      purpose_details,
      priority || 'normal',
      base_fee || 0,
      additional_fees || 0,
      processing_fee || 0,
      payment_method_id || null,
      delivery_method || 'pickup',
      delivery_address || null,
      delivery_fee || 0
    ];

    console.log('Service: Executing INSERT query with params:', params);
    console.log('Service: Checking for undefined values:', {
      request_number: typeof request_number,
      client_id: typeof client_id,
      document_type_id: typeof document_type_id,
      purpose_category_id: typeof purpose_category_id,
      purpose_details: typeof purpose_details,
      priority: typeof priority,
      base_fee: typeof base_fee,
      additional_fees: typeof additional_fees,
      processing_fee: typeof processing_fee,
      payment_method_id: typeof payment_method_id,
      delivery_method: typeof delivery_method,
      delivery_address: typeof delivery_address,
      delivery_fee: typeof delivery_fee
    });

    const result = await connection.execute(query, params);
    console.log('Service: INSERT result:', result[0]);
    
    // Return the created request data
    return {
      id: result[0].insertId,
      request_number,
      client_id,
      document_type_id,
      purpose_category_id,
      purpose_details,
      status_id: 1,
      priority,
      base_fee,
      additional_fees,
      processing_fee,
      payment_method_id,
      delivery_method,
      delivery_address,
      delivery_fee
    };
  }

  // Helper method to create barangay clearance application within transaction
  static async createBarangayClearanceApplicationInTransaction(connection, applicationData) {
    console.log('Service: createBarangayClearanceApplicationInTransaction called with:', applicationData);
    const {
      request_id, has_pending_cases, pending_cases_details,
      voter_registration_number, precinct_number, emergency_contact_name,
      emergency_contact_relationship, emergency_contact_phone, emergency_contact_address
    } = applicationData;

    const query = `
      INSERT INTO barangay_clearance_applications (
        request_id, has_pending_cases, pending_cases_details, 
        voter_registration_number, precinct_number, emergency_contact_name,
        emergency_contact_relationship, emergency_contact_phone, emergency_contact_address
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      request_id,
      has_pending_cases || false,
      pending_cases_details || null,
      voter_registration_number || null,
      precinct_number || null,
      emergency_contact_name || null,
      emergency_contact_relationship || null,
      emergency_contact_phone || null,
      emergency_contact_address || null
    ];

    console.log('Service: Executing Barangay Clearance INSERT with params:', params);
    await connection.execute(query, params);
    console.log('Service: Barangay Clearance application created successfully');
  }

  // Helper method to create cedula application within transaction
  static async createCedulaApplicationInTransaction(connection, applicationData) {
    console.log('Service: createCedulaApplicationInTransaction called with:', applicationData);

    const {
      request_id, occupation, employer_name, employer_address,
      monthly_income, annual_income, business_name, business_address,
      business_type, business_income, has_real_property, property_assessed_value,
      property_location, tin_number, previous_ctc_number, previous_ctc_date_issued,
      previous_ctc_place_issued, computed_tax
    } = applicationData;

    const query = `
      INSERT INTO cedula_applications (
        request_id, occupation, employer_name, employer_address,
        monthly_income, annual_income, business_name, business_address,
        business_type, business_income, has_real_property, property_assessed_value,
        property_location, tin_number, previous_ctc_number, previous_ctc_date_issued,
        previous_ctc_place_issued, computed_tax
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const params = [
      request_id, occupation || null, employer_name || null, employer_address || null,
      monthly_income || 0, annual_income || 0, business_name || null, business_address || null,
      business_type || null, business_income || 0, has_real_property || false, property_assessed_value || 0,
      property_location || null, tin_number || null, previous_ctc_number || null, previous_ctc_date_issued || null,
      previous_ctc_place_issued || null, computed_tax || 0
    ];

    console.log('Service: Executing Cedula INSERT with params:', params);

    try {
      await connection.execute(query, params);
      console.log('Service: Cedula application created successfully');
    } catch (error) {
      console.error('Service: Error creating Cedula application:', error);
      throw error;
    }
  }

  // Get client's requests
  static async getClientRequests(clientId, filters = {}) {
    try {
      const result = await DocumentRequest.getClientRequests(clientId, filters);
      
      logger.info('Client requests retrieved successfully', {
        clientId,
        count: result.requests.length,
        page: result.pagination.page
      });
      
      return {
        success: true,
        data: result.requests,
        pagination: result.pagination,
        message: 'Requests retrieved successfully'
      };
    } catch (error) {
      logger.error('Error retrieving client requests', {
        error: error.message,
        clientId
      });
      throw new Error('Failed to retrieve requests');
    }
  }

  // Get request details
  static async getRequestDetails(requestId, clientId = null) {
    try {
      const request = await DocumentRequest.getRequestDetails(requestId, clientId);
      
      if (!request) {
        throw new Error('Request not found');
      }
      
      logger.info('Request details retrieved successfully', {
        requestId,
        clientId
      });
      
      return {
        success: true,
        data: request,
        message: 'Request details retrieved successfully'
      };
    } catch (error) {
      logger.error('Error retrieving request details', {
        error: error.message,
        requestId,
        clientId
      });
      throw error;
    }
  }

  // Cancel request
  static async cancelRequest(requestId, clientId, reason = null) {
    try {
      const request = await DocumentRequest.findById(requestId);
      
      if (!request) {
        throw new Error('Request not found');
      }

      // Verify ownership if clientId is provided
      if (clientId && request.client_id !== clientId) {
        throw new Error('Unauthorized to cancel this request');
      }

      // Check if request can be cancelled
      const cancellableStatuses = [1, 2]; // pending, under_review
      if (!cancellableStatuses.includes(request.status_id)) {
        throw new Error('Request cannot be cancelled at this stage');
      }

      // Update status to cancelled (status_id = 8)
      await request.updateStatus(8, clientId || null, reason);
      
      logger.info('Request cancelled successfully', {
        requestId,
        clientId,
        reason
      });
      
      return {
        success: true,
        data: { id: requestId, status: 'cancelled' },
        message: 'Request cancelled successfully'
      };
    } catch (error) {
      logger.error('Error cancelling request', {
        error: error.message,
        requestId,
        clientId
      });
      throw error;
    }
  }

  // Get request status history
  static async getRequestHistory(requestId, clientId = null) {
    try {
      // Verify request exists and ownership if clientId provided
      if (clientId) {
        const request = await DocumentRequest.findById(requestId);
        if (!request || request.client_id !== clientId) {
          throw new Error('Request not found');
        }
      }

      const history = await DocumentRequest.getStatusHistory(requestId);
      
      logger.info('Request history retrieved successfully', {
        requestId,
        clientId,
        historyCount: history.length
      });
      
      return {
        success: true,
        data: history,
        message: 'Request history retrieved successfully'
      };
    } catch (error) {
      logger.error('Error retrieving request history', {
        error: error.message,
        requestId,
        clientId
      });
      throw error;
    }
  }
}

module.exports = DocumentRequestService;
