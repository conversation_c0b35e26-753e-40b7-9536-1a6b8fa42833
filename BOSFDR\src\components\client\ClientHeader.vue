<template>
  <header class="dashboard-header" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
    <div class="header-content">
      <!-- Left Section -->
      <div class="header-left">
        <button class="sidebar-toggle" @click="handleSidebarToggle">
          <i class="fas fa-bars"></i>
        </button>
        <div class="page-title">
          <h1>{{ getPageTitle() }}</h1>
        </div>
      </div>

      <!-- Header Actions -->
      <div class="header-actions">
        <!-- Notifications -->
        <ClientNotifications
          @new-notification="handleNewNotification"
          @notification-click="handleNotificationClick"
          @error="handleNotificationError"
        />

        <!-- User Profile -->
        <div class="user-dropdown" :class="{ active: showUserDropdown }">
          <button class="user-btn" @click="handleUserDropdownToggle">
            <div class="user-avatar">
              <i class="fas fa-user-circle"></i>
            </div>
            <div class="user-info">
              <span class="user-name">{{ userName }}</span>
              <span class="user-role">Client</span>
            </div>
            <i class="fas fa-chevron-down dropdown-arrow"></i>
          </button>
          
          <div v-if="showUserDropdown" class="dropdown-menu">
            <a href="#" class="dropdown-item" @click="handleMenuAction('profile')">
              <i class="fas fa-user me-2"></i>
              My Profile
            </a>
            <a href="#" class="dropdown-item" @click="handleMenuAction('settings')">
              <i class="fas fa-cog me-2"></i>
              Settings
            </a>
            <a href="#" class="dropdown-item" @click="handleMenuAction('account')">
              <i class="fas fa-id-card me-2"></i>
              Account Info
            </a>
            <div class="dropdown-divider"></div>
            <a href="#" class="dropdown-item" @click="handleLogout">
              <i class="fas fa-sign-out-alt me-2"></i>
              Logout
            </a>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script>
import ClientNotifications from './ClientNotifications.vue';

export default {
  name: 'ClientHeader',
  components: {
    ClientNotifications
  },
  props: {
    userName: {
      type: String,
      default: 'User'
    },
    showUserDropdown: {
      type: Boolean,
      default: false
    },
    sidebarCollapsed: {
      type: Boolean,
      default: false
    },
    activeMenu: {
      type: String,
      default: 'dashboard'
    }
  },

  emits: [
    'sidebar-toggle',
    'user-dropdown-toggle',
    'menu-action',
    'logout',
    'error'
  ],

  mounted() {
    // Setup event listeners for outside clicks
    document.addEventListener('click', this.handleOutsideClick);
  },

  beforeUnmount() {
    // Clean up event listeners
    document.removeEventListener('click', this.handleOutsideClick);
  },

  methods: {
    // Get page title based on active menu
    getPageTitle() {
      const titles = {
        'dashboard': 'Dashboard',
        'services': 'Services',
        'requests': 'My Requests',
        'documents': 'Documents',
        'profile': 'Profile',
        'notifications': 'Notifications',
        'help': 'Help & Support'
      };
      return titles[this.activeMenu] || 'Dashboard';
    },

    // Handle sidebar toggle
    handleSidebarToggle() {
      this.$emit('sidebar-toggle');
    },

    // Handle user dropdown toggle
    handleUserDropdownToggle() {
      this.$emit('user-dropdown-toggle');
    },

    // Handle menu actions (profile, settings, etc.)
    handleMenuAction(action) {
      this.$emit('menu-action', action);
    },

    // Handle logout
    handleLogout() {
      this.$emit('logout');
    },

    // Handle outside clicks to close dropdowns
    handleOutsideClick(event) {
      // Check if click is outside user dropdown
      if (!event.target.closest('.user-dropdown')) {
        if (this.showUserDropdown) {
          this.$emit('user-dropdown-toggle');
        }
      }
    },

    // Notification event handlers
    handleNewNotification(notification) {
      console.log('New notification received:', notification);
      // Handle new notification - could show toast, update UI, etc.
    },

    handleNotificationClick(notification) {
      console.log('Notification clicked:', notification);

      // Ensure we have a valid notification object
      if (!notification || typeof notification !== 'object') {
        console.error('Invalid notification object received:', notification);
        return;
      }

      // Handle notification click - could navigate to relevant page
      if (notification.data && notification.data.request_id) {
        // Navigate to request details if it's a request-related notification
        this.$router.push(`/client/requests?highlight=${notification.data.request_id}`);
      }
    },

    handleNotificationError(error) {
      console.error('Notification error:', error);
      this.$emit('error', error);
    }
  }
};
</script>

<style scoped src="./css/clientHeader.css"></style>
