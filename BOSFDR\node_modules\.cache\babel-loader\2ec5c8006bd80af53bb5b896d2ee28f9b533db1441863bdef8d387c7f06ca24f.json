{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, toDisplayString as _toDisplayString, vModelSelect as _vModelSelect, withDirectives as _withDirectives, vModelText as _vModelText, openBlock as _openBlock, createElementBlock as _createElementBlock, Fragment as _Fragment, renderList as _renderList, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"admin-users\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-container\"\n};\nconst _hoisted_3 = {\n  class: \"container-fluid p-4\"\n};\nconst _hoisted_4 = {\n  class: \"row mb-4\"\n};\nconst _hoisted_5 = {\n  class: \"col-12\"\n};\nconst _hoisted_6 = {\n  class: \"d-flex justify-content-between align-items-center flex-wrap\"\n};\nconst _hoisted_7 = {\n  class: \"d-flex gap-2\"\n};\nconst _hoisted_8 = [\"disabled\"];\nconst _hoisted_9 = {\n  class: \"row mb-4\"\n};\nconst _hoisted_10 = {\n  class: \"col-md-3 mb-3\"\n};\nconst _hoisted_11 = {\n  class: \"card border-left-primary shadow h-100 py-2\"\n};\nconst _hoisted_12 = {\n  class: \"card-body\"\n};\nconst _hoisted_13 = {\n  class: \"row no-gutters align-items-center\"\n};\nconst _hoisted_14 = {\n  class: \"col mr-2\"\n};\nconst _hoisted_15 = {\n  class: \"h5 mb-0 font-weight-bold text-gray-800\"\n};\nconst _hoisted_16 = {\n  class: \"col-md-3 mb-3\"\n};\nconst _hoisted_17 = {\n  class: \"card border-left-success shadow h-100 py-2\"\n};\nconst _hoisted_18 = {\n  class: \"card-body\"\n};\nconst _hoisted_19 = {\n  class: \"row no-gutters align-items-center\"\n};\nconst _hoisted_20 = {\n  class: \"col mr-2\"\n};\nconst _hoisted_21 = {\n  class: \"h5 mb-0 font-weight-bold text-gray-800\"\n};\nconst _hoisted_22 = {\n  class: \"col-md-3 mb-3\"\n};\nconst _hoisted_23 = {\n  class: \"card border-left-warning shadow h-100 py-2\"\n};\nconst _hoisted_24 = {\n  class: \"card-body\"\n};\nconst _hoisted_25 = {\n  class: \"row no-gutters align-items-center\"\n};\nconst _hoisted_26 = {\n  class: \"col mr-2\"\n};\nconst _hoisted_27 = {\n  class: \"h5 mb-0 font-weight-bold text-gray-800\"\n};\nconst _hoisted_28 = {\n  class: \"col-md-3 mb-3\"\n};\nconst _hoisted_29 = {\n  class: \"card border-left-info shadow h-100 py-2\"\n};\nconst _hoisted_30 = {\n  class: \"card-body\"\n};\nconst _hoisted_31 = {\n  class: \"row no-gutters align-items-center\"\n};\nconst _hoisted_32 = {\n  class: \"col mr-2\"\n};\nconst _hoisted_33 = {\n  class: \"h5 mb-0 font-weight-bold text-gray-800\"\n};\nconst _hoisted_34 = {\n  class: \"row\"\n};\nconst _hoisted_35 = {\n  class: \"col-12\"\n};\nconst _hoisted_36 = {\n  class: \"card shadow\"\n};\nconst _hoisted_37 = {\n  class: \"card-header py-3 d-flex justify-content-between align-items-center\"\n};\nconst _hoisted_38 = {\n  class: \"d-flex gap-2\"\n};\nconst _hoisted_39 = {\n  class: \"card-body\"\n};\nconst _hoisted_40 = {\n  class: \"row mb-3\"\n};\nconst _hoisted_41 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_42 = {\n  class: \"input-group\"\n};\nconst _hoisted_43 = {\n  class: \"col-md-6 text-end\"\n};\nconst _hoisted_44 = {\n  class: \"text-muted\"\n};\nconst _hoisted_45 = {\n  key: 0,\n  class: \"text-center py-4\"\n};\nconst _hoisted_46 = {\n  class: \"text-center py-5\"\n};\nconst _hoisted_47 = {\n  class: \"text-muted\"\n};\nconst _hoisted_48 = {\n  class: \"table-responsive\"\n};\nconst _hoisted_49 = {\n  class: \"table table-hover\"\n};\nconst _hoisted_50 = {\n  class: \"d-flex align-items-center\"\n};\nconst _hoisted_51 = {\n  class: \"user-avatar me-3\"\n};\nconst _hoisted_52 = [\"src\", \"alt\"];\nconst _hoisted_53 = {\n  key: 1,\n  class: \"avatar-placeholder rounded-circle\"\n};\nconst _hoisted_54 = {\n  class: \"fw-bold\"\n};\nconst _hoisted_55 = {\n  class: \"text-muted small\"\n};\nconst _hoisted_56 = {\n  class: \"btn-group btn-group-sm\"\n};\nconst _hoisted_57 = [\"onClick\"];\nconst _hoisted_58 = [\"onClick\"];\nconst _hoisted_59 = [\"onClick\", \"title\"];\nconst _hoisted_60 = [\"onClick\"];\nconst _hoisted_61 = {\n  key: 3,\n  class: \"d-flex justify-content-between align-items-center mt-3\"\n};\nconst _hoisted_62 = {\n  class: \"text-muted\"\n};\nconst _hoisted_63 = {\n  class: \"pagination pagination-sm mb-0\"\n};\nconst _hoisted_64 = [\"disabled\"];\nconst _hoisted_65 = [\"onClick\"];\nconst _hoisted_66 = [\"disabled\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_AdminHeader = _resolveComponent(\"AdminHeader\");\n  const _component_AdminSidebar = _resolveComponent(\"AdminSidebar\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_AdminHeader, {\n    userName: $data.adminData?.first_name || 'Admin',\n    showUserDropdown: $data.showUserDropdown,\n    sidebarCollapsed: $data.sidebarCollapsed,\n    activeMenu: $options.activeMenu,\n    onSidebarToggle: $options.handleSidebarToggle,\n    onUserDropdownToggle: $options.handleUserDropdownToggle,\n    onMenuAction: $options.handleMenuAction,\n    onLogout: $options.handleLogout\n  }, null, 8 /* PROPS */, [\"userName\", \"showUserDropdown\", \"sidebarCollapsed\", \"activeMenu\", \"onSidebarToggle\", \"onUserDropdownToggle\", \"onMenuAction\", \"onLogout\"]), _createCommentVNode(\" Mobile Overlay \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"mobile-overlay\", {\n      active: !$data.sidebarCollapsed && $data.isMobile\n    }]),\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.closeMobileSidebar && $options.closeMobileSidebar(...args))\n  }, null, 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_AdminSidebar, {\n    collapsed: $data.sidebarCollapsed,\n    activeMenu: $options.activeMenu,\n    onMenuChange: $options.handleMenuChange,\n    onLogout: $options.handleLogout,\n    onToggleSidebar: $options.handleSidebarToggle\n  }, null, 8 /* PROPS */, [\"collapsed\", \"activeMenu\", \"onMenuChange\", \"onLogout\", \"onToggleSidebar\"]), _createElementVNode(\"main\", {\n    class: _normalizeClass([\"main-content\", {\n      'sidebar-collapsed': $data.sidebarCollapsed\n    }])\n  }, [_createElementVNode(\"div\", _hoisted_3, [_createCommentVNode(\" Page Header \"), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"button\", {\n    class: \"btn btn-outline-success btn-sm\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.loadUsers && $options.loadUsers(...args)),\n    disabled: $data.loading\n  }, [_createElementVNode(\"i\", {\n    class: _normalizeClass([\"fas fa-sync-alt me-1\", {\n      'fa-spin': $data.loading\n    }])\n  }, null, 2 /* CLASS */), _cache[11] || (_cache[11] = _createTextVNode(\" Refresh \"))], 8 /* PROPS */, _hoisted_8), _createElementVNode(\"button\", {\n    class: \"btn btn-success btn-sm\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.showAddUserModal && $options.showAddUserModal(...args))\n  }, _cache[12] || (_cache[12] = [_createElementVNode(\"i\", {\n    class: \"fas fa-user-plus me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Add User \")]))])])])]), _createCommentVNode(\" User Statistics \"), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_cache[13] || (_cache[13] = _createElementVNode(\"div\", {\n    class: \"text-xs font-weight-bold text-primary text-uppercase mb-1\"\n  }, \" Total Users \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_15, _toDisplayString($data.userStats.total || 0), 1 /* TEXT */)]), _cache[14] || (_cache[14] = _createElementVNode(\"div\", {\n    class: \"col-auto\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-users fa-2x text-gray-300\"\n  })], -1 /* HOISTED */))])])])]), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n    class: \"text-xs font-weight-bold text-success text-uppercase mb-1\"\n  }, \" Active Users \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_21, _toDisplayString($data.userStats.active || 0), 1 /* TEXT */)]), _cache[16] || (_cache[16] = _createElementVNode(\"div\", {\n    class: \"col-auto\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user-check fa-2x text-gray-300\"\n  })], -1 /* HOISTED */))])])])]), _createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"div\", _hoisted_26, [_cache[17] || (_cache[17] = _createElementVNode(\"div\", {\n    class: \"text-xs font-weight-bold text-warning text-uppercase mb-1\"\n  }, \" Pending Verification \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_27, _toDisplayString($data.userStats.pending || 0), 1 /* TEXT */)]), _cache[18] || (_cache[18] = _createElementVNode(\"div\", {\n    class: \"col-auto\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user-clock fa-2x text-gray-300\"\n  })], -1 /* HOISTED */))])])])]), _createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"div\", _hoisted_30, [_createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, [_cache[19] || (_cache[19] = _createElementVNode(\"div\", {\n    class: \"text-xs font-weight-bold text-info text-uppercase mb-1\"\n  }, \" Admin Users \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_33, _toDisplayString($data.userStats.admins || 0), 1 /* TEXT */)]), _cache[20] || (_cache[20] = _createElementVNode(\"div\", {\n    class: \"col-auto\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user-shield fa-2x text-gray-300\"\n  })], -1 /* HOISTED */))])])])])]), _createCommentVNode(\" Users Table \"), _createElementVNode(\"div\", _hoisted_34, [_createElementVNode(\"div\", _hoisted_35, [_createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"div\", _hoisted_37, [_cache[23] || (_cache[23] = _createElementVNode(\"h6\", {\n    class: \"m-0 font-weight-bold text-primary\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-users me-2\"\n  }), _createTextVNode(\" User List \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_38, [_withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select form-select-sm\",\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $data.filterStatus = $event),\n    onChange: _cache[4] || (_cache[4] = (...args) => $options.filterUsers && $options.filterUsers(...args))\n  }, _cache[21] || (_cache[21] = [_createStaticVNode(\"<option value=\\\"\\\" data-v-f270332e>All Status</option><option value=\\\"active\\\" data-v-f270332e>Active</option><option value=\\\"inactive\\\" data-v-f270332e>Inactive</option><option value=\\\"pending\\\" data-v-f270332e>Pending</option><option value=\\\"suspended\\\" data-v-f270332e>Suspended</option>\", 5)]), 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.filterStatus]]), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select form-select-sm\",\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.filterType = $event),\n    onChange: _cache[6] || (_cache[6] = (...args) => $options.filterUsers && $options.filterUsers(...args))\n  }, _cache[22] || (_cache[22] = [_createElementVNode(\"option\", {\n    value: \"\"\n  }, \"All Types\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"client\"\n  }, \"Clients\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"admin\"\n  }, \"Admins\", -1 /* HOISTED */)]), 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.filterType]])])]), _createElementVNode(\"div\", _hoisted_39, [_createCommentVNode(\" Search Bar \"), _createElementVNode(\"div\", _hoisted_40, [_createElementVNode(\"div\", _hoisted_41, [_createElementVNode(\"div\", _hoisted_42, [_cache[24] || (_cache[24] = _createElementVNode(\"span\", {\n    class: \"input-group-text\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-search\"\n  })], -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    placeholder: \"Search users by name, email, or username...\",\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.searchQuery = $event),\n    onInput: _cache[8] || (_cache[8] = (...args) => $options.searchUsers && $options.searchUsers(...args))\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $data.searchQuery]])])]), _createElementVNode(\"div\", _hoisted_43, [_createElementVNode(\"span\", _hoisted_44, \" Showing \" + _toDisplayString($data.filteredUsers.length) + \" of \" + _toDisplayString($data.users.length) + \" users \", 1 /* TEXT */)])]), _createCommentVNode(\" Loading State \"), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_45, _cache[25] || (_cache[25] = [_createElementVNode(\"div\", {\n    class: \"spinner-border text-primary\",\n    role: \"status\"\n  }, [_createElementVNode(\"span\", {\n    class: \"visually-hidden\"\n  }, \"Loading...\")], -1 /* HOISTED */), _createElementVNode(\"p\", {\n    class: \"text-muted mt-2\"\n  }, \"Loading users...\", -1 /* HOISTED */)]))) : $data.filteredUsers.length === 0 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" Empty State \"), _createElementVNode(\"div\", _hoisted_46, [_cache[26] || (_cache[26] = _createElementVNode(\"i\", {\n    class: \"fas fa-users fa-3x text-gray-300 mb-3\"\n  }, null, -1 /* HOISTED */)), _cache[27] || (_cache[27] = _createElementVNode(\"h5\", {\n    class: \"text-gray-600\"\n  }, \"No users found\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_47, _toDisplayString($data.searchQuery ? 'Try adjusting your search criteria.' : 'No users have been registered yet.'), 1 /* TEXT */)])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createCommentVNode(\" Users Table \"), _createElementVNode(\"div\", _hoisted_48, [_createElementVNode(\"table\", _hoisted_49, [_cache[31] || (_cache[31] = _createElementVNode(\"thead\", {\n    class: \"table-light\"\n  }, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, \"User\"), _createElementVNode(\"th\", null, \"Email\"), _createElementVNode(\"th\", null, \"Type\"), _createElementVNode(\"th\", null, \"Status\"), _createElementVNode(\"th\", null, \"Registered\"), _createElementVNode(\"th\", null, \"Actions\")])], -1 /* HOISTED */)), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.paginatedUsers, user => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: user.id\n    }, [_createElementVNode(\"td\", null, [_createElementVNode(\"div\", _hoisted_50, [_createElementVNode(\"div\", _hoisted_51, [user.profile_picture ? (_openBlock(), _createElementBlock(\"img\", {\n      key: 0,\n      src: user.profile_picture,\n      alt: user.full_name,\n      class: \"rounded-circle\"\n    }, null, 8 /* PROPS */, _hoisted_52)) : (_openBlock(), _createElementBlock(\"div\", _hoisted_53, _toDisplayString($options.getInitials(user.full_name)), 1 /* TEXT */))]), _createElementVNode(\"div\", null, [_createElementVNode(\"div\", _hoisted_54, _toDisplayString(user.full_name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_55, \"@\" + _toDisplayString(user.username), 1 /* TEXT */)])])]), _createElementVNode(\"td\", null, _toDisplayString(user.email), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"span\", {\n      class: _normalizeClass([\"badge\", user.type === 'admin' ? 'bg-primary' : 'bg-info'])\n    }, _toDisplayString(user.type === 'admin' ? 'Admin' : 'Client'), 3 /* TEXT, CLASS */)]), _createElementVNode(\"td\", null, [_createElementVNode(\"span\", {\n      class: _normalizeClass([\"badge\", $options.getStatusBadgeClass(user.status)])\n    }, _toDisplayString($options.formatStatus(user.status)), 3 /* TEXT, CLASS */)]), _createElementVNode(\"td\", null, _toDisplayString($options.formatDate(user.created_at)), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"div\", _hoisted_56, [_createElementVNode(\"button\", {\n      class: \"btn btn-outline-primary\",\n      onClick: $event => $options.viewUser(user),\n      title: \"View Details\"\n    }, [...(_cache[28] || (_cache[28] = [_createElementVNode(\"i\", {\n      class: \"fas fa-eye\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_57), _createElementVNode(\"button\", {\n      class: \"btn btn-outline-warning\",\n      onClick: $event => $options.editUser(user),\n      title: \"Edit User\"\n    }, [...(_cache[29] || (_cache[29] = [_createElementVNode(\"i\", {\n      class: \"fas fa-edit\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_58), _createElementVNode(\"button\", {\n      class: _normalizeClass([\"btn\", user.status === 'active' ? 'btn-outline-warning' : 'btn-outline-success']),\n      onClick: $event => $options.toggleUserStatus(user),\n      title: user.status === 'active' ? 'Suspend User' : 'Activate User'\n    }, [_createElementVNode(\"i\", {\n      class: _normalizeClass(user.status === 'active' ? 'fas fa-pause' : 'fas fa-play')\n    }, null, 2 /* CLASS */)], 10 /* CLASS, PROPS */, _hoisted_59), _createElementVNode(\"button\", {\n      class: \"btn btn-outline-danger\",\n      onClick: $event => $options.deleteUser(user),\n      title: \"Delete User\"\n    }, [...(_cache[30] || (_cache[30] = [_createElementVNode(\"i\", {\n      class: \"fas fa-trash\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_60)])])]);\n  }), 128 /* KEYED_FRAGMENT */))])])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" Pagination \"), $options.totalPages > 1 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_61, [_createElementVNode(\"div\", _hoisted_62, \" Page \" + _toDisplayString($data.currentPage) + \" of \" + _toDisplayString($options.totalPages), 1 /* TEXT */), _createElementVNode(\"nav\", null, [_createElementVNode(\"ul\", _hoisted_63, [_createElementVNode(\"li\", {\n    class: _normalizeClass([\"page-item\", {\n      disabled: $data.currentPage === 1\n    }])\n  }, [_createElementVNode(\"button\", {\n    class: \"page-link\",\n    onClick: _cache[9] || (_cache[9] = $event => $options.changePage($data.currentPage - 1)),\n    disabled: $data.currentPage === 1\n  }, \" Previous \", 8 /* PROPS */, _hoisted_64)], 2 /* CLASS */), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.visiblePages, page => {\n    return _openBlock(), _createElementBlock(\"li\", {\n      key: page,\n      class: _normalizeClass([\"page-item\", {\n        active: page === $data.currentPage\n      }])\n    }, [_createElementVNode(\"button\", {\n      class: \"page-link\",\n      onClick: $event => $options.changePage(page)\n    }, _toDisplayString(page), 9 /* TEXT, PROPS */, _hoisted_65)], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */)), _createElementVNode(\"li\", {\n    class: _normalizeClass([\"page-item\", {\n      disabled: $data.currentPage === $options.totalPages\n    }])\n  }, [_createElementVNode(\"button\", {\n    class: \"page-link\",\n    onClick: _cache[10] || (_cache[10] = $event => $options.changePage($data.currentPage + 1)),\n    disabled: $data.currentPage === $options.totalPages\n  }, \" Next \", 8 /* PROPS */, _hoisted_66)], 2 /* CLASS */)])])])) : _createCommentVNode(\"v-if\", true)])])])])])], 2 /* CLASS */)])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_AdminHeader", "userName", "$data", "adminData", "first_name", "showUserDropdown", "sidebarCollapsed", "activeMenu", "$options", "onSidebarToggle", "handleSidebarToggle", "onUserDropdownToggle", "handleUserDropdownToggle", "onMenuAction", "handleMenuAction", "onLogout", "handleLogout", "_createCommentVNode", "_createElementVNode", "_normalizeClass", "active", "isMobile", "onClick", "_cache", "args", "closeMobileSidebar", "_hoisted_2", "_component_AdminSidebar", "collapsed", "onMenuChange", "handleMenuChange", "onToggleSidebar", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "loadUsers", "disabled", "loading", "showAddUserModal", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_toDisplayString", "userStats", "total", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "pending", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_hoisted_31", "_hoisted_32", "_hoisted_33", "admins", "_hoisted_34", "_hoisted_35", "_hoisted_36", "_hoisted_37", "_hoisted_38", "filterStatus", "$event", "onChange", "filterUsers", "filterType", "value", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "type", "placeholder", "searchQuery", "onInput", "searchUsers", "_hoisted_43", "_hoisted_44", "filteredUsers", "length", "users", "_hoisted_45", "role", "_Fragment", "key", "_hoisted_46", "_hoisted_47", "_hoisted_48", "_hoisted_49", "_renderList", "paginatedUsers", "user", "id", "_hoisted_50", "_hoisted_51", "profile_picture", "src", "alt", "full_name", "_hoisted_53", "getInitials", "_hoisted_54", "_hoisted_55", "username", "email", "getStatusBadgeClass", "status", "formatStatus", "formatDate", "created_at", "_hoisted_56", "viewUser", "title", "editUser", "toggleUserStatus", "deleteUser", "totalPages", "_hoisted_61", "_hoisted_62", "currentPage", "_hoisted_63", "changePage", "_hoisted_64", "visiblePages", "page", "_hoisted_65", "_hoisted_66"], "sources": ["D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminUsers.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-users\">\n    <AdminHeader\n      :userName=\"adminData?.first_name || 'Admin'\"\n      :showUserDropdown=\"showUserDropdown\"\n      :sidebarCollapsed=\"sidebarCollapsed\"\n      :activeMenu=\"activeMenu\"\n      @sidebar-toggle=\"handleSidebarToggle\"\n      @user-dropdown-toggle=\"handleUserDropdownToggle\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"handleLogout\"\n    />\n\n    <!-- Mobile Overlay -->\n    <div\n      class=\"mobile-overlay\"\n      :class=\"{ active: !sidebarCollapsed && isMobile }\"\n      @click=\"closeMobileSidebar\"\n    ></div>\n\n    <div class=\"dashboard-container\">\n      <AdminSidebar\n        :collapsed=\"sidebarCollapsed\"\n        :activeMenu=\"activeMenu\"\n        @menu-change=\"handleMenuChange\"\n        @logout=\"handleLogout\"\n        @toggle-sidebar=\"handleSidebarToggle\"\n      />\n\n      <main class=\"main-content\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n        <div class=\"container-fluid p-4\">\n          <!-- Page Header -->\n          <div class=\"row mb-4\">\n            <div class=\"col-12\">\n              <div class=\"d-flex justify-content-between align-items-center flex-wrap\">\n\n                <div class=\"d-flex gap-2\">\n                  <button class=\"btn btn-outline-success btn-sm\" @click=\"loadUsers\" :disabled=\"loading\">\n                    <i class=\"fas fa-sync-alt me-1\" :class=\"{ 'fa-spin': loading }\"></i>\n                    Refresh\n                  </button>\n                  <button class=\"btn btn-success btn-sm\" @click=\"showAddUserModal\">\n                    <i class=\"fas fa-user-plus me-1\"></i>\n                    Add User\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- User Statistics -->\n          <div class=\"row mb-4\">\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-primary shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-primary text-uppercase mb-1\">\n                        Total Users\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.total || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-users fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-success shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-success text-uppercase mb-1\">\n                        Active Users\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.active || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-user-check fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-warning shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-warning text-uppercase mb-1\">\n                        Pending Verification\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.pending || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-user-clock fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-info shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-info text-uppercase mb-1\">\n                        Admin Users\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.admins || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-user-shield fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Users Table -->\n          <div class=\"row\">\n            <div class=\"col-12\">\n              <div class=\"card shadow\">\n                <div class=\"card-header py-3 d-flex justify-content-between align-items-center\">\n                  <h6 class=\"m-0 font-weight-bold text-primary\">\n                    <i class=\"fas fa-users me-2\"></i>\n                    User List\n                  </h6>\n                  <div class=\"d-flex gap-2\">\n                    <select class=\"form-select form-select-sm\" v-model=\"filterStatus\" @change=\"filterUsers\">\n                      <option value=\"\">All Status</option>\n                      <option value=\"active\">Active</option>\n                      <option value=\"inactive\">Inactive</option>\n                      <option value=\"pending\">Pending</option>\n                      <option value=\"suspended\">Suspended</option>\n                    </select>\n                    <select class=\"form-select form-select-sm\" v-model=\"filterType\" @change=\"filterUsers\">\n                      <option value=\"\">All Types</option>\n                      <option value=\"client\">Clients</option>\n                      <option value=\"admin\">Admins</option>\n                    </select>\n                  </div>\n                </div>\n                <div class=\"card-body\">\n                  <!-- Search Bar -->\n                  <div class=\"row mb-3\">\n                    <div class=\"col-md-6\">\n                      <div class=\"input-group\">\n                        <span class=\"input-group-text\">\n                          <i class=\"fas fa-search\"></i>\n                        </span>\n                        <input\n                          type=\"text\"\n                          class=\"form-control\"\n                          placeholder=\"Search users by name, email, or username...\"\n                          v-model=\"searchQuery\"\n                          @input=\"searchUsers\"\n                        >\n                      </div>\n                    </div>\n                    <div class=\"col-md-6 text-end\">\n                      <span class=\"text-muted\">\n                        Showing {{ filteredUsers.length }} of {{ users.length }} users\n                      </span>\n                    </div>\n                  </div>\n\n                  <!-- Loading State -->\n                  <div v-if=\"loading\" class=\"text-center py-4\">\n                    <div class=\"spinner-border text-primary\" role=\"status\">\n                      <span class=\"visually-hidden\">Loading...</span>\n                    </div>\n                    <p class=\"text-muted mt-2\">Loading users...</p>\n                  </div>\n\n                  <!-- Empty State -->\n                  <div v-else-if=\"filteredUsers.length === 0\" class=\"text-center py-5\">\n                    <i class=\"fas fa-users fa-3x text-gray-300 mb-3\"></i>\n                    <h5 class=\"text-gray-600\">No users found</h5>\n                    <p class=\"text-muted\">\n                      {{ searchQuery ? 'Try adjusting your search criteria.' : 'No users have been registered yet.' }}\n                    </p>\n                  </div>\n\n                  <!-- Users Table -->\n                  <div v-else class=\"table-responsive\">\n                    <table class=\"table table-hover\">\n                      <thead class=\"table-light\">\n                        <tr>\n                          <th>User</th>\n                          <th>Email</th>\n                          <th>Type</th>\n                          <th>Status</th>\n                          <th>Registered</th>\n                          <th>Actions</th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        <tr v-for=\"user in paginatedUsers\" :key=\"user.id\">\n                          <td>\n                            <div class=\"d-flex align-items-center\">\n                              <div class=\"user-avatar me-3\">\n                                <img v-if=\"user.profile_picture\" :src=\"user.profile_picture\" :alt=\"user.full_name\" class=\"rounded-circle\">\n                                <div v-else class=\"avatar-placeholder rounded-circle\">\n                                  {{ getInitials(user.full_name) }}\n                                </div>\n                              </div>\n                              <div>\n                                <div class=\"fw-bold\">{{ user.full_name }}</div>\n                                <div class=\"text-muted small\">@{{ user.username }}</div>\n                              </div>\n                            </div>\n                          </td>\n                          <td>{{ user.email }}</td>\n                          <td>\n                            <span class=\"badge\" :class=\"user.type === 'admin' ? 'bg-primary' : 'bg-info'\">\n                              {{ user.type === 'admin' ? 'Admin' : 'Client' }}\n                            </span>\n                          </td>\n                          <td>\n                            <span class=\"badge\" :class=\"getStatusBadgeClass(user.status)\">\n                              {{ formatStatus(user.status) }}\n                            </span>\n                          </td>\n                          <td>{{ formatDate(user.created_at) }}</td>\n                          <td>\n                            <div class=\"btn-group btn-group-sm\">\n                              <button class=\"btn btn-outline-primary\" @click=\"viewUser(user)\" title=\"View Details\">\n                                <i class=\"fas fa-eye\"></i>\n                              </button>\n                              <button class=\"btn btn-outline-warning\" @click=\"editUser(user)\" title=\"Edit User\">\n                                <i class=\"fas fa-edit\"></i>\n                              </button>\n                              <button\n                                class=\"btn\"\n                                :class=\"user.status === 'active' ? 'btn-outline-warning' : 'btn-outline-success'\"\n                                @click=\"toggleUserStatus(user)\"\n                                :title=\"user.status === 'active' ? 'Suspend User' : 'Activate User'\"\n                              >\n                                <i :class=\"user.status === 'active' ? 'fas fa-pause' : 'fas fa-play'\"></i>\n                              </button>\n                              <button class=\"btn btn-outline-danger\" @click=\"deleteUser(user)\" title=\"Delete User\">\n                                <i class=\"fas fa-trash\"></i>\n                              </button>\n                            </div>\n                          </td>\n                        </tr>\n                      </tbody>\n                    </table>\n                  </div>\n\n                  <!-- Pagination -->\n                  <div v-if=\"totalPages > 1\" class=\"d-flex justify-content-between align-items-center mt-3\">\n                    <div class=\"text-muted\">\n                      Page {{ currentPage }} of {{ totalPages }}\n                    </div>\n                    <nav>\n                      <ul class=\"pagination pagination-sm mb-0\">\n                        <li class=\"page-item\" :class=\"{ disabled: currentPage === 1 }\">\n                          <button class=\"page-link\" @click=\"changePage(currentPage - 1)\" :disabled=\"currentPage === 1\">\n                            Previous\n                          </button>\n                        </li>\n                        <li\n                          v-for=\"page in visiblePages\"\n                          :key=\"page\"\n                          class=\"page-item\"\n                          :class=\"{ active: page === currentPage }\"\n                        >\n                          <button class=\"page-link\" @click=\"changePage(page)\">{{ page }}</button>\n                        </li>\n                        <li class=\"page-item\" :class=\"{ disabled: currentPage === totalPages }\">\n                          <button class=\"page-link\" @click=\"changePage(currentPage + 1)\" :disabled=\"currentPage === totalPages\">\n                            Next\n                          </button>\n                        </li>\n                      </ul>\n                    </nav>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  </div>\n</template>\n\n<script>\nimport AdminHeader from './AdminHeader.vue';\nimport AdminSidebar from './AdminSidebar.vue';\nimport adminAuthService from '@/services/adminAuthService';\nimport userManagementService from '@/services/userManagementService';\n\nexport default {\n  name: 'AdminUsers',\n  components: {\n    AdminHeader,\n    AdminSidebar\n  },\n\n  data() {\n    return {\n      // UI State\n      sidebarCollapsed: false,\n      showUserDropdown: false,\n      isMobile: false,\n      adminData: null,\n      // Component Data\n      users: [],\n      filteredUsers: [],\n      searchQuery: '',\n      filterStatus: '',\n      filterType: '',\n      currentPage: 1,\n      itemsPerPage: 10,\n      loading: false,\n      userStats: {\n        total: 0,\n        active: 0,\n        pending: 0,\n        admins: 0\n      }\n    };\n  },\n\n  computed: {\n    activeMenu() {\n      const path = this.$route.path;\n      if (path.includes('/admin/users')) return 'users';\n      if (path.includes('/admin/requests')) return 'requests';\n      if (path.includes('/admin/reports')) return 'reports';\n      if (path.includes('/admin/settings')) return 'settings';\n      if (path.includes('/admin/activity-logs')) return 'activity';\n      if (path.includes('/admin/profile')) return 'profile';\n      return 'dashboard';\n    },\n\n    paginatedUsers() {\n      const start = (this.currentPage - 1) * this.itemsPerPage;\n      const end = start + this.itemsPerPage;\n      return this.filteredUsers.slice(start, end);\n    },\n\n    totalPages() {\n      return Math.ceil(this.filteredUsers.length / this.itemsPerPage);\n    },\n\n    visiblePages() {\n      const pages = [];\n      const start = Math.max(1, this.currentPage - 2);\n      const end = Math.min(this.totalPages, this.currentPage + 2);\n\n      for (let i = start; i <= end; i++) {\n        pages.push(i);\n      }\n      return pages;\n    }\n  },\n\n  async mounted() {\n    // Check authentication\n    if (!adminAuthService.isLoggedIn()) {\n      this.$router.push('/admin/login');\n      return;\n    }\n\n    // Initialize UI state\n    this.initializeUI();\n\n    // Load component data\n    await this.loadAdminProfile();\n    await this.loadUsers();\n  },\n\n  beforeUnmount() {\n    if (this.handleResize) {\n      window.removeEventListener('resize', this.handleResize);\n    }\n  },\n\n  methods: {\n    // Initialize UI state\n    initializeUI() {\n      this.isMobile = window.innerWidth <= 768;\n\n      // Load saved sidebar state (only on desktop)\n      if (!this.isMobile) {\n        const saved = localStorage.getItem('adminSidebarCollapsed');\n        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n      } else {\n        this.sidebarCollapsed = true; // Always collapsed on mobile\n      }\n\n      // Setup resize listener\n      this.handleResize = () => {\n        const wasMobile = this.isMobile;\n        this.isMobile = window.innerWidth <= 768;\n\n        if (this.isMobile && !wasMobile) {\n          this.sidebarCollapsed = true; // Collapse when switching to mobile\n        } else if (!this.isMobile && wasMobile) {\n          // Restore saved state when switching to desktop\n          const saved = localStorage.getItem('adminSidebarCollapsed');\n          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n        }\n      };\n      window.addEventListener('resize', this.handleResize);\n    },\n\n    // Sidebar toggle\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));\n    },\n\n    // Menu navigation\n    handleMenuChange(menu) {\n      const routes = {\n        'dashboard': '/admin/dashboard',\n        'users': '/admin/users',\n        'requests': '/admin/requests',\n        'reports': '/admin/reports',\n        'settings': '/admin/settings',\n        'activity': '/admin/activity-logs',\n        'profile': '/admin/profile'\n      };\n\n      // Close sidebar on mobile after navigation\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n\n      if (routes[menu]) {\n        this.$router.push(routes[menu]);\n      }\n    },\n\n    // User dropdown toggle\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n\n    // Menu actions\n    handleMenuAction(action) {\n      if (action === 'profile') {\n        this.$router.push('/admin/profile');\n      } else if (action === 'settings') {\n        this.$router.push('/admin/settings');\n      }\n      this.showUserDropdown = false;\n    },\n\n    // Close mobile sidebar\n    closeMobileSidebar() {\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n    },\n\n    // Logout\n    handleLogout() {\n      adminAuthService.logout();\n      this.$router.push('/admin/login');\n    },\n\n    // Load admin profile data\n    async loadAdminProfile() {\n      try {\n        const response = await adminAuthService.getProfile();\n        if (response.success) {\n          this.adminData = response.data;\n        }\n      } catch (error) {\n        console.error('Failed to load admin data:', error);\n        this.adminData = adminAuthService.getAdminData();\n      }\n    },\n\n    // Load users data\n    async loadUsers() {\n      this.loading = true;\n      try {\n        const params = {\n          page: this.currentPage,\n          limit: 50, // Load more for client-side filtering\n          search: this.searchQuery || undefined,\n          role: this.filterType || undefined,\n          is_active: this.filterStatus === 'active' ? true :\n                     this.filterStatus === 'inactive' ? false : undefined\n        };\n\n        const response = await userManagementService.getUsers(params);\n\n        if (response.success) {\n          // Format users for display\n          this.users = response.data.users.map(user =>\n            userManagementService.formatUserData(user)\n          );\n\n          this.filteredUsers = [...this.users];\n          this.calculateStats();\n        } else {\n          throw new Error(response.message || 'Failed to load users');\n        }\n      } catch (error) {\n        console.error('Failed to load users:', error);\n        this.$toast?.error?.(error.message || 'Failed to load users');\n\n        // Fallback to empty state\n        this.users = [];\n        this.filteredUsers = [];\n        this.calculateStats();\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // Calculate user statistics\n    calculateStats() {\n      this.userStats = {\n        total: this.users.length,\n        active: this.users.filter(u => u.status === 'active').length,\n        pending: this.users.filter(u => u.status === 'pending').length,\n        admins: this.users.filter(u => u.type === 'admin').length\n      };\n    },\n\n    // Search users\n    searchUsers() {\n      this.filterUsers();\n    },\n\n    // Filter users based on search and filters\n    filterUsers() {\n      let filtered = [...this.users];\n\n      // Apply search filter\n      if (this.searchQuery) {\n        const query = this.searchQuery.toLowerCase();\n        filtered = filtered.filter(user =>\n          user.full_name.toLowerCase().includes(query) ||\n          user.email.toLowerCase().includes(query) ||\n          user.username.toLowerCase().includes(query)\n        );\n      }\n\n      // Apply status filter\n      if (this.filterStatus) {\n        filtered = filtered.filter(user => user.status === this.filterStatus);\n      }\n\n      // Apply type filter\n      if (this.filterType) {\n        filtered = filtered.filter(user => user.type === this.filterType);\n      }\n\n      this.filteredUsers = filtered;\n      this.currentPage = 1; // Reset to first page\n    },\n\n    // Change page\n    changePage(page) {\n      if (page >= 1 && page <= this.totalPages) {\n        this.currentPage = page;\n      }\n    },\n\n    // Get user initials for avatar\n    getInitials(fullName) {\n      if (!fullName) return '?';\n      return fullName.split(' ').map(name => name.charAt(0)).join('').toUpperCase().slice(0, 2);\n    },\n\n    // Get status badge class\n    getStatusBadgeClass(status) {\n      const classes = {\n        'active': 'bg-success',\n        'inactive': 'bg-secondary',\n        'pending': 'bg-warning',\n        'suspended': 'bg-danger'\n      };\n      return classes[status] || 'bg-secondary';\n    },\n\n    // Format status text\n    formatStatus(status) {\n      return status.charAt(0).toUpperCase() + status.slice(1);\n    },\n\n    // Format date\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    },\n\n    // User actions\n    viewUser(user) {\n      console.log('Viewing user:', user);\n      // Implement user view modal or navigation\n    },\n\n    editUser(user) {\n      console.log('Editing user:', user);\n      // Implement user edit modal or navigation\n    },\n\n    async toggleUserStatus(user) {\n      try {\n        const newStatus = user.status === 'active' ? 'suspended' : 'active';\n        const reason = `Status changed by admin: ${this.adminData?.first_name || 'Admin'}`;\n\n        const response = await userManagementService.updateUserStatus(user.id, newStatus, reason);\n\n        if (response.success) {\n          // Update local data\n          user.status = newStatus;\n          this.calculateStats();\n\n          this.$toast?.success?.(`User ${user.full_name} has been ${newStatus === 'active' ? 'activated' : 'suspended'}.`);\n        } else {\n          throw new Error(response.message || 'Failed to update user status');\n        }\n      } catch (error) {\n        console.error('Failed to update user status:', error);\n        this.$toast?.error?.(error.message || 'Failed to update user status. Please try again.');\n      }\n    },\n\n    async deleteUser(user) {\n      if (!confirm(`Are you sure you want to delete user \"${user.full_name}\"? This action cannot be undone.`)) {\n        return;\n      }\n\n      try {\n        const reason = `User deleted by admin: ${this.adminData?.first_name || 'Admin'}`;\n        const response = await userManagementService.deleteUser(user.id, reason);\n\n        if (response.success) {\n          // Remove from local data\n          const index = this.users.findIndex(u => u.id === user.id);\n          if (index > -1) {\n            this.users.splice(index, 1);\n            this.filterUsers();\n            this.calculateStats();\n          }\n\n          this.$toast?.success?.(`User ${user.full_name} has been deleted.`);\n        } else {\n          throw new Error(response.message || 'Failed to delete user');\n        }\n      } catch (error) {\n        console.error('Failed to delete user:', error);\n        this.$toast?.error?.(error.message || 'Failed to delete user. Please try again.');\n      }\n    },\n\n    showAddUserModal() {\n      console.log('Opening add user modal');\n      // Implement add user modal\n      alert('Add user functionality will be implemented soon.');\n    },\n\n    // Additional user-specific methods can be added here\n    // Navigation handlers are now provided by the mixin\n  }\n};\n</script>\n\n<style scoped>\n@import './css/adminDashboard.css';\n\n/* User avatar styles */\n.user-avatar {\n  width: 40px;\n  height: 40px;\n}\n\n.user-avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.avatar-placeholder {\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: 600;\n  font-size: 0.875rem;\n}\n\n/* Table improvements */\n.table th {\n  border-top: none;\n  font-weight: 600;\n  color: #5a5c69;\n  font-size: 0.875rem;\n}\n\n.table td {\n  vertical-align: middle;\n  font-size: 0.875rem;\n}\n\n.table-hover tbody tr:hover {\n  background-color: rgba(0, 123, 255, 0.05);\n}\n\n/* Button group improvements */\n.btn-group-sm .btn {\n  padding: 0.375rem 0.5rem;\n  font-size: 0.75rem;\n}\n\n/* Search and filter improvements */\n.form-select-sm {\n  font-size: 0.875rem;\n  padding: 0.375rem 0.75rem;\n}\n\n/* Pagination improvements */\n.pagination-sm .page-link {\n  padding: 0.375rem 0.75rem;\n  font-size: 0.875rem;\n}\n\n/* Badge improvements */\n.badge {\n  font-size: 0.75rem;\n  padding: 0.375rem 0.75rem;\n  border-radius: 0.5rem;\n}\n\n/* Responsive improvements */\n@media (max-width: 768px) {\n  .table-responsive {\n    font-size: 0.8rem;\n  }\n\n  .btn-group-sm .btn {\n    padding: 0.25rem 0.375rem;\n    font-size: 0.7rem;\n  }\n\n  .user-avatar {\n    width: 32px;\n    height: 32px;\n  }\n\n  .avatar-placeholder {\n    width: 32px;\n    height: 32px;\n    font-size: 0.75rem;\n  }\n}\n\n@media (max-width: 576px) {\n  .d-flex.gap-2 {\n    flex-direction: column;\n    gap: 0.5rem !important;\n  }\n\n  .btn-group {\n    flex-direction: column;\n  }\n\n  .btn-group .btn {\n    border-radius: 0.375rem !important;\n    margin-bottom: 0.25rem;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAa;;EAmBjBA,KAAK,EAAC;AAAqB;;EAUvBA,KAAK,EAAC;AAAqB;;EAEzBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAQ;;EACZA,KAAK,EAAC;AAA6D;;EAEjEA,KAAK,EAAC;AAAc;;;EAe1BA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA4C;;EAChDA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAAU;;EAIdA,KAAK,EAAC;AAAwC;;EASxDA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA4C;;EAChDA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAAU;;EAIdA,KAAK,EAAC;AAAwC;;EASxDA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA4C;;EAChDA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAAU;;EAIdA,KAAK,EAAC;AAAwC;;EASxDA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAyC;;EAC7CA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAAU;;EAIdA,KAAK,EAAC;AAAwC;;EAY1DA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAQ;;EACZA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAoE;;EAKxEA,KAAK,EAAC;AAAc;;EAetBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAa;;EAarBA,KAAK,EAAC;AAAmB;;EACtBA,KAAK,EAAC;AAAY;;;EAORA,KAAK,EAAC;;;EAQkBA,KAAK,EAAC;AAAkB;;EAG/DA,KAAK,EAAC;AAAY;;EAMXA,KAAK,EAAC;AAAkB;;EAC3BA,KAAK,EAAC;AAAmB;;EAcnBA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAAkB;;;;EAEfA,KAAK,EAAC;;;EAKbA,KAAK,EAAC;AAAS;;EACfA,KAAK,EAAC;AAAkB;;EAiB5BA,KAAK,EAAC;AAAwB;;;;;;;EA0BlBA,KAAK,EAAC;;;EAC1BA,KAAK,EAAC;AAAY;;EAIjBA,KAAK,EAAC;AAA+B;;;;;;;uBAnQ7DC,mBAAA,CAgSM,OAhSNC,UAgSM,GA/RJC,YAAA,CASEC,sBAAA;IARCC,QAAQ,EAAEC,KAAA,CAAAC,SAAS,EAAEC,UAAU;IAC/BC,gBAAgB,EAAEH,KAAA,CAAAG,gBAAgB;IAClCC,gBAAgB,EAAEJ,KAAA,CAAAI,gBAAgB;IAClCC,UAAU,EAAEC,QAAA,CAAAD,UAAU;IACtBE,eAAc,EAAED,QAAA,CAAAE,mBAAmB;IACnCC,oBAAoB,EAAEH,QAAA,CAAAI,wBAAwB;IAC9CC,YAAW,EAAEL,QAAA,CAAAM,gBAAgB;IAC7BC,QAAM,EAAEP,QAAA,CAAAQ;sKAGXC,mBAAA,oBAAuB,EACvBC,mBAAA,CAIO;IAHLtB,KAAK,EAAAuB,eAAA,EAAC,gBAAgB;MAAAC,MAAA,GACHlB,KAAA,CAAAI,gBAAgB,IAAIJ,KAAA,CAAAmB;IAAQ;IAC9CC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAiB,kBAAA,IAAAjB,QAAA,CAAAiB,kBAAA,IAAAD,IAAA,CAAkB;2BAG5BN,mBAAA,CA4QM,OA5QNQ,UA4QM,GA3QJ3B,YAAA,CAME4B,uBAAA;IALCC,SAAS,EAAE1B,KAAA,CAAAI,gBAAgB;IAC3BC,UAAU,EAAEC,QAAA,CAAAD,UAAU;IACtBsB,YAAW,EAAErB,QAAA,CAAAsB,gBAAgB;IAC7Bf,QAAM,EAAEP,QAAA,CAAAQ,YAAY;IACpBe,eAAc,EAAEvB,QAAA,CAAAE;uGAGnBQ,mBAAA,CAkQO;IAlQDtB,KAAK,EAAAuB,eAAA,EAAC,cAAc;MAAA,qBAAgCjB,KAAA,CAAAI;IAAgB;MACxEY,mBAAA,CAgQM,OAhQNc,UAgQM,GA/PJf,mBAAA,iBAAoB,EACpBC,mBAAA,CAgBM,OAhBNe,UAgBM,GAfJf,mBAAA,CAcM,OAdNgB,UAcM,GAbJhB,mBAAA,CAYM,OAZNiB,UAYM,GAVJjB,mBAAA,CASM,OATNkB,UASM,GARJlB,mBAAA,CAGS;IAHDtB,KAAK,EAAC,gCAAgC;IAAE0B,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAA6B,SAAA,IAAA7B,QAAA,CAAA6B,SAAA,IAAAb,IAAA,CAAS;IAAGc,QAAQ,EAAEpC,KAAA,CAAAqC;MAC3ErB,mBAAA,CAAoE;IAAjEtB,KAAK,EAAAuB,eAAA,EAAC,sBAAsB;MAAA,WAAsBjB,KAAA,CAAAqC;IAAO;wEAAQ,WAEtE,G,8BACArB,mBAAA,CAGS;IAHDtB,KAAK,EAAC,wBAAwB;IAAE0B,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAgC,gBAAA,IAAAhC,QAAA,CAAAgC,gBAAA,IAAAhB,IAAA,CAAgB;kCAC7DN,mBAAA,CAAqC;IAAlCtB,KAAK,EAAC;EAAuB,4B,iBAAK,YAEvC,E,YAMRqB,mBAAA,qBAAwB,EACxBC,mBAAA,CAqEM,OArENuB,UAqEM,GApEJvB,mBAAA,CAgBM,OAhBNwB,WAgBM,GAfJxB,mBAAA,CAcM,OAdNyB,WAcM,GAbJzB,mBAAA,CAYM,OAZN0B,WAYM,GAXJ1B,mBAAA,CAUM,OAVN2B,WAUM,GATJ3B,mBAAA,CAKM,OALN4B,WAKM,G,4BAJJ5B,mBAAA,CAEM;IAFDtB,KAAK,EAAC;EAA2D,GAAC,eAEvE,sBACAsB,mBAAA,CAAoF,OAApF6B,WAAoF,EAAAC,gBAAA,CAA7B9C,KAAA,CAAA+C,SAAS,CAACC,KAAK,sB,+BAExEhC,mBAAA,CAEM;IAFDtB,KAAK,EAAC;EAAU,IACnBsB,mBAAA,CAAgD;IAA7CtB,KAAK,EAAC;EAAkC,G,8BAMrDsB,mBAAA,CAgBM,OAhBNiC,WAgBM,GAfJjC,mBAAA,CAcM,OAdNkC,WAcM,GAbJlC,mBAAA,CAYM,OAZNmC,WAYM,GAXJnC,mBAAA,CAUM,OAVNoC,WAUM,GATJpC,mBAAA,CAKM,OALNqC,WAKM,G,4BAJJrC,mBAAA,CAEM;IAFDtB,KAAK,EAAC;EAA2D,GAAC,gBAEvE,sBACAsB,mBAAA,CAAqF,OAArFsC,WAAqF,EAAAR,gBAAA,CAA9B9C,KAAA,CAAA+C,SAAS,CAAC7B,MAAM,sB,+BAEzEF,mBAAA,CAEM;IAFDtB,KAAK,EAAC;EAAU,IACnBsB,mBAAA,CAAqD;IAAlDtB,KAAK,EAAC;EAAuC,G,8BAM1DsB,mBAAA,CAgBM,OAhBNuC,WAgBM,GAfJvC,mBAAA,CAcM,OAdNwC,WAcM,GAbJxC,mBAAA,CAYM,OAZNyC,WAYM,GAXJzC,mBAAA,CAUM,OAVN0C,WAUM,GATJ1C,mBAAA,CAKM,OALN2C,WAKM,G,4BAJJ3C,mBAAA,CAEM;IAFDtB,KAAK,EAAC;EAA2D,GAAC,wBAEvE,sBACAsB,mBAAA,CAAsF,OAAtF4C,WAAsF,EAAAd,gBAAA,CAA/B9C,KAAA,CAAA+C,SAAS,CAACc,OAAO,sB,+BAE1E7C,mBAAA,CAEM;IAFDtB,KAAK,EAAC;EAAU,IACnBsB,mBAAA,CAAqD;IAAlDtB,KAAK,EAAC;EAAuC,G,8BAM1DsB,mBAAA,CAgBM,OAhBN8C,WAgBM,GAfJ9C,mBAAA,CAcM,OAdN+C,WAcM,GAbJ/C,mBAAA,CAYM,OAZNgD,WAYM,GAXJhD,mBAAA,CAUM,OAVNiD,WAUM,GATJjD,mBAAA,CAKM,OALNkD,WAKM,G,4BAJJlD,mBAAA,CAEM;IAFDtB,KAAK,EAAC;EAAwD,GAAC,eAEpE,sBACAsB,mBAAA,CAAqF,OAArFmD,WAAqF,EAAArB,gBAAA,CAA9B9C,KAAA,CAAA+C,SAAS,CAACqB,MAAM,sB,+BAEzEpD,mBAAA,CAEM;IAFDtB,KAAK,EAAC;EAAU,IACnBsB,mBAAA,CAAsD;IAAnDtB,KAAK,EAAC;EAAwC,G,gCAQ7DqB,mBAAA,iBAAoB,EACpBC,mBAAA,CAkKM,OAlKNqD,WAkKM,GAjKJrD,mBAAA,CAgKM,OAhKNsD,WAgKM,GA/JJtD,mBAAA,CA8JM,OA9JNuD,WA8JM,GA7JJvD,mBAAA,CAmBM,OAnBNwD,WAmBM,G,4BAlBJxD,mBAAA,CAGK;IAHDtB,KAAK,EAAC;EAAmC,IAC3CsB,mBAAA,CAAiC;IAA9BtB,KAAK,EAAC;EAAmB,I,iBAAK,aAEnC,E,sBACAsB,mBAAA,CAaM,OAbNyD,WAaM,G,gBAZJzD,mBAAA,CAMS;IANDtB,KAAK,EAAC,4BAA4B;+DAAUM,KAAA,CAAA0E,YAAY,GAAAC,MAAA;IAAGC,QAAM,EAAAvD,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAuE,WAAA,IAAAvE,QAAA,CAAAuE,WAAA,IAAAvD,IAAA,CAAW;yZAAlCtB,KAAA,CAAA0E,YAAY,E,mBAOhE1D,mBAAA,CAIS;IAJDtB,KAAK,EAAC,4BAA4B;+DAAUM,KAAA,CAAA8E,UAAU,GAAAH,MAAA;IAAGC,QAAM,EAAAvD,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAuE,WAAA,IAAAvE,QAAA,CAAAuE,WAAA,IAAAvD,IAAA,CAAW;kCAClFN,mBAAA,CAAmC;IAA3B+D,KAAK,EAAC;EAAE,GAAC,WAAS,qBAC1B/D,mBAAA,CAAuC;IAA/B+D,KAAK,EAAC;EAAQ,GAAC,SAAO,qBAC9B/D,mBAAA,CAAqC;IAA7B+D,KAAK,EAAC;EAAO,GAAC,QAAM,oB,2DAHsB/E,KAAA,CAAA8E,UAAU,E,OAOlE9D,mBAAA,CAwIM,OAxINgE,WAwIM,GAvIJjE,mBAAA,gBAAmB,EACnBC,mBAAA,CAoBM,OApBNiE,WAoBM,GAnBJjE,mBAAA,CAaM,OAbNkE,WAaM,GAZJlE,mBAAA,CAWM,OAXNmE,WAWM,G,4BAVJnE,mBAAA,CAEO;IAFDtB,KAAK,EAAC;EAAkB,IAC5BsB,mBAAA,CAA6B;IAA1BtB,KAAK,EAAC;EAAe,G,sCAE1BsB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX1F,KAAK,EAAC,cAAc;IACpB2F,WAAW,EAAC,6CAA6C;+DAChDrF,KAAA,CAAAsF,WAAW,GAAAX,MAAA;IACnBY,OAAK,EAAAlE,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAkF,WAAA,IAAAlF,QAAA,CAAAkF,WAAA,IAAAlE,IAAA,CAAW;iEADVtB,KAAA,CAAAsF,WAAW,E,OAK1BtE,mBAAA,CAIM,OAJNyE,WAIM,GAHJzE,mBAAA,CAEO,QAFP0E,WAEO,EAFkB,WACf,GAAA5C,gBAAA,CAAG9C,KAAA,CAAA2F,aAAa,CAACC,MAAM,IAAG,MAAI,GAAA9C,gBAAA,CAAG9C,KAAA,CAAA6F,KAAK,CAACD,MAAM,IAAG,SAC1D,gB,KAIJ7E,mBAAA,mBAAsB,EACXf,KAAA,CAAAqC,OAAO,I,cAAlB1C,mBAAA,CAKM,OALNmG,WAKM,EAAAzE,MAAA,SAAAA,MAAA,QAJJL,mBAAA,CAEM;IAFDtB,KAAK,EAAC,6BAA6B;IAACqG,IAAI,EAAC;MAC5C/E,mBAAA,CAA+C;IAAzCtB,KAAK,EAAC;EAAiB,GAAC,YAAU,E,qBAE1CsB,mBAAA,CAA+C;IAA5CtB,KAAK,EAAC;EAAiB,GAAC,kBAAgB,oB,MAI7BM,KAAA,CAAA2F,aAAa,CAACC,MAAM,U,cAApCjG,mBAAA,CAMMqG,SAAA;IAAAC,GAAA;EAAA,IAPNlF,mBAAA,iBAAoB,EACpBC,mBAAA,CAMM,OANNkF,WAMM,G,4BALJlF,mBAAA,CAAqD;IAAlDtB,KAAK,EAAC;EAAuC,6B,4BAChDsB,mBAAA,CAA6C;IAAzCtB,KAAK,EAAC;EAAe,GAAC,gBAAc,sBACxCsB,mBAAA,CAEI,KAFJmF,WAEI,EAAArD,gBAAA,CADC9C,KAAA,CAAAsF,WAAW,gG,qEAKlB3F,mBAAA,CAgEMqG,SAAA;IAAAC,GAAA;EAAA,IAjENlF,mBAAA,iBAAoB,EACpBC,mBAAA,CAgEM,OAhENoF,WAgEM,GA/DJpF,mBAAA,CA8DQ,SA9DRqF,WA8DQ,G,4BA7DNrF,mBAAA,CASQ;IATDtB,KAAK,EAAC;EAAa,IACxBsB,mBAAA,CAOK,aANHA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAc,YAAV,OAAK,GACTA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAe,YAAX,QAAM,GACVA,mBAAA,CAAmB,YAAf,YAAU,GACdA,mBAAA,CAAgB,YAAZ,SAAO,E,wBAGfA,mBAAA,CAkDQ,iB,kBAjDNrB,mBAAA,CAgDKqG,SAAA,QAAAM,WAAA,CAhDchG,QAAA,CAAAiG,cAAc,EAAtBC,IAAI;yBAAf7G,mBAAA,CAgDK;MAhD+BsG,GAAG,EAAEO,IAAI,CAACC;QAC5CzF,mBAAA,CAaK,aAZHA,mBAAA,CAWM,OAXN0F,WAWM,GAVJ1F,mBAAA,CAKM,OALN2F,WAKM,GAJOH,IAAI,CAACI,eAAe,I,cAA/BjH,mBAAA,CAA0G;;MAAxEkH,GAAG,EAAEL,IAAI,CAACI,eAAe;MAAGE,GAAG,EAAEN,IAAI,CAACO,SAAS;MAAErH,KAAK,EAAC;2DACzFC,mBAAA,CAEM,OAFNqH,WAEM,EAAAlE,gBAAA,CADDxC,QAAA,CAAA2G,WAAW,CAACT,IAAI,CAACO,SAAS,mB,GAGjC/F,mBAAA,CAGM,cAFJA,mBAAA,CAA+C,OAA/CkG,WAA+C,EAAApE,gBAAA,CAAvB0D,IAAI,CAACO,SAAS,kBACtC/F,mBAAA,CAAwD,OAAxDmG,WAAwD,EAA1B,GAAC,GAAArE,gBAAA,CAAG0D,IAAI,CAACY,QAAQ,iB,OAIrDpG,mBAAA,CAAyB,YAAA8B,gBAAA,CAAlB0D,IAAI,CAACa,KAAK,kBACjBrG,mBAAA,CAIK,aAHHA,mBAAA,CAEO;MAFDtB,KAAK,EAAAuB,eAAA,EAAC,OAAO,EAASuF,IAAI,CAACpB,IAAI;wBAChCoB,IAAI,CAACpB,IAAI,yD,GAGhBpE,mBAAA,CAIK,aAHHA,mBAAA,CAEO;MAFDtB,KAAK,EAAAuB,eAAA,EAAC,OAAO,EAASX,QAAA,CAAAgH,mBAAmB,CAACd,IAAI,CAACe,MAAM;wBACtDjH,QAAA,CAAAkH,YAAY,CAAChB,IAAI,CAACe,MAAM,yB,GAG/BvG,mBAAA,CAA0C,YAAA8B,gBAAA,CAAnCxC,QAAA,CAAAmH,UAAU,CAACjB,IAAI,CAACkB,UAAU,mBACjC1G,mBAAA,CAoBK,aAnBHA,mBAAA,CAkBM,OAlBN2G,WAkBM,GAjBJ3G,mBAAA,CAES;MAFDtB,KAAK,EAAC,yBAAyB;MAAE0B,OAAK,EAAAuD,MAAA,IAAErE,QAAA,CAAAsH,QAAQ,CAACpB,IAAI;MAAGqB,KAAK,EAAC;yCACpE7G,mBAAA,CAA0B;MAAvBtB,KAAK,EAAC;IAAY,2B,kCAEvBsB,mBAAA,CAES;MAFDtB,KAAK,EAAC,yBAAyB;MAAE0B,OAAK,EAAAuD,MAAA,IAAErE,QAAA,CAAAwH,QAAQ,CAACtB,IAAI;MAAGqB,KAAK,EAAC;yCACpE7G,mBAAA,CAA2B;MAAxBtB,KAAK,EAAC;IAAa,2B,kCAExBsB,mBAAA,CAOS;MANPtB,KAAK,EAAAuB,eAAA,EAAC,KAAK,EACHuF,IAAI,CAACe,MAAM;MAClBnG,OAAK,EAAAuD,MAAA,IAAErE,QAAA,CAAAyH,gBAAgB,CAACvB,IAAI;MAC5BqB,KAAK,EAAErB,IAAI,CAACe,MAAM;QAEnBvG,mBAAA,CAA0E;MAAtEtB,KAAK,EAAAuB,eAAA,CAAEuF,IAAI,CAACe,MAAM;mEAExBvG,mBAAA,CAES;MAFDtB,KAAK,EAAC,wBAAwB;MAAE0B,OAAK,EAAAuD,MAAA,IAAErE,QAAA,CAAA0H,UAAU,CAACxB,IAAI;MAAGqB,KAAK,EAAC;yCACrE7G,mBAAA,CAA4B;MAAzBtB,KAAK,EAAC;IAAc,2B;0FASrCqB,mBAAA,gBAAmB,EACRT,QAAA,CAAA2H,UAAU,Q,cAArBtI,mBAAA,CA0BM,OA1BNuI,WA0BM,GAzBJlH,mBAAA,CAEM,OAFNmH,WAEM,EAFkB,QACjB,GAAArF,gBAAA,CAAG9C,KAAA,CAAAoI,WAAW,IAAG,MAAI,GAAAtF,gBAAA,CAAGxC,QAAA,CAAA2H,UAAU,kBAEzCjH,mBAAA,CAqBM,cApBJA,mBAAA,CAmBK,MAnBLqH,WAmBK,GAlBHrH,mBAAA,CAIK;IAJDtB,KAAK,EAAAuB,eAAA,EAAC,WAAW;MAAAmB,QAAA,EAAqBpC,KAAA,CAAAoI,WAAW;IAAA;MACnDpH,mBAAA,CAES;IAFDtB,KAAK,EAAC,WAAW;IAAE0B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAsD,MAAA,IAAErE,QAAA,CAAAgI,UAAU,CAACtI,KAAA,CAAAoI,WAAW;IAAQhG,QAAQ,EAAEpC,KAAA,CAAAoI,WAAW;KAAQ,YAE7F,iBAAAG,WAAA,E,qCAEF5I,mBAAA,CAOKqG,SAAA,QAAAM,WAAA,CANYhG,QAAA,CAAAkI,YAAY,EAApBC,IAAI;yBADb9I,mBAAA,CAOK;MALFsG,GAAG,EAAEwC,IAAI;MACV/I,KAAK,EAAAuB,eAAA,EAAC,WAAW;QAAAC,MAAA,EACCuH,IAAI,KAAKzI,KAAA,CAAAoI;MAAW;QAEtCpH,mBAAA,CAAuE;MAA/DtB,KAAK,EAAC,WAAW;MAAE0B,OAAK,EAAAuD,MAAA,IAAErE,QAAA,CAAAgI,UAAU,CAACG,IAAI;wBAAMA,IAAI,wBAAAC,WAAA,E;kCAE7D1H,mBAAA,CAIK;IAJDtB,KAAK,EAAAuB,eAAA,EAAC,WAAW;MAAAmB,QAAA,EAAqBpC,KAAA,CAAAoI,WAAW,KAAK9H,QAAA,CAAA2H;IAAU;MAClEjH,mBAAA,CAES;IAFDtB,KAAK,EAAC,WAAW;IAAE0B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAsD,MAAA,IAAErE,QAAA,CAAAgI,UAAU,CAACtI,KAAA,CAAAoI,WAAW;IAAQhG,QAAQ,EAAEpC,KAAA,CAAAoI,WAAW,KAAK9H,QAAA,CAAA2H;KAAY,QAEtG,iBAAAU,WAAA,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}