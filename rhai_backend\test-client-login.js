const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testClientLogin() {
  console.log('🔐 Testing client login credentials...');
  
  const clients = ['revo4438', 'testclient'];
  const passwords = ['password', 'password123', '123456', 'admin123', 'test123', 'revo4438'];
  
  for (const username of clients) {
    console.log(`\n👤 Testing username: ${username}`);
    
    for (const password of passwords) {
      try {
        const response = await axios.post(`${BASE_URL}/client/auth/login`, {
          username: username,
          password: password
        });
        
        if (response.data.success && response.data.data.token) {
          console.log(`✅ SUCCESS! Username: ${username}, Password: ${password}`);
          console.log(`Token: ${response.data.data.token.substring(0, 50)}...`);
          return { username, password, token: response.data.data.token };
        }
      } catch (error) {
        // Silent fail for password testing
      }
    }
    
    console.log(`❌ No valid password found for ${username}`);
  }
  
  console.log('\n❌ No valid client credentials found');
  return null;
}

testClientLogin().then(result => {
  if (result) {
    console.log('\n🎉 Client login successful!');
    console.log(`Use: Username: ${result.username}, Password: ${result.password}`);
  }
  process.exit(0);
}).catch(console.error);
