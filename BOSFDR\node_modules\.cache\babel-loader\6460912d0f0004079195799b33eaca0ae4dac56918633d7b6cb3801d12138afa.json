{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, vModelText as _vModelText, withKeys as _withKeys, withDirectives as _withDirectives, renderList as _renderList, Fragment as _Fragment, vModelSelect as _vModelSelect, vModelRadio as _vModelRadio, withModifiers as _withModifiers, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"admin-requests\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-container\"\n};\nconst _hoisted_3 = {\n  key: 0,\n  class: \"d-flex justify-content-center align-items-center\",\n  style: {\n    \"min-height\": \"400px\"\n  }\n};\nconst _hoisted_4 = {\n  class: \"container-fluid py-4\"\n};\nconst _hoisted_5 = {\n  key: 0,\n  class: \"alert alert-danger alert-dismissible fade show\",\n  role: \"alert\"\n};\nconst _hoisted_6 = {\n  class: \"row mb-4\"\n};\nconst _hoisted_7 = {\n  class: \"col-12\"\n};\nconst _hoisted_8 = {\n  class: \"d-flex justify-content-between align-items-center flex-wrap\"\n};\nconst _hoisted_9 = {\n  class: \"text-muted mb-0\"\n};\nconst _hoisted_10 = {\n  key: 0,\n  class: \"ms-2 small\"\n};\nconst _hoisted_11 = {\n  class: \"d-flex gap-2 align-items-center\"\n};\nconst _hoisted_12 = {\n  class: \"real-time-status me-2\"\n};\nconst _hoisted_13 = {\n  key: 0,\n  class: \"fas fa-circle pulse\"\n};\nconst _hoisted_14 = {\n  key: 1,\n  class: \"fas fa-pause\"\n};\nconst _hoisted_15 = [\"title\"];\nconst _hoisted_16 = [\"disabled\"];\nconst _hoisted_17 = [\"disabled\"];\nconst _hoisted_18 = {\n  class: \"row mb-4\"\n};\nconst _hoisted_19 = {\n  class: \"col-xl-3 col-md-6 mb-3\"\n};\nconst _hoisted_20 = {\n  class: \"card border-left-primary shadow h-100 py-2\"\n};\nconst _hoisted_21 = {\n  class: \"card-body\"\n};\nconst _hoisted_22 = {\n  class: \"row g-0 align-items-center\"\n};\nconst _hoisted_23 = {\n  class: \"col me-2\"\n};\nconst _hoisted_24 = {\n  class: \"h5 mb-0 fw-bold text-dark\"\n};\nconst _hoisted_25 = {\n  class: \"col-xl-3 col-md-6 mb-3\"\n};\nconst _hoisted_26 = {\n  class: \"card border-left-warning shadow h-100 py-2\"\n};\nconst _hoisted_27 = {\n  class: \"card-body\"\n};\nconst _hoisted_28 = {\n  class: \"row g-0 align-items-center\"\n};\nconst _hoisted_29 = {\n  class: \"col me-2\"\n};\nconst _hoisted_30 = {\n  class: \"h5 mb-0 fw-bold text-dark\"\n};\nconst _hoisted_31 = {\n  class: \"col-xl-3 col-md-6 mb-3\"\n};\nconst _hoisted_32 = {\n  class: \"card border-left-success shadow h-100 py-2\"\n};\nconst _hoisted_33 = {\n  class: \"card-body\"\n};\nconst _hoisted_34 = {\n  class: \"row g-0 align-items-center\"\n};\nconst _hoisted_35 = {\n  class: \"col me-2\"\n};\nconst _hoisted_36 = {\n  class: \"h5 mb-0 fw-bold text-dark\"\n};\nconst _hoisted_37 = {\n  class: \"col-xl-3 col-md-6 mb-3\"\n};\nconst _hoisted_38 = {\n  class: \"card border-left-info shadow h-100 py-2\"\n};\nconst _hoisted_39 = {\n  class: \"card-body\"\n};\nconst _hoisted_40 = {\n  class: \"row g-0 align-items-center\"\n};\nconst _hoisted_41 = {\n  class: \"col me-2\"\n};\nconst _hoisted_42 = {\n  class: \"h5 mb-0 fw-bold text-dark\"\n};\nconst _hoisted_43 = {\n  key: 1,\n  class: \"card shadow mb-4\"\n};\nconst _hoisted_44 = {\n  class: \"card-body\"\n};\nconst _hoisted_45 = {\n  class: \"row\"\n};\nconst _hoisted_46 = {\n  class: \"col-md-3 mb-3\"\n};\nconst _hoisted_47 = {\n  class: \"col-md-2 mb-3\"\n};\nconst _hoisted_48 = [\"value\"];\nconst _hoisted_49 = {\n  class: \"col-md-2 mb-3\"\n};\nconst _hoisted_50 = {\n  class: \"col-md-2 mb-3\"\n};\nconst _hoisted_51 = {\n  class: \"col-md-2 mb-3\"\n};\nconst _hoisted_52 = {\n  class: \"col-md-1 mb-3 d-flex align-items-end\"\n};\nconst _hoisted_53 = {\n  class: \"d-flex gap-1 w-100\"\n};\nconst _hoisted_54 = {\n  key: 2,\n  class: \"card shadow mb-4\"\n};\nconst _hoisted_55 = {\n  class: \"card-header py-3 bg-warning\"\n};\nconst _hoisted_56 = {\n  class: \"m-0 fw-bold text-dark\"\n};\nconst _hoisted_57 = {\n  class: \"card-body\"\n};\nconst _hoisted_58 = {\n  class: \"row align-items-end\"\n};\nconst _hoisted_59 = {\n  class: \"col-md-3 mb-3\"\n};\nconst _hoisted_60 = [\"value\"];\nconst _hoisted_61 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_62 = {\n  class: \"col-md-3 mb-3\"\n};\nconst _hoisted_63 = {\n  class: \"d-flex gap-2\"\n};\nconst _hoisted_64 = [\"disabled\"];\nconst _hoisted_65 = {\n  class: \"d-flex justify-content-between align-items-center mb-4\"\n};\nconst _hoisted_66 = {\n  class: \"d-flex align-items-center gap-3\"\n};\nconst _hoisted_67 = {\n  class: \"btn-group\",\n  role: \"group\",\n  \"aria-label\": \"View toggle\"\n};\nconst _hoisted_68 = {\n  class: \"d-flex align-items-center gap-2\"\n};\nconst _hoisted_69 = {\n  class: \"text-muted small\"\n};\nconst _hoisted_70 = {\n  class: \"d-flex align-items-center gap-2\"\n};\nconst _hoisted_71 = {\n  key: 3,\n  class: \"requests-grid\"\n};\nconst _hoisted_72 = {\n  key: 0,\n  class: \"empty-state text-center py-5\"\n};\nconst _hoisted_73 = {\n  class: \"row g-4\"\n};\nconst _hoisted_74 = {\n  class: \"request-card-header\"\n};\nconst _hoisted_75 = {\n  class: \"d-flex justify-content-between align-items-start\"\n};\nconst _hoisted_76 = {\n  class: \"d-flex align-items-center gap-2\"\n};\nconst _hoisted_77 = [\"checked\", \"onChange\"];\nconst _hoisted_78 = {\n  class: \"request-number\"\n};\nconst _hoisted_79 = {\n  class: \"badge bg-primary\"\n};\nconst _hoisted_80 = {\n  class: \"request-actions\"\n};\nconst _hoisted_81 = {\n  class: \"dropdown\"\n};\nconst _hoisted_82 = {\n  class: \"dropdown-menu dropdown-menu-end\"\n};\nconst _hoisted_83 = [\"onClick\"];\nconst _hoisted_84 = [\"onClick\"];\nconst _hoisted_85 = [\"onClick\"];\nconst _hoisted_86 = {\n  class: \"request-card-body\"\n};\nconst _hoisted_87 = {\n  class: \"client-info mb-3\"\n};\nconst _hoisted_88 = {\n  class: \"d-flex align-items-center gap-2 mb-2\"\n};\nconst _hoisted_89 = {\n  class: \"mb-0 fw-bold\"\n};\nconst _hoisted_90 = {\n  class: \"text-muted\"\n};\nconst _hoisted_91 = {\n  class: \"document-type mb-3\"\n};\nconst _hoisted_92 = {\n  class: \"d-flex align-items-center gap-2\"\n};\nconst _hoisted_93 = {\n  class: \"badge bg-info-subtle text-info-emphasis px-3 py-2\"\n};\nconst _hoisted_94 = {\n  class: \"request-meta mb-3\"\n};\nconst _hoisted_95 = {\n  class: \"row g-2\"\n};\nconst _hoisted_96 = {\n  class: \"col-6\"\n};\nconst _hoisted_97 = {\n  class: \"meta-item\"\n};\nconst _hoisted_98 = {\n  class: \"col-6\"\n};\nconst _hoisted_99 = {\n  class: \"meta-item\"\n};\nconst _hoisted_100 = {\n  class: \"fw-bold text-success\"\n};\nconst _hoisted_101 = {\n  class: \"request-date\"\n};\nconst _hoisted_102 = {\n  class: \"text-muted\"\n};\nconst _hoisted_103 = {\n  class: \"request-card-footer\"\n};\nconst _hoisted_104 = {\n  class: \"d-flex gap-2\"\n};\nconst _hoisted_105 = [\"onClick\"];\nconst _hoisted_106 = [\"onClick\", \"disabled\"];\nconst _hoisted_107 = [\"onClick\", \"disabled\"];\nconst _hoisted_108 = {\n  class: \"modern-table-container\"\n};\nconst _hoisted_109 = {\n  class: \"modern-table-header\"\n};\nconst _hoisted_110 = {\n  class: \"d-flex justify-content-between align-items-center\"\n};\nconst _hoisted_111 = {\n  class: \"table-actions d-flex gap-2\"\n};\nconst _hoisted_112 = {\n  key: 0,\n  class: \"modern-table-empty\"\n};\nconst _hoisted_113 = {\n  class: \"modern-table\"\n};\nconst _hoisted_114 = {\n  class: \"table-cell selection-cell\"\n};\nconst _hoisted_115 = [\"checked\", \"onChange\"];\nconst _hoisted_116 = {\n  class: \"table-cell request-number-cell\"\n};\nconst _hoisted_117 = {\n  class: \"request-number-content\"\n};\nconst _hoisted_118 = {\n  class: \"request-number\"\n};\nconst _hoisted_119 = {\n  class: \"request-id\"\n};\nconst _hoisted_120 = {\n  class: \"table-cell client-cell\"\n};\nconst _hoisted_121 = {\n  class: \"client-info\"\n};\nconst _hoisted_122 = {\n  class: \"client-details\"\n};\nconst _hoisted_123 = {\n  class: \"client-name\"\n};\nconst _hoisted_124 = {\n  class: \"client-email\"\n};\nconst _hoisted_125 = {\n  class: \"table-cell document-type-cell\"\n};\nconst _hoisted_126 = {\n  class: \"document-type-badge\"\n};\nconst _hoisted_127 = {\n  class: \"table-cell status-cell\"\n};\nconst _hoisted_128 = {\n  class: \"table-cell amount-cell\"\n};\nconst _hoisted_129 = {\n  class: \"amount-content\"\n};\nconst _hoisted_130 = {\n  class: \"amount\"\n};\nconst _hoisted_131 = {\n  class: \"table-cell date-cell\"\n};\nconst _hoisted_132 = {\n  class: \"date-content\"\n};\nconst _hoisted_133 = {\n  class: \"date\"\n};\nconst _hoisted_134 = {\n  class: \"time\"\n};\nconst _hoisted_135 = {\n  class: \"table-cell actions-cell\"\n};\nconst _hoisted_136 = {\n  class: \"action-buttons\"\n};\nconst _hoisted_137 = [\"onClick\"];\nconst _hoisted_138 = [\"onClick\", \"disabled\"];\nconst _hoisted_139 = [\"onClick\", \"disabled\"];\nconst _hoisted_140 = {\n  class: \"dropdown\"\n};\nconst _hoisted_141 = {\n  class: \"dropdown-menu dropdown-menu-end\"\n};\nconst _hoisted_142 = [\"onClick\"];\nconst _hoisted_143 = [\"onClick\"];\nconst _hoisted_144 = [\"onClick\"];\nconst _hoisted_145 = {\n  key: 5,\n  class: \"pagination-container\"\n};\nconst _hoisted_146 = {\n  \"aria-label\": \"Requests pagination\"\n};\nconst _hoisted_147 = {\n  class: \"pagination pagination-sm justify-content-center mb-0\"\n};\nconst _hoisted_148 = [\"onClick\"];\nconst _hoisted_149 = {\n  key: 2,\n  class: \"modal fade show d-block\",\n  tabindex: \"-1\",\n  style: {\n    \"background-color\": \"rgba(0,0,0,0.5)\"\n  }\n};\nconst _hoisted_150 = {\n  class: \"modal-dialog modal-xl modal-dialog-scrollable\"\n};\nconst _hoisted_151 = {\n  class: \"modal-content\"\n};\nconst _hoisted_152 = {\n  class: \"modal-header\"\n};\nconst _hoisted_153 = {\n  class: \"modal-title\"\n};\nconst _hoisted_154 = {\n  class: \"modal-body\"\n};\nconst _hoisted_155 = {\n  class: \"row\"\n};\nconst _hoisted_156 = {\n  class: \"col-lg-8\"\n};\nconst _hoisted_157 = {\n  class: \"card mb-4\"\n};\nconst _hoisted_158 = {\n  class: \"card-body\"\n};\nconst _hoisted_159 = {\n  class: \"row\"\n};\nconst _hoisted_160 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_161 = {\n  class: \"mb-3\"\n};\nconst _hoisted_162 = {\n  class: \"mb-0\"\n};\nconst _hoisted_163 = {\n  class: \"mb-3\"\n};\nconst _hoisted_164 = {\n  class: \"mb-0\"\n};\nconst _hoisted_165 = {\n  class: \"badge bg-info\"\n};\nconst _hoisted_166 = {\n  class: \"mb-3\"\n};\nconst _hoisted_167 = {\n  class: \"mb-0\"\n};\nconst _hoisted_168 = {\n  class: \"mb-3\"\n};\nconst _hoisted_169 = {\n  class: \"mb-0\"\n};\nconst _hoisted_170 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_171 = {\n  class: \"mb-3\"\n};\nconst _hoisted_172 = {\n  class: \"mb-0\"\n};\nconst _hoisted_173 = {\n  class: \"mb-3\"\n};\nconst _hoisted_174 = {\n  class: \"mb-0\"\n};\nconst _hoisted_175 = {\n  class: \"mb-3\"\n};\nconst _hoisted_176 = {\n  class: \"mb-0\"\n};\nconst _hoisted_177 = {\n  class: \"mb-3\"\n};\nconst _hoisted_178 = {\n  class: \"mb-0\"\n};\nconst _hoisted_179 = {\n  class: \"card mb-4\"\n};\nconst _hoisted_180 = {\n  class: \"card-body\"\n};\nconst _hoisted_181 = {\n  class: \"row\"\n};\nconst _hoisted_182 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_183 = {\n  class: \"mb-3\"\n};\nconst _hoisted_184 = {\n  class: \"mb-0\"\n};\nconst _hoisted_185 = {\n  class: \"mb-3\"\n};\nconst _hoisted_186 = {\n  class: \"mb-0\"\n};\nconst _hoisted_187 = [\"href\"];\nconst _hoisted_188 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_189 = {\n  class: \"mb-3\"\n};\nconst _hoisted_190 = {\n  class: \"mb-0\"\n};\nconst _hoisted_191 = [\"href\"];\nconst _hoisted_192 = {\n  class: \"mb-3\"\n};\nconst _hoisted_193 = {\n  class: \"mb-0\"\n};\nconst _hoisted_194 = {\n  key: 0,\n  class: \"card mb-4\"\n};\nconst _hoisted_195 = {\n  class: \"card-body\"\n};\nconst _hoisted_196 = {\n  key: 0\n};\nconst _hoisted_197 = {\n  class: \"row\"\n};\nconst _hoisted_198 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_199 = {\n  class: \"mb-3\"\n};\nconst _hoisted_200 = {\n  class: \"mb-0\"\n};\nconst _hoisted_201 = {\n  class: \"mb-3\"\n};\nconst _hoisted_202 = {\n  class: \"mb-0\"\n};\nconst _hoisted_203 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_204 = {\n  class: \"mb-3\"\n};\nconst _hoisted_205 = {\n  class: \"mb-0\"\n};\nconst _hoisted_206 = {\n  class: \"mb-3\"\n};\nconst _hoisted_207 = {\n  class: \"mb-0\"\n};\nconst _hoisted_208 = {\n  class: \"row\"\n};\nconst _hoisted_209 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_210 = {\n  class: \"mb-3\"\n};\nconst _hoisted_211 = {\n  class: \"mb-0\"\n};\nconst _hoisted_212 = {\n  class: \"mb-3\"\n};\nconst _hoisted_213 = {\n  class: \"mb-0\"\n};\nconst _hoisted_214 = {\n  class: \"mb-3\"\n};\nconst _hoisted_215 = {\n  class: \"mb-0\"\n};\nconst _hoisted_216 = {\n  class: \"mb-3\"\n};\nconst _hoisted_217 = {\n  class: \"mb-0\"\n};\nconst _hoisted_218 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_219 = {\n  class: \"mb-3\"\n};\nconst _hoisted_220 = {\n  class: \"mb-0\"\n};\nconst _hoisted_221 = {\n  class: \"mb-3\"\n};\nconst _hoisted_222 = {\n  class: \"mb-0\"\n};\nconst _hoisted_223 = {\n  class: \"mb-3\"\n};\nconst _hoisted_224 = {\n  class: \"mb-0\"\n};\nconst _hoisted_225 = {\n  class: \"mb-3\"\n};\nconst _hoisted_226 = {\n  class: \"mb-0\"\n};\nconst _hoisted_227 = {\n  class: \"col-lg-4\"\n};\nconst _hoisted_228 = {\n  class: \"card mb-4\"\n};\nconst _hoisted_229 = {\n  class: \"card-body\"\n};\nconst _hoisted_230 = {\n  class: \"mb-3\"\n};\nconst _hoisted_231 = [\"value\"];\nconst _hoisted_232 = {\n  class: \"mb-3\"\n};\nconst _hoisted_233 = {\n  class: \"d-grid gap-2\"\n};\nconst _hoisted_234 = [\"disabled\"];\nconst _hoisted_235 = {\n  key: 0,\n  class: \"mt-3 p-3 border rounded bg-light\"\n};\nconst _hoisted_236 = {\n  class: \"mb-3\"\n};\nconst _hoisted_237 = {\n  class: \"d-grid\"\n};\nconst _hoisted_238 = [\"disabled\"];\nconst _hoisted_239 = {\n  class: \"card mb-4\"\n};\nconst _hoisted_240 = {\n  class: \"card-body\"\n};\nconst _hoisted_241 = {\n  class: \"mb-3\"\n};\nconst _hoisted_242 = {\n  class: \"mb-0\"\n};\nconst _hoisted_243 = {\n  class: \"mb-3\"\n};\nconst _hoisted_244 = {\n  class: \"mb-0\"\n};\nconst _hoisted_245 = {\n  class: \"row\"\n};\nconst _hoisted_246 = {\n  class: \"col-6\"\n};\nconst _hoisted_247 = {\n  class: \"mb-2\"\n};\nconst _hoisted_248 = {\n  class: \"mb-0\"\n};\nconst _hoisted_249 = {\n  class: \"col-6\"\n};\nconst _hoisted_250 = {\n  class: \"mb-2\"\n};\nconst _hoisted_251 = {\n  class: \"mb-0\"\n};\nconst _hoisted_252 = {\n  class: \"col-6\"\n};\nconst _hoisted_253 = {\n  class: \"mb-2\"\n};\nconst _hoisted_254 = {\n  class: \"mb-0\"\n};\nconst _hoisted_255 = {\n  class: \"col-6\"\n};\nconst _hoisted_256 = {\n  class: \"mb-2\"\n};\nconst _hoisted_257 = {\n  class: \"mb-0 fw-bold text-primary\"\n};\nconst _hoisted_258 = {\n  class: \"card\"\n};\nconst _hoisted_259 = {\n  class: \"card-body\"\n};\nconst _hoisted_260 = {\n  key: 0,\n  class: \"timeline\"\n};\nconst _hoisted_261 = {\n  class: \"timeline-content\"\n};\nconst _hoisted_262 = {\n  class: \"timeline-header\"\n};\nconst _hoisted_263 = {\n  class: \"text-muted ms-2\"\n};\nconst _hoisted_264 = {\n  class: \"timeline-body\"\n};\nconst _hoisted_265 = {\n  class: \"mb-1\"\n};\nconst _hoisted_266 = {\n  key: 0,\n  class: \"mb-1\"\n};\nconst _hoisted_267 = {\n  key: 1,\n  class: \"mb-0\"\n};\nconst _hoisted_268 = {\n  key: 1,\n  class: \"text-center text-muted py-3\"\n};\nconst _hoisted_269 = {\n  class: \"modal-footer\"\n};\nconst _hoisted_270 = {\n  key: 3,\n  class: \"modal fade show d-block\",\n  tabindex: \"-1\",\n  style: {\n    \"background-color\": \"rgba(0,0,0,0.5)\"\n  }\n};\nconst _hoisted_271 = {\n  class: \"modal-dialog\"\n};\nconst _hoisted_272 = {\n  class: \"modal-content\"\n};\nconst _hoisted_273 = {\n  class: \"modal-header\"\n};\nconst _hoisted_274 = {\n  class: \"modal-body\"\n};\nconst _hoisted_275 = {\n  class: \"mb-3\"\n};\nconst _hoisted_276 = {\n  class: \"list-unstyled mt-2\"\n};\nconst _hoisted_277 = {\n  class: \"mb-3\"\n};\nconst _hoisted_278 = {\n  key: 0,\n  class: \"invalid-feedback\"\n};\nconst _hoisted_279 = {\n  class: \"modal-footer\"\n};\nconst _hoisted_280 = [\"disabled\"];\nconst _hoisted_281 = [\"disabled\"];\nconst _hoisted_282 = {\n  key: 0\n};\nconst _hoisted_283 = {\n  key: 1\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_AdminHeader = _resolveComponent(\"AdminHeader\");\n  const _component_AdminSidebar = _resolveComponent(\"AdminSidebar\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_AdminHeader, {\n    userName: $data.adminData?.first_name || 'Admin',\n    showUserDropdown: $data.showUserDropdown,\n    sidebarCollapsed: $data.sidebarCollapsed,\n    activeMenu: $options.activeMenu,\n    onSidebarToggle: $options.handleSidebarToggle,\n    onUserDropdownToggle: $options.handleUserDropdownToggle,\n    onMenuAction: $options.handleMenuAction,\n    onLogout: $options.handleLogout\n  }, null, 8 /* PROPS */, [\"userName\", \"showUserDropdown\", \"sidebarCollapsed\", \"activeMenu\", \"onSidebarToggle\", \"onUserDropdownToggle\", \"onMenuAction\", \"onLogout\"]), _createCommentVNode(\" Mobile Overlay \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"mobile-overlay\", {\n      active: !$data.sidebarCollapsed && $data.isMobile\n    }]),\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.closeMobileSidebar && $options.closeMobileSidebar(...args))\n  }, null, 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_AdminSidebar, {\n    collapsed: $data.sidebarCollapsed,\n    activeMenu: $options.activeMenu,\n    onMenuChange: $options.handleMenuChange,\n    onLogout: $options.handleLogout,\n    onToggleSidebar: $options.handleSidebarToggle\n  }, null, 8 /* PROPS */, [\"collapsed\", \"activeMenu\", \"onMenuChange\", \"onLogout\", \"onToggleSidebar\"]), _createElementVNode(\"main\", {\n    class: _normalizeClass([\"main-content\", {\n      'sidebar-collapsed': $data.sidebarCollapsed\n    }])\n  }, [_createCommentVNode(\" Loading State \"), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, _cache[40] || (_cache[40] = [_createElementVNode(\"div\", {\n    class: \"spinner-border text-primary\",\n    role: \"status\"\n  }, [_createElementVNode(\"span\", {\n    class: \"visually-hidden\"\n  }, \"Loading...\")], -1 /* HOISTED */)]))) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" Main Content \"), _createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" Error Message \"), $data.errorMessage ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_cache[41] || (_cache[41] = _createElementVNode(\"i\", {\n    class: \"fas fa-exclamation-triangle me-2\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.errorMessage) + \" \", 1 /* TEXT */), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn-close\",\n    onClick: _cache[1] || (_cache[1] = $event => $data.errorMessage = ''),\n    \"aria-label\": \"Close\"\n  })])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Page Header \"), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", null, [_cache[44] || (_cache[44] = _createElementVNode(\"h1\", {\n    class: \"h3 mb-0 text-gray-800\"\n  }, \"Document Request Management\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_9, [_cache[43] || (_cache[43] = _createTextVNode(\" Manage and process document requests \")), $data.lastRefresh ? (_openBlock(), _createElementBlock(\"span\", _hoisted_10, [_cache[42] || (_cache[42] = _createElementVNode(\"i\", {\n    class: \"fas fa-clock text-muted\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" Last updated: \" + _toDisplayString($options.formatTime($data.lastRefresh)), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_11, [_createCommentVNode(\" Real-time status indicator \"), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"span\", {\n    class: _normalizeClass([\"badge\", $data.autoRefreshEnabled ? 'bg-success' : 'bg-secondary'])\n  }, [$data.autoRefreshEnabled ? (_openBlock(), _createElementBlock(\"i\", _hoisted_13)) : (_openBlock(), _createElementBlock(\"i\", _hoisted_14)), _createTextVNode(\" \" + _toDisplayString($data.autoRefreshEnabled ? 'Live' : 'Paused'), 1 /* TEXT */)], 2 /* CLASS */)]), _createElementVNode(\"button\", {\n    class: \"btn btn-outline-secondary btn-sm\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.toggleAutoRefresh && $options.toggleAutoRefresh(...args)),\n    title: $data.autoRefreshEnabled ? 'Disable auto-refresh' : 'Enable auto-refresh'\n  }, [_createElementVNode(\"i\", {\n    class: _normalizeClass([\"fas\", $data.autoRefreshEnabled ? 'fa-pause' : 'fa-play'])\n  }, null, 2 /* CLASS */)], 8 /* PROPS */, _hoisted_15), _createElementVNode(\"button\", {\n    class: \"btn btn-outline-primary btn-sm\",\n    onClick: _cache[3] || (_cache[3] = $event => $data.showFilters = !$data.showFilters)\n  }, [_cache[45] || (_cache[45] = _createElementVNode(\"i\", {\n    class: \"fas fa-filter me-1\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.showFilters ? 'Hide' : 'Show') + \" Filters \", 1 /* TEXT */)]), _createElementVNode(\"button\", {\n    class: \"btn btn-success btn-sm\",\n    onClick: _cache[4] || (_cache[4] = (...args) => $options.exportRequests && $options.exportRequests(...args)),\n    disabled: $data.loading\n  }, _cache[46] || (_cache[46] = [_createElementVNode(\"i\", {\n    class: \"fas fa-download me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Export CSV \")]), 8 /* PROPS */, _hoisted_16), _createElementVNode(\"button\", {\n    class: \"btn btn-primary btn-sm\",\n    onClick: _cache[5] || (_cache[5] = (...args) => $options.refreshRequestsData && $options.refreshRequestsData(...args)),\n    disabled: $data.loading\n  }, [_createElementVNode(\"i\", {\n    class: _normalizeClass([\"fas fa-sync-alt me-1\", {\n      'fa-spin': $data.loading\n    }])\n  }, null, 2 /* CLASS */), _cache[47] || (_cache[47] = _createTextVNode(\" Refresh \"))], 8 /* PROPS */, _hoisted_17)])])])]), _createCommentVNode(\" Request Statistics \"), _createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, [_cache[48] || (_cache[48] = _createElementVNode(\"div\", {\n    class: \"text-xs fw-bold text-primary text-uppercase mb-1\"\n  }, \" Total Requests \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_24, _toDisplayString($data.requestStats.total || 0), 1 /* TEXT */)]), _cache[49] || (_cache[49] = _createElementVNode(\"div\", {\n    class: \"col-auto\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-file-alt fa-2x text-muted\"\n  })], -1 /* HOISTED */))])])])]), _createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"div\", _hoisted_29, [_cache[50] || (_cache[50] = _createElementVNode(\"div\", {\n    class: \"text-xs fw-bold text-warning text-uppercase mb-1\"\n  }, \" Pending \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_30, _toDisplayString($data.requestStats.pending || 0), 1 /* TEXT */)]), _cache[51] || (_cache[51] = _createElementVNode(\"div\", {\n    class: \"col-auto\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-clock fa-2x text-muted\"\n  })], -1 /* HOISTED */))])])])]), _createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"div\", _hoisted_34, [_createElementVNode(\"div\", _hoisted_35, [_cache[52] || (_cache[52] = _createElementVNode(\"div\", {\n    class: \"text-xs fw-bold text-success text-uppercase mb-1\"\n  }, \" Completed \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_36, _toDisplayString($data.requestStats.completed || 0), 1 /* TEXT */)]), _cache[53] || (_cache[53] = _createElementVNode(\"div\", {\n    class: \"col-auto\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-check-circle fa-2x text-muted\"\n  })], -1 /* HOISTED */))])])])]), _createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"div\", _hoisted_39, [_createElementVNode(\"div\", _hoisted_40, [_createElementVNode(\"div\", _hoisted_41, [_cache[54] || (_cache[54] = _createElementVNode(\"div\", {\n    class: \"text-xs fw-bold text-info text-uppercase mb-1\"\n  }, \" Approved \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_42, _toDisplayString($data.requestStats.approved || 0), 1 /* TEXT */)]), _cache[55] || (_cache[55] = _createElementVNode(\"div\", {\n    class: \"col-auto\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-thumbs-up fa-2x text-muted\"\n  })], -1 /* HOISTED */))])])])])]), _createCommentVNode(\" Filters Panel \"), $data.showFilters ? (_openBlock(), _createElementBlock(\"div\", _hoisted_43, [_cache[65] || (_cache[65] = _createElementVNode(\"div\", {\n    class: \"card-header py-3\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"m-0 fw-bold text-primary\"\n  }, \"Filter Requests\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_44, [_createElementVNode(\"div\", _hoisted_45, [_createElementVNode(\"div\", _hoisted_46, [_cache[56] || (_cache[56] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Search\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $data.filters.search = $event),\n    placeholder: \"Search by name, email, or request number\",\n    onKeyup: _cache[7] || (_cache[7] = _withKeys((...args) => $options.applyFilters && $options.applyFilters(...args), [\"enter\"]))\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $data.filters.search]])]), _createElementVNode(\"div\", _hoisted_47, [_cache[58] || (_cache[58] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Status\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select\",\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $data.filters.status = $event)\n  }, [_cache[57] || (_cache[57] = _createElementVNode(\"option\", {\n    value: \"\"\n  }, \"All Statuses\", -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.statusOptions, status => {\n    return _openBlock(), _createElementBlock(\"option\", {\n      key: status.id,\n      value: status.status_name\n    }, _toDisplayString($options.formatStatus(status.status_name)), 9 /* TEXT, PROPS */, _hoisted_48);\n  }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.filters.status]])]), _createElementVNode(\"div\", _hoisted_49, [_cache[60] || (_cache[60] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Document Type\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select\",\n    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $data.filters.document_type = $event)\n  }, _cache[59] || (_cache[59] = [_createElementVNode(\"option\", {\n    value: \"\"\n  }, \"All Types\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"barangay_clearance\"\n  }, \"Barangay Clearance\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"cedula\"\n  }, \"Cedula\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.filters.document_type]])]), _createElementVNode(\"div\", _hoisted_50, [_cache[61] || (_cache[61] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Date From\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"date\",\n    class: \"form-control\",\n    \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $data.filters.date_from = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.filters.date_from]])]), _createElementVNode(\"div\", _hoisted_51, [_cache[62] || (_cache[62] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Date To\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"date\",\n    class: \"form-control\",\n    \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $data.filters.date_to = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.filters.date_to]])]), _createElementVNode(\"div\", _hoisted_52, [_createElementVNode(\"div\", _hoisted_53, [_createElementVNode(\"button\", {\n    class: \"btn btn-primary btn-sm\",\n    onClick: _cache[12] || (_cache[12] = (...args) => $options.applyFilters && $options.applyFilters(...args))\n  }, _cache[63] || (_cache[63] = [_createElementVNode(\"i\", {\n    class: \"fas fa-search\"\n  }, null, -1 /* HOISTED */)])), _createElementVNode(\"button\", {\n    class: \"btn btn-outline-secondary btn-sm\",\n    onClick: _cache[13] || (_cache[13] = (...args) => $options.clearFilters && $options.clearFilters(...args))\n  }, _cache[64] || (_cache[64] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times\"\n  }, null, -1 /* HOISTED */)]))])])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Bulk Actions Panel \"), $data.selectedRequests.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_54, [_createElementVNode(\"div\", _hoisted_55, [_createElementVNode(\"h6\", _hoisted_56, [_cache[66] || (_cache[66] = _createElementVNode(\"i\", {\n    class: \"fas fa-tasks me-2\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" Bulk Actions (\" + _toDisplayString($data.selectedRequests.length) + \" selected) \", 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_57, [_createElementVNode(\"div\", _hoisted_58, [_createElementVNode(\"div\", _hoisted_59, [_cache[68] || (_cache[68] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Action\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select\",\n    \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $data.bulkAction = $event)\n  }, [_cache[67] || (_cache[67] = _createElementVNode(\"option\", {\n    value: \"\"\n  }, \"Select Action\", -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.statusOptions, status => {\n    return _openBlock(), _createElementBlock(\"option\", {\n      key: status.id,\n      value: status.id\n    }, \" Change to \" + _toDisplayString($options.formatStatus(status.status_name)), 9 /* TEXT, PROPS */, _hoisted_60);\n  }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.bulkAction]])]), _createElementVNode(\"div\", _hoisted_61, [_cache[69] || (_cache[69] = _createElementVNode(\"label\", {\n    class: \"form-label\"\n  }, \"Reason (Optional)\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    \"onUpdate:modelValue\": _cache[15] || (_cache[15] = $event => $data.bulkReason = $event),\n    placeholder: \"Enter reason for bulk action\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.bulkReason]])]), _createElementVNode(\"div\", _hoisted_62, [_createElementVNode(\"div\", _hoisted_63, [_createElementVNode(\"button\", {\n    class: \"btn btn-warning\",\n    onClick: _cache[16] || (_cache[16] = (...args) => $options.performBulkAction && $options.performBulkAction(...args)),\n    disabled: !$data.bulkAction\n  }, _cache[70] || (_cache[70] = [_createElementVNode(\"i\", {\n    class: \"fas fa-play me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Apply \")]), 8 /* PROPS */, _hoisted_64), _createElementVNode(\"button\", {\n    class: \"btn btn-outline-secondary\",\n    onClick: _cache[17] || (_cache[17] = $event => $data.selectedRequests = [])\n  }, _cache[71] || (_cache[71] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Cancel \")]))])])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" View Toggle \"), _createElementVNode(\"div\", _hoisted_65, [_createElementVNode(\"div\", _hoisted_66, [_createElementVNode(\"div\", _hoisted_67, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"radio\",\n    class: \"btn-check\",\n    name: \"viewMode\",\n    id: \"cardView\",\n    \"onUpdate:modelValue\": _cache[18] || (_cache[18] = $event => $data.viewMode = $event),\n    value: \"card\",\n    autocomplete: \"off\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.viewMode]]), _cache[72] || (_cache[72] = _createElementVNode(\"label\", {\n    class: \"btn btn-outline-primary btn-sm\",\n    for: \"cardView\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-th-large me-1\"\n  }), _createTextVNode(\"Cards \")], -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"radio\",\n    class: \"btn-check\",\n    name: \"viewMode\",\n    id: \"tableView\",\n    \"onUpdate:modelValue\": _cache[19] || (_cache[19] = $event => $data.viewMode = $event),\n    value: \"table\",\n    autocomplete: \"off\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelRadio, $data.viewMode]]), _cache[73] || (_cache[73] = _createElementVNode(\"label\", {\n    class: \"btn btn-outline-primary btn-sm\",\n    for: \"tableView\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-table me-1\"\n  }), _createTextVNode(\"Table \")], -1 /* HOISTED */))]), _createElementVNode(\"div\", _hoisted_68, [_createElementVNode(\"span\", _hoisted_69, \" Showing \" + _toDisplayString(($data.pagination.currentPage - 1) * $data.pagination.itemsPerPage + 1) + \" - \" + _toDisplayString(Math.min($data.pagination.currentPage * $data.pagination.itemsPerPage, $data.pagination.totalItems)) + \" of \" + _toDisplayString($data.pagination.totalItems) + \" requests \", 1 /* TEXT */), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select form-select-sm\",\n    style: {\n      \"width\": \"auto\"\n    },\n    \"onUpdate:modelValue\": _cache[20] || (_cache[20] = $event => $data.pagination.itemsPerPage = $event),\n    onChange: _cache[21] || (_cache[21] = $event => $options.changeItemsPerPage($data.pagination.itemsPerPage))\n  }, _cache[74] || (_cache[74] = [_createElementVNode(\"option\", {\n    value: \"10\"\n  }, \"10 per page\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"25\"\n  }, \"25 per page\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"50\"\n  }, \"50 per page\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"100\"\n  }, \"100 per page\", -1 /* HOISTED */)]), 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.pagination.itemsPerPage]])])]), _createElementVNode(\"div\", _hoisted_70, [$data.requests.length > 0 ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    class: \"btn btn-sm btn-outline-secondary\",\n    onClick: _cache[22] || (_cache[22] = (...args) => $options.selectAllRequests && $options.selectAllRequests(...args))\n  }, [_cache[75] || (_cache[75] = _createElementVNode(\"i\", {\n    class: \"fas fa-check-square me-1\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.selectedRequests.length === $data.requests.length ? 'Deselect All' : 'Select All'), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Card View \"), $data.viewMode === 'card' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_71, [_createCommentVNode(\" Empty State \"), $data.requests.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_72, _cache[76] || (_cache[76] = [_createElementVNode(\"div\", {\n    class: \"empty-state-icon mb-3\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-inbox fa-4x text-muted\"\n  })], -1 /* HOISTED */), _createElementVNode(\"h5\", {\n    class: \"text-muted mb-2\"\n  }, \"No Document Requests Found\", -1 /* HOISTED */), _createElementVNode(\"p\", {\n    class: \"text-muted\"\n  }, \"There are no document requests matching your current filters.\", -1 /* HOISTED */)]))) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" Request Cards \"), _createElementVNode(\"div\", _hoisted_73, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.requests, request => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: request.id,\n      class: \"col-xl-4 col-lg-6 col-md-6\"\n    }, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"request-card\", {\n        'selected': $data.selectedRequests.includes(request.id)\n      }])\n    }, [_createCommentVNode(\" Card Header \"), _createElementVNode(\"div\", _hoisted_74, [_createElementVNode(\"div\", _hoisted_75, [_createElementVNode(\"div\", _hoisted_76, [_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      class: \"form-check-input\",\n      checked: $data.selectedRequests.includes(request.id),\n      onChange: $event => $options.toggleRequestSelection(request.id)\n    }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_77), _createElementVNode(\"div\", _hoisted_78, [_createElementVNode(\"span\", _hoisted_79, _toDisplayString(request.request_number), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_80, [_createElementVNode(\"div\", _hoisted_81, [_cache[81] || (_cache[81] = _createElementVNode(\"button\", {\n      class: \"btn btn-sm btn-light dropdown-toggle\",\n      type: \"button\",\n      \"data-bs-toggle\": \"dropdown\",\n      \"aria-expanded\": \"false\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-ellipsis-v\"\n    })], -1 /* HOISTED */)), _createElementVNode(\"ul\", _hoisted_82, [_createElementVNode(\"li\", null, [_createElementVNode(\"a\", {\n      class: \"dropdown-item\",\n      href: \"#\",\n      onClick: _withModifiers($event => $options.viewRequestDetails(request.id), [\"prevent\"])\n    }, [...(_cache[77] || (_cache[77] = [_createElementVNode(\"i\", {\n      class: \"fas fa-eye me-2\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\"View Details \")]))], 8 /* PROPS */, _hoisted_83)]), _cache[80] || (_cache[80] = _createElementVNode(\"li\", null, [_createElementVNode(\"hr\", {\n      class: \"dropdown-divider\"\n    })], -1 /* HOISTED */)), _createElementVNode(\"li\", null, [_createElementVNode(\"a\", {\n      class: \"dropdown-item text-success\",\n      href: \"#\",\n      onClick: _withModifiers($event => $options.approveRequest(request.id), [\"prevent\"])\n    }, [...(_cache[78] || (_cache[78] = [_createElementVNode(\"i\", {\n      class: \"fas fa-check me-2\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\"Approve \")]))], 8 /* PROPS */, _hoisted_84)]), _createElementVNode(\"li\", null, [_createElementVNode(\"a\", {\n      class: \"dropdown-item text-danger\",\n      href: \"#\",\n      onClick: _withModifiers($event => $options.rejectRequest(request.id, _ctx.prompt('Rejection reason:')), [\"prevent\"])\n    }, [...(_cache[79] || (_cache[79] = [_createElementVNode(\"i\", {\n      class: \"fas fa-times me-2\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\"Reject \")]))], 8 /* PROPS */, _hoisted_85)])])])])])]), _createCommentVNode(\" Card Body \"), _createElementVNode(\"div\", _hoisted_86, [_createCommentVNode(\" Client Info \"), _createElementVNode(\"div\", _hoisted_87, [_createElementVNode(\"div\", _hoisted_88, [_cache[82] || (_cache[82] = _createElementVNode(\"div\", {\n      class: \"client-avatar\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-user-circle fa-2x text-primary\"\n    })], -1 /* HOISTED */)), _createElementVNode(\"div\", null, [_createElementVNode(\"h6\", _hoisted_89, _toDisplayString(request.client_name), 1 /* TEXT */), _createElementVNode(\"small\", _hoisted_90, _toDisplayString(request.client_email), 1 /* TEXT */)])])]), _createCommentVNode(\" Document Type \"), _createElementVNode(\"div\", _hoisted_91, [_createElementVNode(\"div\", _hoisted_92, [_cache[83] || (_cache[83] = _createElementVNode(\"i\", {\n      class: \"fas fa-file-alt text-info\"\n    }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_93, _toDisplayString(request.document_type), 1 /* TEXT */)])]), _createCommentVNode(\" Status and Amount \"), _createElementVNode(\"div\", _hoisted_94, [_createElementVNode(\"div\", _hoisted_95, [_createElementVNode(\"div\", _hoisted_96, [_createElementVNode(\"div\", _hoisted_97, [_cache[84] || (_cache[84] = _createElementVNode(\"small\", {\n      class: \"text-muted d-block\"\n    }, \"Status\", -1 /* HOISTED */)), _createElementVNode(\"span\", {\n      class: _normalizeClass([\"badge\", `bg-${$options.getStatusColor(request.status_name)}`])\n    }, _toDisplayString($options.formatStatus(request.status_name)), 3 /* TEXT, CLASS */)])]), _createElementVNode(\"div\", _hoisted_98, [_createElementVNode(\"div\", _hoisted_99, [_cache[85] || (_cache[85] = _createElementVNode(\"small\", {\n      class: \"text-muted d-block\"\n    }, \"Amount\", -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_100, _toDisplayString($options.formatCurrency(request.total_fee)), 1 /* TEXT */)])])])]), _createCommentVNode(\" Date \"), _createElementVNode(\"div\", _hoisted_101, [_createElementVNode(\"small\", _hoisted_102, [_cache[86] || (_cache[86] = _createElementVNode(\"i\", {\n      class: \"fas fa-calendar-alt me-1\"\n    }, null, -1 /* HOISTED */)), _createTextVNode(\" Submitted \" + _toDisplayString($options.formatDate(request.requested_at)), 1 /* TEXT */)])])]), _createCommentVNode(\" Card Footer \"), _createElementVNode(\"div\", _hoisted_103, [_createElementVNode(\"div\", _hoisted_104, [_createElementVNode(\"button\", {\n      class: \"btn btn-sm btn-outline-primary flex-fill\",\n      onClick: $event => $options.viewRequestDetails(request.id)\n    }, [...(_cache[87] || (_cache[87] = [_createElementVNode(\"i\", {\n      class: \"fas fa-eye me-1\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\"View \")]))], 8 /* PROPS */, _hoisted_105), $options.canApprove(request) ? (_openBlock(), _createElementBlock(\"button\", {\n      key: 0,\n      class: \"btn btn-sm btn-success\",\n      onClick: $event => $options.quickApprove(request),\n      title: 'Approve Request',\n      disabled: $data.loading\n    }, [...(_cache[88] || (_cache[88] = [_createElementVNode(\"i\", {\n      class: \"fas fa-check\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_106)) : _createCommentVNode(\"v-if\", true), $options.canReject(request) ? (_openBlock(), _createElementBlock(\"button\", {\n      key: 1,\n      class: \"btn btn-sm btn-danger\",\n      onClick: $event => $options.showQuickRejectModal(request),\n      title: 'Reject Request',\n      disabled: $data.loading\n    }, [...(_cache[89] || (_cache[89] = [_createElementVNode(\"i\", {\n      class: \"fas fa-times\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_107)) : _createCommentVNode(\"v-if\", true)])])], 2 /* CLASS */)]);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 4\n  }, [_createCommentVNode(\" Table View \"), _createElementVNode(\"div\", _hoisted_108, [_createCommentVNode(\" Table Header \"), _createElementVNode(\"div\", _hoisted_109, [_createElementVNode(\"div\", _hoisted_110, [_cache[91] || (_cache[91] = _createElementVNode(\"h5\", {\n    class: \"mb-0 fw-bold text-dark\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-file-alt me-2 text-primary\"\n  }), _createTextVNode(\" Document Requests \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_111, [$data.requests.length > 0 ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    class: \"btn btn-sm btn-outline-secondary\",\n    onClick: _cache[23] || (_cache[23] = (...args) => $options.selectAllRequests && $options.selectAllRequests(...args))\n  }, [_cache[90] || (_cache[90] = _createElementVNode(\"i\", {\n    class: \"fas fa-check-square me-1\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.selectedRequests.length === $data.requests.length ? 'Deselect All' : 'Select All'), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])])]), _createCommentVNode(\" Empty State \"), $data.requests.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_112, _cache[92] || (_cache[92] = [_createStaticVNode(\"<div class=\\\"empty-content\\\" data-v-1d5e65f3><div class=\\\"empty-icon\\\" data-v-1d5e65f3><i class=\\\"fas fa-inbox\\\" data-v-1d5e65f3></i></div><h6 class=\\\"empty-title\\\" data-v-1d5e65f3>No Document Requests Found</h6><p class=\\\"empty-text\\\" data-v-1d5e65f3>There are no document requests matching your current filters.</p></div>\", 1)]))) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" Modern Table \"), _createElementVNode(\"div\", _hoisted_113, [_createCommentVNode(\" Table Row \"), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.requests, request => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: request.id,\n      class: _normalizeClass([\"table-row\", {\n        'selected': $data.selectedRequests.includes(request.id)\n      }])\n    }, [_createCommentVNode(\" Selection Column \"), _createElementVNode(\"div\", _hoisted_114, [_createElementVNode(\"input\", {\n      type: \"checkbox\",\n      class: \"form-check-input\",\n      checked: $data.selectedRequests.includes(request.id),\n      onChange: $event => $options.toggleRequestSelection(request.id)\n    }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_115)]), _createCommentVNode(\" Request Number Column \"), _createElementVNode(\"div\", _hoisted_116, [_createElementVNode(\"div\", _hoisted_117, [_createElementVNode(\"span\", _hoisted_118, _toDisplayString(request.request_number), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_119, \"ID: \" + _toDisplayString(request.id), 1 /* TEXT */)])]), _createCommentVNode(\" Client Column \"), _createElementVNode(\"div\", _hoisted_120, [_createElementVNode(\"div\", _hoisted_121, [_cache[93] || (_cache[93] = _createElementVNode(\"div\", {\n      class: \"client-avatar-sm\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-user\"\n    })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_122, [_createElementVNode(\"div\", _hoisted_123, _toDisplayString(request.client_name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_124, _toDisplayString(request.client_email), 1 /* TEXT */)])])]), _createCommentVNode(\" Document Type Column \"), _createElementVNode(\"div\", _hoisted_125, [_createElementVNode(\"div\", _hoisted_126, [_cache[94] || (_cache[94] = _createElementVNode(\"i\", {\n      class: \"fas fa-file-alt me-2\"\n    }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", null, _toDisplayString(request.document_type), 1 /* TEXT */)])]), _createCommentVNode(\" Status Column \"), _createElementVNode(\"div\", _hoisted_127, [_createElementVNode(\"span\", {\n      class: _normalizeClass([\"status-badge\", `status-${$options.getStatusColor(request.status_name)}`])\n    }, [_cache[95] || (_cache[95] = _createElementVNode(\"i\", {\n      class: \"fas fa-circle status-indicator\"\n    }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($options.formatStatus(request.status_name)), 1 /* TEXT */)], 2 /* CLASS */)]), _createCommentVNode(\" Amount Column \"), _createElementVNode(\"div\", _hoisted_128, [_createElementVNode(\"div\", _hoisted_129, [_createElementVNode(\"span\", _hoisted_130, _toDisplayString($options.formatCurrency(request.total_fee)), 1 /* TEXT */), _cache[96] || (_cache[96] = _createElementVNode(\"span\", {\n      class: \"currency\"\n    }, \"PHP\", -1 /* HOISTED */))])]), _createCommentVNode(\" Date Column \"), _createElementVNode(\"div\", _hoisted_131, [_createElementVNode(\"div\", _hoisted_132, [_createElementVNode(\"span\", _hoisted_133, _toDisplayString($options.formatDate(request.requested_at)), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_134, _toDisplayString($options.formatTime(request.requested_at)), 1 /* TEXT */)])]), _createCommentVNode(\" Actions Column \"), _createElementVNode(\"div\", _hoisted_135, [_createElementVNode(\"div\", _hoisted_136, [_createElementVNode(\"button\", {\n      class: \"action-btn view-btn\",\n      onClick: $event => $options.viewRequestDetails(request.id),\n      title: \"View Details\"\n    }, [...(_cache[97] || (_cache[97] = [_createElementVNode(\"i\", {\n      class: \"fas fa-eye\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_137), $options.canApprove(request) ? (_openBlock(), _createElementBlock(\"button\", {\n      key: 0,\n      class: \"action-btn approve-btn\",\n      onClick: $event => $options.quickApprove(request),\n      title: \"Approve\",\n      disabled: $data.loading\n    }, [...(_cache[98] || (_cache[98] = [_createElementVNode(\"i\", {\n      class: \"fas fa-check\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_138)) : _createCommentVNode(\"v-if\", true), $options.canReject(request) ? (_openBlock(), _createElementBlock(\"button\", {\n      key: 1,\n      class: \"action-btn reject-btn\",\n      onClick: $event => $options.showQuickRejectModal(request),\n      title: \"Reject\",\n      disabled: $data.loading\n    }, [...(_cache[99] || (_cache[99] = [_createElementVNode(\"i\", {\n      class: \"fas fa-times\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_139)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_140, [_cache[104] || (_cache[104] = _createElementVNode(\"button\", {\n      class: \"action-btn more-btn dropdown-toggle\",\n      type: \"button\",\n      \"data-bs-toggle\": \"dropdown\",\n      \"aria-expanded\": \"false\",\n      title: \"More Actions\"\n    }, [_createElementVNode(\"i\", {\n      class: \"fas fa-ellipsis-v\"\n    })], -1 /* HOISTED */)), _createElementVNode(\"ul\", _hoisted_141, [_createElementVNode(\"li\", null, [_createElementVNode(\"a\", {\n      class: \"dropdown-item\",\n      href: \"#\",\n      onClick: _withModifiers($event => $options.viewRequestDetails(request.id), [\"prevent\"])\n    }, [...(_cache[100] || (_cache[100] = [_createElementVNode(\"i\", {\n      class: \"fas fa-eye me-2\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\"View Details \")]))], 8 /* PROPS */, _hoisted_142)]), _cache[103] || (_cache[103] = _createElementVNode(\"li\", null, [_createElementVNode(\"hr\", {\n      class: \"dropdown-divider\"\n    })], -1 /* HOISTED */)), _createElementVNode(\"li\", null, [_createElementVNode(\"a\", {\n      class: \"dropdown-item text-success\",\n      href: \"#\",\n      onClick: _withModifiers($event => $options.approveRequest(request.id), [\"prevent\"])\n    }, [...(_cache[101] || (_cache[101] = [_createElementVNode(\"i\", {\n      class: \"fas fa-check me-2\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\"Approve \")]))], 8 /* PROPS */, _hoisted_143)]), _createElementVNode(\"li\", null, [_createElementVNode(\"a\", {\n      class: \"dropdown-item text-danger\",\n      href: \"#\",\n      onClick: _withModifiers($event => $options.rejectRequest(request.id, _ctx.prompt('Rejection reason:')), [\"prevent\"])\n    }, [...(_cache[102] || (_cache[102] = [_createElementVNode(\"i\", {\n      class: \"fas fa-times me-2\"\n    }, null, -1 /* HOISTED */), _createTextVNode(\"Reject \")]))], 8 /* PROPS */, _hoisted_144)])])])])])], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" Pagination \"), $data.pagination.totalPages > 1 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_145, [_createElementVNode(\"nav\", _hoisted_146, [_createElementVNode(\"ul\", _hoisted_147, [_createElementVNode(\"li\", {\n    class: _normalizeClass([\"page-item\", {\n      disabled: $data.pagination.currentPage === 1\n    }])\n  }, [_createElementVNode(\"a\", {\n    class: \"page-link\",\n    href: \"#\",\n    onClick: _cache[24] || (_cache[24] = _withModifiers($event => $options.changePage($data.pagination.currentPage - 1), [\"prevent\"]))\n  }, _cache[105] || (_cache[105] = [_createElementVNode(\"i\", {\n    class: \"fas fa-chevron-left\"\n  }, null, -1 /* HOISTED */)]))], 2 /* CLASS */), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(Math.min($data.pagination.totalPages, 10), page => {\n    return _openBlock(), _createElementBlock(\"li\", {\n      key: page,\n      class: _normalizeClass([\"page-item\", {\n        active: page === $data.pagination.currentPage\n      }])\n    }, [_createElementVNode(\"a\", {\n      class: \"page-link\",\n      href: \"#\",\n      onClick: _withModifiers($event => $options.changePage(page), [\"prevent\"])\n    }, _toDisplayString(page), 9 /* TEXT, PROPS */, _hoisted_148)], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */)), _createElementVNode(\"li\", {\n    class: _normalizeClass([\"page-item\", {\n      disabled: $data.pagination.currentPage === $data.pagination.totalPages\n    }])\n  }, [_createElementVNode(\"a\", {\n    class: \"page-link\",\n    href: \"#\",\n    onClick: _cache[25] || (_cache[25] = _withModifiers($event => $options.changePage($data.pagination.currentPage + 1), [\"prevent\"]))\n  }, _cache[106] || (_cache[106] = [_createElementVNode(\"i\", {\n    class: \"fas fa-chevron-right\"\n  }, null, -1 /* HOISTED */)]))], 2 /* CLASS */)])])])) : _createCommentVNode(\"v-if\", true)])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" Request Details Modal \"), $data.showRequestDetails && $data.currentRequest ? (_openBlock(), _createElementBlock(\"div\", _hoisted_149, [_createElementVNode(\"div\", _hoisted_150, [_createElementVNode(\"div\", _hoisted_151, [_createElementVNode(\"div\", _hoisted_152, [_createElementVNode(\"h5\", _hoisted_153, [_cache[107] || (_cache[107] = _createElementVNode(\"i\", {\n    class: \"fas fa-file-alt me-2\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" Request Details - \" + _toDisplayString($data.currentRequest.request_number), 1 /* TEXT */)]), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn-close\",\n    onClick: _cache[26] || (_cache[26] = $event => $data.showRequestDetails = false)\n  })]), _createElementVNode(\"div\", _hoisted_154, [_createElementVNode(\"div\", _hoisted_155, [_createCommentVNode(\" Left Column - Request Information \"), _createElementVNode(\"div\", _hoisted_156, [_createCommentVNode(\" Basic Information \"), _createElementVNode(\"div\", _hoisted_157, [_cache[116] || (_cache[116] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"mb-0\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-info-circle me-2\"\n  }), _createTextVNode(\"Request Information\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_158, [_createElementVNode(\"div\", _hoisted_159, [_createElementVNode(\"div\", _hoisted_160, [_createElementVNode(\"div\", _hoisted_161, [_cache[108] || (_cache[108] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Request Number\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_162, _toDisplayString($data.currentRequest.request_number), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_163, [_cache[109] || (_cache[109] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Document Type\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_164, [_createElementVNode(\"span\", _hoisted_165, _toDisplayString($data.currentRequest.document_type), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_166, [_cache[110] || (_cache[110] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Purpose Category\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_167, _toDisplayString($data.currentRequest.purpose_category), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_168, [_cache[111] || (_cache[111] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Purpose Details\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_169, _toDisplayString($data.currentRequest.purpose_details || 'Not specified'), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_170, [_createElementVNode(\"div\", _hoisted_171, [_cache[112] || (_cache[112] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Current Status\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_172, [_createElementVNode(\"span\", {\n    class: _normalizeClass([\"badge\", `bg-${$options.getStatusColor($data.currentRequest.status_name)}`])\n  }, _toDisplayString($options.formatStatus($data.currentRequest.status_name)), 3 /* TEXT, CLASS */)])]), _createElementVNode(\"div\", _hoisted_173, [_cache[113] || (_cache[113] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Priority\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_174, [_createElementVNode(\"span\", {\n    class: _normalizeClass([\"badge\", $data.currentRequest.priority === 'high' ? 'bg-danger' : $data.currentRequest.priority === 'medium' ? 'bg-warning' : 'bg-secondary'])\n  }, _toDisplayString($data.currentRequest.priority || 'Normal'), 3 /* TEXT, CLASS */)])]), _createElementVNode(\"div\", _hoisted_175, [_cache[114] || (_cache[114] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Delivery Method\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_176, _toDisplayString($data.currentRequest.delivery_method || 'Pickup'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_177, [_cache[115] || (_cache[115] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Date Submitted\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_178, _toDisplayString($options.formatDateTime($data.currentRequest.requested_at)), 1 /* TEXT */)])])])])]), _createCommentVNode(\" Client Information \"), _createElementVNode(\"div\", _hoisted_179, [_cache[121] || (_cache[121] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"mb-0\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user me-2\"\n  }), _createTextVNode(\"Client Information\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_180, [_createElementVNode(\"div\", _hoisted_181, [_createElementVNode(\"div\", _hoisted_182, [_createElementVNode(\"div\", _hoisted_183, [_cache[117] || (_cache[117] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Full Name\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_184, _toDisplayString($data.currentRequest.client_name), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_185, [_cache[118] || (_cache[118] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Email Address\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_186, [_createElementVNode(\"a\", {\n    href: `mailto:${$data.currentRequest.client_email}`\n  }, _toDisplayString($data.currentRequest.client_email), 9 /* TEXT, PROPS */, _hoisted_187)])])]), _createElementVNode(\"div\", _hoisted_188, [_createElementVNode(\"div\", _hoisted_189, [_cache[119] || (_cache[119] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Phone Number\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_190, [_createElementVNode(\"a\", {\n    href: `tel:${$data.currentRequest.client_phone}`\n  }, _toDisplayString($data.currentRequest.client_phone || 'Not provided'), 9 /* TEXT, PROPS */, _hoisted_191)])]), _createElementVNode(\"div\", _hoisted_192, [_cache[120] || (_cache[120] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Address\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_193, _toDisplayString($data.currentRequest.client_address || 'Not provided'), 1 /* TEXT */)])])])])]), _createCommentVNode(\" Document-Specific Details \"), $data.currentRequest.specific_details ? (_openBlock(), _createElementBlock(\"div\", _hoisted_194, [_cache[134] || (_cache[134] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"mb-0\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-clipboard-list me-2\"\n  }), _createTextVNode(\"Document-Specific Information\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_195, [_createCommentVNode(\" Barangay Clearance Details \"), $data.currentRequest.document_type === 'Barangay Clearance' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_196, [_createElementVNode(\"div\", _hoisted_197, [_createElementVNode(\"div\", _hoisted_198, [_createElementVNode(\"div\", _hoisted_199, [_cache[122] || (_cache[122] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Residency Period\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_200, _toDisplayString($data.currentRequest.specific_details.residency_period || 'Not specified'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_201, [_cache[123] || (_cache[123] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Civil Status\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_202, _toDisplayString($data.currentRequest.specific_details.civil_status || 'Not specified'), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_203, [_createElementVNode(\"div\", _hoisted_204, [_cache[124] || (_cache[124] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Occupation\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_205, _toDisplayString($data.currentRequest.specific_details.occupation || 'Not specified'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_206, [_cache[125] || (_cache[125] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Monthly Income\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_207, _toDisplayString($options.formatCurrency($data.currentRequest.specific_details.monthly_income) || 'Not specified'), 1 /* TEXT */)])])])])) : $data.currentRequest.document_type === 'Cedula' ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" Cedula Details \"), _createElementVNode(\"div\", null, [_createElementVNode(\"div\", _hoisted_208, [_createElementVNode(\"div\", _hoisted_209, [_createElementVNode(\"div\", _hoisted_210, [_cache[126] || (_cache[126] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Birth Date\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_211, _toDisplayString($options.formatDate($data.currentRequest.specific_details.birth_date) || 'Not specified'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_212, [_cache[127] || (_cache[127] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Birth Place\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_213, _toDisplayString($data.currentRequest.specific_details.birth_place || 'Not specified'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_214, [_cache[128] || (_cache[128] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Civil Status\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_215, _toDisplayString($data.currentRequest.specific_details.civil_status || 'Not specified'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_216, [_cache[129] || (_cache[129] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Citizenship\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_217, _toDisplayString($data.currentRequest.specific_details.citizenship || 'Not specified'), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_218, [_createElementVNode(\"div\", _hoisted_219, [_cache[130] || (_cache[130] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Occupation\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_220, _toDisplayString($data.currentRequest.specific_details.occupation || 'Not specified'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_221, [_cache[131] || (_cache[131] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Annual Income\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_222, _toDisplayString($options.formatCurrency($data.currentRequest.specific_details.annual_income) || 'Not specified'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_223, [_cache[132] || (_cache[132] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Tax Amount\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_224, _toDisplayString($options.formatCurrency($data.currentRequest.specific_details.tax_amount) || 'Not calculated'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_225, [_cache[133] || (_cache[133] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Interest/Penalty\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_226, _toDisplayString($options.formatCurrency($data.currentRequest.specific_details.interest_penalty) || 'None'), 1 /* TEXT */)])])])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)])])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" Right Column - Status Management \"), _createElementVNode(\"div\", _hoisted_227, [_createCommentVNode(\" Status Management \"), _createElementVNode(\"div\", _hoisted_228, [_cache[143] || (_cache[143] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"mb-0\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-tasks me-2\"\n  }), _createTextVNode(\"Status Management\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_229, [_createElementVNode(\"div\", _hoisted_230, [_cache[136] || (_cache[136] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Change Status\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select\",\n    \"onUpdate:modelValue\": _cache[27] || (_cache[27] = $event => $data.statusUpdateForm.status_id = $event)\n  }, [_cache[135] || (_cache[135] = _createElementVNode(\"option\", {\n    value: \"\"\n  }, \"Select new status\", -1 /* HOISTED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.statusOptions, status => {\n    return _openBlock(), _createElementBlock(\"option\", {\n      key: status.id,\n      value: status.id\n    }, _toDisplayString($options.formatStatus(status.status_name)), 9 /* TEXT, PROPS */, _hoisted_231);\n  }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.statusUpdateForm.status_id]])]), _createElementVNode(\"div\", _hoisted_232, [_cache[137] || (_cache[137] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Reason/Notes\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n    class: \"form-control\",\n    rows: \"3\",\n    \"onUpdate:modelValue\": _cache[28] || (_cache[28] = $event => $data.statusUpdateForm.reason = $event),\n    placeholder: \"Enter reason for status change (optional)\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.statusUpdateForm.reason]])]), _createElementVNode(\"div\", _hoisted_233, [_createElementVNode(\"button\", {\n    class: \"btn btn-primary\",\n    onClick: _cache[29] || (_cache[29] = (...args) => $options.updateRequestStatusFromModal && $options.updateRequestStatusFromModal(...args)),\n    disabled: !$data.statusUpdateForm.status_id\n  }, _cache[138] || (_cache[138] = [_createElementVNode(\"i\", {\n    class: \"fas fa-save me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Update Status \")]), 8 /* PROPS */, _hoisted_234), _createElementVNode(\"button\", {\n    class: \"btn btn-success\",\n    onClick: _cache[30] || (_cache[30] = (...args) => $options.approveRequestFromModal && $options.approveRequestFromModal(...args))\n  }, _cache[139] || (_cache[139] = [_createElementVNode(\"i\", {\n    class: \"fas fa-check me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Quick Approve \")])), _createElementVNode(\"button\", {\n    class: \"btn btn-danger\",\n    onClick: _cache[31] || (_cache[31] = $event => $data.showRejectForm = !$data.showRejectForm)\n  }, [_cache[140] || (_cache[140] = _createElementVNode(\"i\", {\n    class: \"fas fa-times me-1\"\n  }, null, -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.showRejectForm ? 'Cancel' : 'Reject Request'), 1 /* TEXT */)])]), _createCommentVNode(\" Rejection Form \"), $data.showRejectForm ? (_openBlock(), _createElementBlock(\"div\", _hoisted_235, [_createElementVNode(\"div\", _hoisted_236, [_cache[141] || (_cache[141] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold text-danger\"\n  }, \"Rejection Reason *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n    class: \"form-control\",\n    rows: \"3\",\n    \"onUpdate:modelValue\": _cache[32] || (_cache[32] = $event => $data.rejectForm.reason = $event),\n    placeholder: \"Please provide a detailed reason for rejection\",\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.rejectForm.reason]])]), _createElementVNode(\"div\", _hoisted_237, [_createElementVNode(\"button\", {\n    class: \"btn btn-danger\",\n    onClick: _cache[33] || (_cache[33] = (...args) => $options.rejectRequestFromModal && $options.rejectRequestFromModal(...args)),\n    disabled: !$data.rejectForm.reason || $data.rejectForm.reason.trim() === ''\n  }, _cache[142] || (_cache[142] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Confirm Rejection \")]), 8 /* PROPS */, _hoisted_238)])])) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" Payment Information \"), _createElementVNode(\"div\", _hoisted_239, [_cache[150] || (_cache[150] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"mb-0\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-credit-card me-2\"\n  }), _createTextVNode(\"Payment Information\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_240, [_createElementVNode(\"div\", _hoisted_241, [_cache[144] || (_cache[144] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Payment Method\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_242, _toDisplayString($data.currentRequest.payment_method || 'Not specified'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_243, [_cache[145] || (_cache[145] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold\"\n  }, \"Payment Status\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_244, [_createElementVNode(\"span\", {\n    class: _normalizeClass([\"badge\", $data.currentRequest.payment_status === 'paid' ? 'bg-success' : $data.currentRequest.payment_status === 'pending' ? 'bg-warning' : 'bg-secondary'])\n  }, _toDisplayString($data.currentRequest.payment_status || 'Unpaid'), 3 /* TEXT, CLASS */)])]), _createElementVNode(\"div\", _hoisted_245, [_createElementVNode(\"div\", _hoisted_246, [_createElementVNode(\"div\", _hoisted_247, [_cache[146] || (_cache[146] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold small\"\n  }, \"Base Fee\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_248, _toDisplayString($options.formatCurrency($data.currentRequest.base_fee)), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_249, [_createElementVNode(\"div\", _hoisted_250, [_cache[147] || (_cache[147] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold small\"\n  }, \"Additional Fees\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_251, _toDisplayString($options.formatCurrency($data.currentRequest.additional_fees)), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_252, [_createElementVNode(\"div\", _hoisted_253, [_cache[148] || (_cache[148] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold small\"\n  }, \"Processing Fee\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_254, _toDisplayString($options.formatCurrency($data.currentRequest.processing_fee)), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_255, [_createElementVNode(\"div\", _hoisted_256, [_cache[149] || (_cache[149] = _createElementVNode(\"label\", {\n    class: \"form-label fw-bold small\"\n  }, \"Total Amount\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_257, _toDisplayString($options.formatCurrency($data.currentRequest.total_fee)), 1 /* TEXT */)])])])])])])]), _createCommentVNode(\" Status History Timeline \"), _createElementVNode(\"div\", _hoisted_258, [_cache[156] || (_cache[156] = _createElementVNode(\"div\", {\n    class: \"card-header\"\n  }, [_createElementVNode(\"h6\", {\n    class: \"mb-0\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-history me-2\"\n  }), _createTextVNode(\"Status History\")])], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_259, [$data.currentRequest.status_history && $data.currentRequest.status_history.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_260, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.currentRequest.status_history, (history, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: history.id,\n      class: _normalizeClass([\"timeline-item\", {\n        'timeline-item-last': index === $data.currentRequest.status_history.length - 1\n      }])\n    }, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"timeline-marker\", `bg-${$options.getStatusColor(history.new_status_name)}`])\n    }, [...(_cache[151] || (_cache[151] = [_createElementVNode(\"i\", {\n      class: \"fas fa-circle\"\n    }, null, -1 /* HOISTED */)]))], 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_261, [_createElementVNode(\"div\", _hoisted_262, [_createElementVNode(\"span\", {\n      class: _normalizeClass([\"badge\", `bg-${$options.getStatusColor(history.new_status_name)}`])\n    }, _toDisplayString($options.formatStatus(history.new_status_name)), 3 /* TEXT, CLASS */), _createElementVNode(\"small\", _hoisted_263, _toDisplayString($options.formatDateTime(history.changed_at)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_264, [_createElementVNode(\"p\", _hoisted_265, [_cache[152] || (_cache[152] = _createElementVNode(\"strong\", null, \"Changed by:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString(history.changed_by_name), 1 /* TEXT */)]), history.old_status_name ? (_openBlock(), _createElementBlock(\"p\", _hoisted_266, [_cache[153] || (_cache[153] = _createElementVNode(\"strong\", null, \"From:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($options.formatStatus(history.old_status_name)), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), history.change_reason ? (_openBlock(), _createElementBlock(\"p\", _hoisted_267, [_cache[154] || (_cache[154] = _createElementVNode(\"strong\", null, \"Reason:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString(history.change_reason), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])])], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */))])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_268, _cache[155] || (_cache[155] = [_createElementVNode(\"i\", {\n    class: \"fas fa-history fa-2x mb-2\"\n  }, null, -1 /* HOISTED */), _createElementVNode(\"p\", null, \"No status history available\", -1 /* HOISTED */)])))])])]), _createElementVNode(\"div\", _hoisted_269, [_createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-secondary\",\n    onClick: _cache[34] || (_cache[34] = $event => $data.showRequestDetails = false)\n  }, _cache[157] || (_cache[157] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Close \")])), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-primary\",\n    onClick: _cache[35] || (_cache[35] = (...args) => $options.refreshRequestDetails && $options.refreshRequestDetails(...args))\n  }, _cache[158] || (_cache[158] = [_createElementVNode(\"i\", {\n    class: \"fas fa-sync-alt me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Refresh \")]))])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Quick Reject Modal \"), $data.showQuickReject && $data.selectedRequestForReject ? (_openBlock(), _createElementBlock(\"div\", _hoisted_270, [_createElementVNode(\"div\", _hoisted_271, [_createElementVNode(\"div\", _hoisted_272, [_createElementVNode(\"div\", _hoisted_273, [_cache[159] || (_cache[159] = _createElementVNode(\"h5\", {\n    class: \"modal-title\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-times-circle text-danger me-2\"\n  }), _createTextVNode(\" Reject Request \")], -1 /* HOISTED */)), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn-close\",\n    onClick: _cache[36] || (_cache[36] = (...args) => $options.closeQuickRejectModal && $options.closeQuickRejectModal(...args))\n  })]), _createElementVNode(\"div\", _hoisted_274, [_cache[166] || (_cache[166] = _createElementVNode(\"div\", {\n    class: \"alert alert-warning\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-exclamation-triangle me-2\"\n  }), _createTextVNode(\" You are about to reject this document request. This action will notify the client immediately. \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_275, [_cache[163] || (_cache[163] = _createElementVNode(\"strong\", null, \"Request Details:\", -1 /* HOISTED */)), _createElementVNode(\"ul\", _hoisted_276, [_createElementVNode(\"li\", null, [_cache[160] || (_cache[160] = _createElementVNode(\"strong\", null, \"Request Number:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.selectedRequestForReject.request_number), 1 /* TEXT */)]), _createElementVNode(\"li\", null, [_cache[161] || (_cache[161] = _createElementVNode(\"strong\", null, \"Document Type:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.selectedRequestForReject.document_type), 1 /* TEXT */)]), _createElementVNode(\"li\", null, [_cache[162] || (_cache[162] = _createElementVNode(\"strong\", null, \"Client:\", -1 /* HOISTED */)), _createTextVNode(\" \" + _toDisplayString($data.selectedRequestForReject.client_name), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_277, [_cache[164] || (_cache[164] = _createElementVNode(\"label\", {\n    for: \"rejectionReason\",\n    class: \"form-label\"\n  }, [_createElementVNode(\"strong\", null, [_createTextVNode(\"Rejection Reason \"), _createElementVNode(\"span\", {\n    class: \"text-danger\"\n  }, \"*\")])], -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"textarea\", {\n    id: \"rejectionReason\",\n    \"onUpdate:modelValue\": _cache[37] || (_cache[37] = $event => $data.quickRejectForm.reason = $event),\n    class: _normalizeClass([\"form-control\", {\n      'is-invalid': $data.quickRejectForm.error\n    }]),\n    rows: \"4\",\n    placeholder: \"Please provide a clear reason for rejecting this request...\"\n  }, null, 2 /* CLASS */), [[_vModelText, $data.quickRejectForm.reason]]), $data.quickRejectForm.error ? (_openBlock(), _createElementBlock(\"div\", _hoisted_278, _toDisplayString($data.quickRejectForm.error), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), _cache[165] || (_cache[165] = _createElementVNode(\"div\", {\n    class: \"form-text\"\n  }, \" This reason will be visible to the client and included in their notification. \", -1 /* HOISTED */))])]), _createElementVNode(\"div\", _hoisted_279, [_createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-secondary\",\n    onClick: _cache[38] || (_cache[38] = (...args) => $options.closeQuickRejectModal && $options.closeQuickRejectModal(...args)),\n    disabled: $data.quickRejectForm.loading\n  }, _cache[167] || (_cache[167] = [_createElementVNode(\"i\", {\n    class: \"fas fa-times me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Cancel \")]), 8 /* PROPS */, _hoisted_280), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-danger\",\n    onClick: _cache[39] || (_cache[39] = (...args) => $options.confirmQuickReject && $options.confirmQuickReject(...args)),\n    disabled: $data.quickRejectForm.loading || !$data.quickRejectForm.reason.trim()\n  }, [_cache[169] || (_cache[169] = _createElementVNode(\"i\", {\n    class: \"fas fa-times-circle me-1\"\n  }, null, -1 /* HOISTED */)), $data.quickRejectForm.loading ? (_openBlock(), _createElementBlock(\"span\", _hoisted_282, _cache[168] || (_cache[168] = [_createElementVNode(\"i\", {\n    class: \"fas fa-spinner fa-spin me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Rejecting... \")]))) : (_openBlock(), _createElementBlock(\"span\", _hoisted_283, \"Reject Request\"))], 8 /* PROPS */, _hoisted_281)])])])])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)])]);\n}", "map": {"version": 3, "names": ["class", "style", "role", "tabindex", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_AdminHeader", "userName", "$data", "adminData", "first_name", "showUserDropdown", "sidebarCollapsed", "activeMenu", "$options", "onSidebarToggle", "handleSidebarToggle", "onUserDropdownToggle", "handleUserDropdownToggle", "onMenuAction", "handleMenuAction", "onLogout", "handleLogout", "_createCommentVNode", "_createElementVNode", "_normalizeClass", "active", "isMobile", "onClick", "_cache", "args", "closeMobileSidebar", "_hoisted_2", "_component_AdminSidebar", "collapsed", "onMenuChange", "handleMenuChange", "onToggleSidebar", "loading", "_hoisted_3", "_Fragment", "key", "_hoisted_4", "errorMessage", "_hoisted_5", "_toDisplayString", "type", "$event", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "lastRefresh", "_hoisted_10", "formatTime", "_hoisted_11", "_hoisted_12", "autoRefreshEnabled", "_hoisted_13", "_hoisted_14", "toggleAutoRefresh", "title", "showFilters", "exportRequests", "disabled", "refreshRequestsData", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "requestStats", "total", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "_hoisted_30", "pending", "_hoisted_31", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_35", "_hoisted_36", "completed", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "approved", "_hoisted_43", "_hoisted_44", "_hoisted_45", "_hoisted_46", "filters", "search", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "applyFilters", "_hoisted_47", "status", "value", "_renderList", "statusOptions", "id", "status_name", "formatStatus", "_hoisted_48", "_hoisted_49", "document_type", "_hoisted_50", "date_from", "_hoisted_51", "date_to", "_hoisted_52", "_hoisted_53", "clearFilters", "selectedRequests", "length", "_hoisted_54", "_hoisted_55", "_hoisted_56", "_hoisted_57", "_hoisted_58", "_hoisted_59", "bulkAction", "_hoisted_60", "_hoisted_61", "bulkReason", "_hoisted_62", "_hoisted_63", "performBulkAction", "_hoisted_65", "_hoisted_66", "_hoisted_67", "name", "viewMode", "autocomplete", "for", "_hoisted_68", "_hoisted_69", "pagination", "currentPage", "itemsPerPage", "Math", "min", "totalItems", "onChange", "changeItemsPerPage", "_hoisted_70", "requests", "selectAllRequests", "_hoisted_71", "_hoisted_72", "_hoisted_73", "request", "includes", "_hoisted_74", "_hoisted_75", "_hoisted_76", "checked", "toggleRequestSelection", "_hoisted_78", "_hoisted_79", "request_number", "_hoisted_80", "_hoisted_81", "_hoisted_82", "href", "_withModifiers", "viewRequestDetails", "approveRequest", "rejectRequest", "_ctx", "prompt", "_hoisted_86", "_hoisted_87", "_hoisted_88", "_hoisted_89", "client_name", "_hoisted_90", "client_email", "_hoisted_91", "_hoisted_92", "_hoisted_93", "_hoisted_94", "_hoisted_95", "_hoisted_96", "_hoisted_97", "getStatusColor", "_hoisted_98", "_hoisted_99", "_hoisted_100", "formatCurrency", "total_fee", "_hoisted_101", "_hoisted_102", "formatDate", "requested_at", "_hoisted_103", "_hoisted_104", "canApprove", "quickApprove", "canReject", "showQuickRejectModal", "_hoisted_108", "_hoisted_109", "_hoisted_110", "_hoisted_111", "_hoisted_112", "_hoisted_113", "_hoisted_114", "_hoisted_116", "_hoisted_117", "_hoisted_118", "_hoisted_119", "_hoisted_120", "_hoisted_121", "_hoisted_122", "_hoisted_123", "_hoisted_124", "_hoisted_125", "_hoisted_126", "_hoisted_127", "_hoisted_128", "_hoisted_129", "_hoisted_130", "_hoisted_131", "_hoisted_132", "_hoisted_133", "_hoisted_134", "_hoisted_135", "_hoisted_136", "_hoisted_140", "_hoisted_141", "totalPages", "_hoisted_145", "_hoisted_146", "_hoisted_147", "changePage", "page", "_hoisted_148", "showRequestDetails", "currentRequest", "_hoisted_149", "_hoisted_150", "_hoisted_151", "_hoisted_152", "_hoisted_153", "_hoisted_154", "_hoisted_155", "_hoisted_156", "_hoisted_157", "_hoisted_158", "_hoisted_159", "_hoisted_160", "_hoisted_161", "_hoisted_162", "_hoisted_163", "_hoisted_164", "_hoisted_165", "_hoisted_166", "_hoisted_167", "purpose_category", "_hoisted_168", "_hoisted_169", "purpose_details", "_hoisted_170", "_hoisted_171", "_hoisted_172", "_hoisted_173", "_hoisted_174", "priority", "_hoisted_175", "_hoisted_176", "delivery_method", "_hoisted_177", "_hoisted_178", "formatDateTime", "_hoisted_179", "_hoisted_180", "_hoisted_181", "_hoisted_182", "_hoisted_183", "_hoisted_184", "_hoisted_185", "_hoisted_186", "_hoisted_187", "_hoisted_188", "_hoisted_189", "_hoisted_190", "client_phone", "_hoisted_191", "_hoisted_192", "_hoisted_193", "client_address", "specific_details", "_hoisted_194", "_hoisted_195", "_hoisted_196", "_hoisted_197", "_hoisted_198", "_hoisted_199", "_hoisted_200", "residency_period", "_hoisted_201", "_hoisted_202", "civil_status", "_hoisted_203", "_hoisted_204", "_hoisted_205", "occupation", "_hoisted_206", "_hoisted_207", "monthly_income", "_hoisted_208", "_hoisted_209", "_hoisted_210", "_hoisted_211", "birth_date", "_hoisted_212", "_hoisted_213", "birth_place", "_hoisted_214", "_hoisted_215", "_hoisted_216", "_hoisted_217", "citizenship", "_hoisted_218", "_hoisted_219", "_hoisted_220", "_hoisted_221", "_hoisted_222", "annual_income", "_hoisted_223", "_hoisted_224", "tax_amount", "_hoisted_225", "_hoisted_226", "interest_penalty", "_hoisted_227", "_hoisted_228", "_hoisted_229", "_hoisted_230", "statusUpdateForm", "status_id", "_hoisted_231", "_hoisted_232", "rows", "reason", "_hoisted_233", "updateRequestStatusFromModal", "approveRequestFromModal", "showRejectForm", "_hoisted_235", "_hoisted_236", "rejectForm", "required", "_hoisted_237", "rejectRequestFromModal", "trim", "_hoisted_239", "_hoisted_240", "_hoisted_241", "_hoisted_242", "payment_method", "_hoisted_243", "_hoisted_244", "payment_status", "_hoisted_245", "_hoisted_246", "_hoisted_247", "_hoisted_248", "base_fee", "_hoisted_249", "_hoisted_250", "_hoisted_251", "additional_fees", "_hoisted_252", "_hoisted_253", "_hoisted_254", "processing_fee", "_hoisted_255", "_hoisted_256", "_hoisted_257", "_hoisted_258", "_hoisted_259", "status_history", "_hoisted_260", "history", "index", "new_status_name", "_hoisted_261", "_hoisted_262", "_hoisted_263", "changed_at", "_hoisted_264", "_hoisted_265", "changed_by_name", "old_status_name", "_hoisted_266", "change_reason", "_hoisted_267", "_hoisted_268", "_hoisted_269", "refreshRequestDetails", "showQuickReject", "selectedRequestForReject", "_hoisted_270", "_hoisted_271", "_hoisted_272", "_hoisted_273", "closeQuickRejectModal", "_hoisted_274", "_hoisted_275", "_hoisted_276", "_hoisted_277", "quickRejectForm", "error", "_hoisted_278", "_hoisted_279", "confirmQuickReject", "_hoisted_282", "_hoisted_283"], "sources": ["D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminRequests.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-requests\">\n    <AdminHeader\n      :userName=\"adminData?.first_name || 'Admin'\"\n      :showUserDropdown=\"showUserDropdown\"\n      :sidebarCollapsed=\"sidebarCollapsed\"\n      :activeMenu=\"activeMenu\"\n      @sidebar-toggle=\"handleSidebarToggle\"\n      @user-dropdown-toggle=\"handleUserDropdownToggle\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"handleLogout\"\n    />\n\n    <!-- Mobile Overlay -->\n    <div\n      class=\"mobile-overlay\"\n      :class=\"{ active: !sidebarCollapsed && isMobile }\"\n      @click=\"closeMobileSidebar\"\n    ></div>\n\n    <div class=\"dashboard-container\">\n      <AdminSidebar\n        :collapsed=\"sidebarCollapsed\"\n        :activeMenu=\"activeMenu\"\n        @menu-change=\"handleMenuChange\"\n        @logout=\"handleLogout\"\n        @toggle-sidebar=\"handleSidebarToggle\"\n      />\n\n      <main class=\"main-content\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n        <!-- Loading State -->\n        <div v-if=\"loading\" class=\"d-flex justify-content-center align-items-center\" style=\"min-height: 400px;\">\n          <div class=\"spinner-border text-primary\" role=\"status\">\n            <span class=\"visually-hidden\">Loading...</span>\n          </div>\n        </div>\n\n        <!-- Main Content -->\n        <div v-else class=\"container-fluid py-4\">\n          <!-- Error Message -->\n          <div v-if=\"errorMessage\" class=\"alert alert-danger alert-dismissible fade show\" role=\"alert\">\n            <i class=\"fas fa-exclamation-triangle me-2\"></i>\n            {{ errorMessage }}\n            <button type=\"button\" class=\"btn-close\" @click=\"errorMessage = ''\" aria-label=\"Close\"></button>\n          </div>\n\n          <!-- Page Header -->\n          <div class=\"row mb-4\">\n            <div class=\"col-12\">\n              <div class=\"d-flex justify-content-between align-items-center flex-wrap\">\n                <div>\n                  <h1 class=\"h3 mb-0 text-gray-800\">Document Request Management</h1>\n                  <p class=\"text-muted mb-0\">\n                    Manage and process document requests\n                    <span v-if=\"lastRefresh\" class=\"ms-2 small\">\n                      <i class=\"fas fa-clock text-muted\"></i>\n                      Last updated: {{ formatTime(lastRefresh) }}\n                    </span>\n                  </p>\n                </div>\n                <div class=\"d-flex gap-2 align-items-center\">\n                  <!-- Real-time status indicator -->\n                  <div class=\"real-time-status me-2\">\n                    <span class=\"badge\" :class=\"autoRefreshEnabled ? 'bg-success' : 'bg-secondary'\">\n                      <i class=\"fas fa-circle pulse\" v-if=\"autoRefreshEnabled\"></i>\n                      <i class=\"fas fa-pause\" v-else></i>\n                      {{ autoRefreshEnabled ? 'Live' : 'Paused' }}\n                    </span>\n                  </div>\n\n                  <button class=\"btn btn-outline-secondary btn-sm\" @click=\"toggleAutoRefresh\" :title=\"autoRefreshEnabled ? 'Disable auto-refresh' : 'Enable auto-refresh'\">\n                    <i class=\"fas\" :class=\"autoRefreshEnabled ? 'fa-pause' : 'fa-play'\"></i>\n                  </button>\n                  <button class=\"btn btn-outline-primary btn-sm\" @click=\"showFilters = !showFilters\">\n                    <i class=\"fas fa-filter me-1\"></i>\n                    {{ showFilters ? 'Hide' : 'Show' }} Filters\n                  </button>\n                  <button class=\"btn btn-success btn-sm\" @click=\"exportRequests\" :disabled=\"loading\">\n                    <i class=\"fas fa-download me-1\"></i>\n                    Export CSV\n                  </button>\n                  <button class=\"btn btn-primary btn-sm\" @click=\"refreshRequestsData\" :disabled=\"loading\">\n                    <i class=\"fas fa-sync-alt me-1\" :class=\"{ 'fa-spin': loading }\"></i>\n                    Refresh\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Request Statistics -->\n          <div class=\"row mb-4\">\n            <div class=\"col-xl-3 col-md-6 mb-3\">\n              <div class=\"card border-left-primary shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row g-0 align-items-center\">\n                    <div class=\"col me-2\">\n                      <div class=\"text-xs fw-bold text-primary text-uppercase mb-1\">\n                        Total Requests\n                      </div>\n                      <div class=\"h5 mb-0 fw-bold text-dark\">{{ requestStats.total || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-file-alt fa-2x text-muted\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-xl-3 col-md-6 mb-3\">\n              <div class=\"card border-left-warning shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row g-0 align-items-center\">\n                    <div class=\"col me-2\">\n                      <div class=\"text-xs fw-bold text-warning text-uppercase mb-1\">\n                        Pending\n                      </div>\n                      <div class=\"h5 mb-0 fw-bold text-dark\">{{ requestStats.pending || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-clock fa-2x text-muted\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-xl-3 col-md-6 mb-3\">\n              <div class=\"card border-left-success shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row g-0 align-items-center\">\n                    <div class=\"col me-2\">\n                      <div class=\"text-xs fw-bold text-success text-uppercase mb-1\">\n                        Completed\n                      </div>\n                      <div class=\"h5 mb-0 fw-bold text-dark\">{{ requestStats.completed || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-check-circle fa-2x text-muted\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-xl-3 col-md-6 mb-3\">\n              <div class=\"card border-left-info shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row g-0 align-items-center\">\n                    <div class=\"col me-2\">\n                      <div class=\"text-xs fw-bold text-info text-uppercase mb-1\">\n                        Approved\n                      </div>\n                      <div class=\"h5 mb-0 fw-bold text-dark\">{{ requestStats.approved || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-thumbs-up fa-2x text-muted\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Filters Panel -->\n          <div v-if=\"showFilters\" class=\"card shadow mb-4\">\n            <div class=\"card-header py-3\">\n              <h6 class=\"m-0 fw-bold text-primary\">Filter Requests</h6>\n            </div>\n            <div class=\"card-body\">\n              <div class=\"row\">\n                <div class=\"col-md-3 mb-3\">\n                  <label class=\"form-label\">Search</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    v-model=\"filters.search\"\n                    placeholder=\"Search by name, email, or request number\"\n                    @keyup.enter=\"applyFilters\"\n                  >\n                </div>\n                <div class=\"col-md-2 mb-3\">\n                  <label class=\"form-label\">Status</label>\n                  <select class=\"form-select\" v-model=\"filters.status\">\n                    <option value=\"\">All Statuses</option>\n                    <option v-for=\"status in statusOptions\" :key=\"status.id\" :value=\"status.status_name\">\n                      {{ formatStatus(status.status_name) }}\n                    </option>\n                  </select>\n                </div>\n                <div class=\"col-md-2 mb-3\">\n                  <label class=\"form-label\">Document Type</label>\n                  <select class=\"form-select\" v-model=\"filters.document_type\">\n                    <option value=\"\">All Types</option>\n                    <option value=\"barangay_clearance\">Barangay Clearance</option>\n                    <option value=\"cedula\">Cedula</option>\n                  </select>\n                </div>\n                <div class=\"col-md-2 mb-3\">\n                  <label class=\"form-label\">Date From</label>\n                  <input type=\"date\" class=\"form-control\" v-model=\"filters.date_from\">\n                </div>\n                <div class=\"col-md-2 mb-3\">\n                  <label class=\"form-label\">Date To</label>\n                  <input type=\"date\" class=\"form-control\" v-model=\"filters.date_to\">\n                </div>\n                <div class=\"col-md-1 mb-3 d-flex align-items-end\">\n                  <div class=\"d-flex gap-1 w-100\">\n                    <button class=\"btn btn-primary btn-sm\" @click=\"applyFilters\">\n                      <i class=\"fas fa-search\"></i>\n                    </button>\n                    <button class=\"btn btn-outline-secondary btn-sm\" @click=\"clearFilters\">\n                      <i class=\"fas fa-times\"></i>\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Bulk Actions Panel -->\n          <div v-if=\"selectedRequests.length > 0\" class=\"card shadow mb-4\">\n            <div class=\"card-header py-3 bg-warning\">\n              <h6 class=\"m-0 fw-bold text-dark\">\n                <i class=\"fas fa-tasks me-2\"></i>\n                Bulk Actions ({{ selectedRequests.length }} selected)\n              </h6>\n            </div>\n            <div class=\"card-body\">\n              <div class=\"row align-items-end\">\n                <div class=\"col-md-3 mb-3\">\n                  <label class=\"form-label\">Action</label>\n                  <select class=\"form-select\" v-model=\"bulkAction\">\n                    <option value=\"\">Select Action</option>\n                    <option v-for=\"status in statusOptions\" :key=\"status.id\" :value=\"status.id\">\n                      Change to {{ formatStatus(status.status_name) }}\n                    </option>\n                  </select>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label class=\"form-label\">Reason (Optional)</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    v-model=\"bulkReason\"\n                    placeholder=\"Enter reason for bulk action\"\n                  >\n                </div>\n                <div class=\"col-md-3 mb-3\">\n                  <div class=\"d-flex gap-2\">\n                    <button class=\"btn btn-warning\" @click=\"performBulkAction\" :disabled=\"!bulkAction\">\n                      <i class=\"fas fa-play me-1\"></i>\n                      Apply\n                    </button>\n                    <button class=\"btn btn-outline-secondary\" @click=\"selectedRequests = []\">\n                      <i class=\"fas fa-times me-1\"></i>\n                      Cancel\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- View Toggle -->\n          <div class=\"d-flex justify-content-between align-items-center mb-4\">\n            <div class=\"d-flex align-items-center gap-3\">\n              <div class=\"btn-group\" role=\"group\" aria-label=\"View toggle\">\n                <input type=\"radio\" class=\"btn-check\" name=\"viewMode\" id=\"cardView\" v-model=\"viewMode\" value=\"card\" autocomplete=\"off\">\n                <label class=\"btn btn-outline-primary btn-sm\" for=\"cardView\">\n                  <i class=\"fas fa-th-large me-1\"></i>Cards\n                </label>\n\n                <input type=\"radio\" class=\"btn-check\" name=\"viewMode\" id=\"tableView\" v-model=\"viewMode\" value=\"table\" autocomplete=\"off\">\n                <label class=\"btn btn-outline-primary btn-sm\" for=\"tableView\">\n                  <i class=\"fas fa-table me-1\"></i>Table\n                </label>\n              </div>\n\n              <div class=\"d-flex align-items-center gap-2\">\n                <span class=\"text-muted small\">\n                  Showing {{ ((pagination.currentPage - 1) * pagination.itemsPerPage) + 1 }} -\n                  {{ Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems) }}\n                  of {{ pagination.totalItems }} requests\n                </span>\n                <select class=\"form-select form-select-sm\" style=\"width: auto;\" v-model=\"pagination.itemsPerPage\" @change=\"changeItemsPerPage(pagination.itemsPerPage)\">\n                  <option value=\"10\">10 per page</option>\n                  <option value=\"25\">25 per page</option>\n                  <option value=\"50\">50 per page</option>\n                  <option value=\"100\">100 per page</option>\n                </select>\n              </div>\n            </div>\n\n            <div class=\"d-flex align-items-center gap-2\">\n              <button class=\"btn btn-sm btn-outline-secondary\" @click=\"selectAllRequests\" v-if=\"requests.length > 0\">\n                <i class=\"fas fa-check-square me-1\"></i>\n                {{ selectedRequests.length === requests.length ? 'Deselect All' : 'Select All' }}\n              </button>\n            </div>\n          </div>\n\n          <!-- Card View -->\n          <div v-if=\"viewMode === 'card'\" class=\"requests-grid\">\n            <!-- Empty State -->\n            <div v-if=\"requests.length === 0\" class=\"empty-state text-center py-5\">\n              <div class=\"empty-state-icon mb-3\">\n                <i class=\"fas fa-inbox fa-4x text-muted\"></i>\n              </div>\n              <h5 class=\"text-muted mb-2\">No Document Requests Found</h5>\n              <p class=\"text-muted\">There are no document requests matching your current filters.</p>\n            </div>\n\n            <!-- Request Cards -->\n            <div v-else class=\"row g-4\">\n              <div v-for=\"request in requests\" :key=\"request.id\" class=\"col-xl-4 col-lg-6 col-md-6\">\n                <div class=\"request-card\" :class=\"{ 'selected': selectedRequests.includes(request.id) }\">\n                  <!-- Card Header -->\n                  <div class=\"request-card-header\">\n                    <div class=\"d-flex justify-content-between align-items-start\">\n                      <div class=\"d-flex align-items-center gap-2\">\n                        <input\n                          type=\"checkbox\"\n                          class=\"form-check-input\"\n                          :checked=\"selectedRequests.includes(request.id)\"\n                          @change=\"toggleRequestSelection(request.id)\"\n                        >\n                        <div class=\"request-number\">\n                          <span class=\"badge bg-primary\">{{ request.request_number }}</span>\n                        </div>\n                      </div>\n                      <div class=\"request-actions\">\n                        <div class=\"dropdown\">\n                          <button class=\"btn btn-sm btn-light dropdown-toggle\" type=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\n                            <i class=\"fas fa-ellipsis-v\"></i>\n                          </button>\n                          <ul class=\"dropdown-menu dropdown-menu-end\">\n                            <li>\n                              <a class=\"dropdown-item\" href=\"#\" @click.prevent=\"viewRequestDetails(request.id)\">\n                                <i class=\"fas fa-eye me-2\"></i>View Details\n                              </a>\n                            </li>\n                            <li><hr class=\"dropdown-divider\"></li>\n                            <li>\n                              <a class=\"dropdown-item text-success\" href=\"#\" @click.prevent=\"approveRequest(request.id)\">\n                                <i class=\"fas fa-check me-2\"></i>Approve\n                              </a>\n                            </li>\n                            <li>\n                              <a class=\"dropdown-item text-danger\" href=\"#\" @click.prevent=\"rejectRequest(request.id, prompt('Rejection reason:'))\">\n                                <i class=\"fas fa-times me-2\"></i>Reject\n                              </a>\n                            </li>\n                          </ul>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Card Body -->\n                  <div class=\"request-card-body\">\n                    <!-- Client Info -->\n                    <div class=\"client-info mb-3\">\n                      <div class=\"d-flex align-items-center gap-2 mb-2\">\n                        <div class=\"client-avatar\">\n                          <i class=\"fas fa-user-circle fa-2x text-primary\"></i>\n                        </div>\n                        <div>\n                          <h6 class=\"mb-0 fw-bold\">{{ request.client_name }}</h6>\n                          <small class=\"text-muted\">{{ request.client_email }}</small>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Document Type -->\n                    <div class=\"document-type mb-3\">\n                      <div class=\"d-flex align-items-center gap-2\">\n                        <i class=\"fas fa-file-alt text-info\"></i>\n                        <span class=\"badge bg-info-subtle text-info-emphasis px-3 py-2\">\n                          {{ request.document_type }}\n                        </span>\n                      </div>\n                    </div>\n\n                    <!-- Status and Amount -->\n                    <div class=\"request-meta mb-3\">\n                      <div class=\"row g-2\">\n                        <div class=\"col-6\">\n                          <div class=\"meta-item\">\n                            <small class=\"text-muted d-block\">Status</small>\n                            <span class=\"badge\" :class=\"`bg-${getStatusColor(request.status_name)}`\">\n                              {{ formatStatus(request.status_name) }}\n                            </span>\n                          </div>\n                        </div>\n                        <div class=\"col-6\">\n                          <div class=\"meta-item\">\n                            <small class=\"text-muted d-block\">Amount</small>\n                            <span class=\"fw-bold text-success\">{{ formatCurrency(request.total_fee) }}</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Date -->\n                    <div class=\"request-date\">\n                      <small class=\"text-muted\">\n                        <i class=\"fas fa-calendar-alt me-1\"></i>\n                        Submitted {{ formatDate(request.requested_at) }}\n                      </small>\n                    </div>\n                  </div>\n\n                  <!-- Card Footer -->\n                  <div class=\"request-card-footer\">\n                    <div class=\"d-flex gap-2\">\n                      <button class=\"btn btn-sm btn-outline-primary flex-fill\" @click=\"viewRequestDetails(request.id)\">\n                        <i class=\"fas fa-eye me-1\"></i>View\n                      </button>\n                      <button\n                        v-if=\"canApprove(request)\"\n                        class=\"btn btn-sm btn-success\"\n                        @click=\"quickApprove(request)\"\n                        :title=\"'Approve Request'\"\n                        :disabled=\"loading\"\n                      >\n                        <i class=\"fas fa-check\"></i>\n                      </button>\n                      <button\n                        v-if=\"canReject(request)\"\n                        class=\"btn btn-sm btn-danger\"\n                        @click=\"showQuickRejectModal(request)\"\n                        :title=\"'Reject Request'\"\n                        :disabled=\"loading\"\n                      >\n                        <i class=\"fas fa-times\"></i>\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Table View -->\n          <div v-else class=\"modern-table-container\">\n            <!-- Table Header -->\n            <div class=\"modern-table-header\">\n              <div class=\"d-flex justify-content-between align-items-center\">\n                <h5 class=\"mb-0 fw-bold text-dark\">\n                  <i class=\"fas fa-file-alt me-2 text-primary\"></i>\n                  Document Requests\n                </h5>\n                <div class=\"table-actions d-flex gap-2\">\n                  <button class=\"btn btn-sm btn-outline-secondary\" @click=\"selectAllRequests\" v-if=\"requests.length > 0\">\n                    <i class=\"fas fa-check-square me-1\"></i>\n                    {{ selectedRequests.length === requests.length ? 'Deselect All' : 'Select All' }}\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <!-- Empty State -->\n            <div v-if=\"requests.length === 0\" class=\"modern-table-empty\">\n              <div class=\"empty-content\">\n                <div class=\"empty-icon\">\n                  <i class=\"fas fa-inbox\"></i>\n                </div>\n                <h6 class=\"empty-title\">No Document Requests Found</h6>\n                <p class=\"empty-text\">There are no document requests matching your current filters.</p>\n              </div>\n            </div>\n\n            <!-- Modern Table -->\n            <div v-else class=\"modern-table\">\n              <!-- Table Row -->\n              <div v-for=\"request in requests\" :key=\"request.id\" class=\"table-row\" :class=\"{ 'selected': selectedRequests.includes(request.id) }\">\n                <!-- Selection Column -->\n                <div class=\"table-cell selection-cell\">\n                  <input\n                    type=\"checkbox\"\n                    class=\"form-check-input\"\n                    :checked=\"selectedRequests.includes(request.id)\"\n                    @change=\"toggleRequestSelection(request.id)\"\n                  >\n                </div>\n\n                <!-- Request Number Column -->\n                <div class=\"table-cell request-number-cell\">\n                  <div class=\"request-number-content\">\n                    <span class=\"request-number\">{{ request.request_number }}</span>\n                    <span class=\"request-id\">ID: {{ request.id }}</span>\n                  </div>\n                </div>\n\n                <!-- Client Column -->\n                <div class=\"table-cell client-cell\">\n                  <div class=\"client-info\">\n                    <div class=\"client-avatar-sm\">\n                      <i class=\"fas fa-user\"></i>\n                    </div>\n                    <div class=\"client-details\">\n                      <div class=\"client-name\">{{ request.client_name }}</div>\n                      <div class=\"client-email\">{{ request.client_email }}</div>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- Document Type Column -->\n                <div class=\"table-cell document-type-cell\">\n                  <div class=\"document-type-badge\">\n                    <i class=\"fas fa-file-alt me-2\"></i>\n                    <span>{{ request.document_type }}</span>\n                  </div>\n                </div>\n\n                <!-- Status Column -->\n                <div class=\"table-cell status-cell\">\n                  <span class=\"status-badge\" :class=\"`status-${getStatusColor(request.status_name)}`\">\n                    <i class=\"fas fa-circle status-indicator\"></i>\n                    {{ formatStatus(request.status_name) }}\n                  </span>\n                </div>\n\n                <!-- Amount Column -->\n                <div class=\"table-cell amount-cell\">\n                  <div class=\"amount-content\">\n                    <span class=\"amount\">{{ formatCurrency(request.total_fee) }}</span>\n                    <span class=\"currency\">PHP</span>\n                  </div>\n                </div>\n\n                <!-- Date Column -->\n                <div class=\"table-cell date-cell\">\n                  <div class=\"date-content\">\n                    <span class=\"date\">{{ formatDate(request.requested_at) }}</span>\n                    <span class=\"time\">{{ formatTime(request.requested_at) }}</span>\n                  </div>\n                </div>\n\n                <!-- Actions Column -->\n                <div class=\"table-cell actions-cell\">\n                  <div class=\"action-buttons\">\n                    <button class=\"action-btn view-btn\" @click=\"viewRequestDetails(request.id)\" title=\"View Details\">\n                      <i class=\"fas fa-eye\"></i>\n                    </button>\n                    <button\n                      v-if=\"canApprove(request)\"\n                      class=\"action-btn approve-btn\"\n                      @click=\"quickApprove(request)\"\n                      title=\"Approve\"\n                      :disabled=\"loading\"\n                    >\n                      <i class=\"fas fa-check\"></i>\n                    </button>\n                    <button\n                      v-if=\"canReject(request)\"\n                      class=\"action-btn reject-btn\"\n                      @click=\"showQuickRejectModal(request)\"\n                      title=\"Reject\"\n                      :disabled=\"loading\"\n                    >\n                      <i class=\"fas fa-times\"></i>\n                    </button>\n                    <div class=\"dropdown\">\n                      <button class=\"action-btn more-btn dropdown-toggle\" type=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\" title=\"More Actions\">\n                        <i class=\"fas fa-ellipsis-v\"></i>\n                      </button>\n                      <ul class=\"dropdown-menu dropdown-menu-end\">\n                        <li>\n                          <a class=\"dropdown-item\" href=\"#\" @click.prevent=\"viewRequestDetails(request.id)\">\n                            <i class=\"fas fa-eye me-2\"></i>View Details\n                          </a>\n                        </li>\n                        <li><hr class=\"dropdown-divider\"></li>\n                        <li>\n                          <a class=\"dropdown-item text-success\" href=\"#\" @click.prevent=\"approveRequest(request.id)\">\n                            <i class=\"fas fa-check me-2\"></i>Approve\n                          </a>\n                        </li>\n                        <li>\n                          <a class=\"dropdown-item text-danger\" href=\"#\" @click.prevent=\"rejectRequest(request.id, prompt('Rejection reason:'))\">\n                            <i class=\"fas fa-times me-2\"></i>Reject\n                          </a>\n                        </li>\n                      </ul>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Pagination -->\n          <div v-if=\"pagination.totalPages > 1\" class=\"pagination-container\">\n              <nav aria-label=\"Requests pagination\">\n                <ul class=\"pagination pagination-sm justify-content-center mb-0\">\n                  <li class=\"page-item\" :class=\"{ disabled: pagination.currentPage === 1 }\">\n                    <a class=\"page-link\" href=\"#\" @click.prevent=\"changePage(pagination.currentPage - 1)\">\n                      <i class=\"fas fa-chevron-left\"></i>\n                    </a>\n                  </li>\n                  <li\n                    v-for=\"page in Math.min(pagination.totalPages, 10)\"\n                    :key=\"page\"\n                    class=\"page-item\"\n                    :class=\"{ active: page === pagination.currentPage }\"\n                  >\n                    <a class=\"page-link\" href=\"#\" @click.prevent=\"changePage(page)\">{{ page }}</a>\n                  </li>\n                  <li class=\"page-item\" :class=\"{ disabled: pagination.currentPage === pagination.totalPages }\">\n                    <a class=\"page-link\" href=\"#\" @click.prevent=\"changePage(pagination.currentPage + 1)\">\n                      <i class=\"fas fa-chevron-right\"></i>\n                    </a>\n                  </li>\n                </ul>\n              </nav>\n            </div>\n          </div>\n\n          <!-- Request Details Modal -->\n          <div v-if=\"showRequestDetails && currentRequest\" class=\"modal fade show d-block\" tabindex=\"-1\" style=\"background-color: rgba(0,0,0,0.5);\">\n            <div class=\"modal-dialog modal-xl modal-dialog-scrollable\">\n              <div class=\"modal-content\">\n                <div class=\"modal-header\">\n                  <h5 class=\"modal-title\">\n                    <i class=\"fas fa-file-alt me-2\"></i>\n                    Request Details - {{ currentRequest.request_number }}\n                  </h5>\n                  <button type=\"button\" class=\"btn-close\" @click=\"showRequestDetails = false\"></button>\n                </div>\n                <div class=\"modal-body\">\n                  <div class=\"row\">\n                    <!-- Left Column - Request Information -->\n                    <div class=\"col-lg-8\">\n                      <!-- Basic Information -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-info-circle me-2\"></i>Request Information</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <div class=\"row\">\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Request Number</label>\n                                <p class=\"mb-0\">{{ currentRequest.request_number }}</p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Document Type</label>\n                                <p class=\"mb-0\">\n                                  <span class=\"badge bg-info\">{{ currentRequest.document_type }}</span>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Purpose Category</label>\n                                <p class=\"mb-0\">{{ currentRequest.purpose_category }}</p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Purpose Details</label>\n                                <p class=\"mb-0\">{{ currentRequest.purpose_details || 'Not specified' }}</p>\n                              </div>\n                            </div>\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Current Status</label>\n                                <p class=\"mb-0\">\n                                  <span class=\"badge\" :class=\"`bg-${getStatusColor(currentRequest.status_name)}`\">\n                                    {{ formatStatus(currentRequest.status_name) }}\n                                  </span>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Priority</label>\n                                <p class=\"mb-0\">\n                                  <span class=\"badge\" :class=\"currentRequest.priority === 'high' ? 'bg-danger' : currentRequest.priority === 'medium' ? 'bg-warning' : 'bg-secondary'\">\n                                    {{ currentRequest.priority || 'Normal' }}\n                                  </span>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Delivery Method</label>\n                                <p class=\"mb-0\">{{ currentRequest.delivery_method || 'Pickup' }}</p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Date Submitted</label>\n                                <p class=\"mb-0\">{{ formatDateTime(currentRequest.requested_at) }}</p>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Client Information -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-user me-2\"></i>Client Information</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <div class=\"row\">\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Full Name</label>\n                                <p class=\"mb-0\">{{ currentRequest.client_name }}</p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Email Address</label>\n                                <p class=\"mb-0\">\n                                  <a :href=\"`mailto:${currentRequest.client_email}`\">{{ currentRequest.client_email }}</a>\n                                </p>\n                              </div>\n                            </div>\n                            <div class=\"col-md-6\">\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Phone Number</label>\n                                <p class=\"mb-0\">\n                                  <a :href=\"`tel:${currentRequest.client_phone}`\">{{ currentRequest.client_phone || 'Not provided' }}</a>\n                                </p>\n                              </div>\n                              <div class=\"mb-3\">\n                                <label class=\"form-label fw-bold\">Address</label>\n                                <p class=\"mb-0\">{{ currentRequest.client_address || 'Not provided' }}</p>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Document-Specific Details -->\n                      <div v-if=\"currentRequest.specific_details\" class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-clipboard-list me-2\"></i>Document-Specific Information</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <!-- Barangay Clearance Details -->\n                          <div v-if=\"currentRequest.document_type === 'Barangay Clearance'\">\n                            <div class=\"row\">\n                              <div class=\"col-md-6\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Residency Period</label>\n                                  <p class=\"mb-0\">{{ currentRequest.specific_details.residency_period || 'Not specified' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Civil Status</label>\n                                  <p class=\"mb-0\">{{ currentRequest.specific_details.civil_status || 'Not specified' }}</p>\n                                </div>\n                              </div>\n                              <div class=\"col-md-6\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Occupation</label>\n                                  <p class=\"mb-0\">{{ currentRequest.specific_details.occupation || 'Not specified' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Monthly Income</label>\n                                  <p class=\"mb-0\">{{ formatCurrency(currentRequest.specific_details.monthly_income) || 'Not specified' }}</p>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n\n                          <!-- Cedula Details -->\n                          <div v-else-if=\"currentRequest.document_type === 'Cedula'\">\n                            <div class=\"row\">\n                              <div class=\"col-md-6\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Birth Date</label>\n                                  <p class=\"mb-0\">{{ formatDate(currentRequest.specific_details.birth_date) || 'Not specified' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Birth Place</label>\n                                  <p class=\"mb-0\">{{ currentRequest.specific_details.birth_place || 'Not specified' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Civil Status</label>\n                                  <p class=\"mb-0\">{{ currentRequest.specific_details.civil_status || 'Not specified' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Citizenship</label>\n                                  <p class=\"mb-0\">{{ currentRequest.specific_details.citizenship || 'Not specified' }}</p>\n                                </div>\n                              </div>\n                              <div class=\"col-md-6\">\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Occupation</label>\n                                  <p class=\"mb-0\">{{ currentRequest.specific_details.occupation || 'Not specified' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Annual Income</label>\n                                  <p class=\"mb-0\">{{ formatCurrency(currentRequest.specific_details.annual_income) || 'Not specified' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Tax Amount</label>\n                                  <p class=\"mb-0\">{{ formatCurrency(currentRequest.specific_details.tax_amount) || 'Not calculated' }}</p>\n                                </div>\n                                <div class=\"mb-3\">\n                                  <label class=\"form-label fw-bold\">Interest/Penalty</label>\n                                  <p class=\"mb-0\">{{ formatCurrency(currentRequest.specific_details.interest_penalty) || 'None' }}</p>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Right Column - Status Management -->\n                    <div class=\"col-lg-4\">\n                      <!-- Status Management -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-tasks me-2\"></i>Status Management</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <div class=\"mb-3\">\n                            <label class=\"form-label fw-bold\">Change Status</label>\n                            <select class=\"form-select\" v-model=\"statusUpdateForm.status_id\">\n                              <option value=\"\">Select new status</option>\n                              <option v-for=\"status in statusOptions\" :key=\"status.id\" :value=\"status.id\">\n                                {{ formatStatus(status.status_name) }}\n                              </option>\n                            </select>\n                          </div>\n                          <div class=\"mb-3\">\n                            <label class=\"form-label fw-bold\">Reason/Notes</label>\n                            <textarea\n                              class=\"form-control\"\n                              rows=\"3\"\n                              v-model=\"statusUpdateForm.reason\"\n                              placeholder=\"Enter reason for status change (optional)\"\n                            ></textarea>\n                          </div>\n                          <div class=\"d-grid gap-2\">\n                            <button\n                              class=\"btn btn-primary\"\n                              @click=\"updateRequestStatusFromModal\"\n                              :disabled=\"!statusUpdateForm.status_id\"\n                            >\n                              <i class=\"fas fa-save me-1\"></i>\n                              Update Status\n                            </button>\n                            <button class=\"btn btn-success\" @click=\"approveRequestFromModal\">\n                              <i class=\"fas fa-check me-1\"></i>\n                              Quick Approve\n                            </button>\n                            <button class=\"btn btn-danger\" @click=\"showRejectForm = !showRejectForm\">\n                              <i class=\"fas fa-times me-1\"></i>\n                              {{ showRejectForm ? 'Cancel' : 'Reject Request' }}\n                            </button>\n                          </div>\n\n                          <!-- Rejection Form -->\n                          <div v-if=\"showRejectForm\" class=\"mt-3 p-3 border rounded bg-light\">\n                            <div class=\"mb-3\">\n                              <label class=\"form-label fw-bold text-danger\">Rejection Reason *</label>\n                              <textarea\n                                class=\"form-control\"\n                                rows=\"3\"\n                                v-model=\"rejectForm.reason\"\n                                placeholder=\"Please provide a detailed reason for rejection\"\n                                required\n                              ></textarea>\n                            </div>\n                            <div class=\"d-grid\">\n                              <button\n                                class=\"btn btn-danger\"\n                                @click=\"rejectRequestFromModal\"\n                                :disabled=\"!rejectForm.reason || rejectForm.reason.trim() === ''\"\n                              >\n                                <i class=\"fas fa-times me-1\"></i>\n                                Confirm Rejection\n                              </button>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <!-- Payment Information -->\n                      <div class=\"card mb-4\">\n                        <div class=\"card-header\">\n                          <h6 class=\"mb-0\"><i class=\"fas fa-credit-card me-2\"></i>Payment Information</h6>\n                        </div>\n                        <div class=\"card-body\">\n                          <div class=\"mb-3\">\n                            <label class=\"form-label fw-bold\">Payment Method</label>\n                            <p class=\"mb-0\">{{ currentRequest.payment_method || 'Not specified' }}</p>\n                          </div>\n                          <div class=\"mb-3\">\n                            <label class=\"form-label fw-bold\">Payment Status</label>\n                            <p class=\"mb-0\">\n                              <span class=\"badge\" :class=\"currentRequest.payment_status === 'paid' ? 'bg-success' : currentRequest.payment_status === 'pending' ? 'bg-warning' : 'bg-secondary'\">\n                                {{ currentRequest.payment_status || 'Unpaid' }}\n                              </span>\n                            </p>\n                          </div>\n                          <div class=\"row\">\n                            <div class=\"col-6\">\n                              <div class=\"mb-2\">\n                                <label class=\"form-label fw-bold small\">Base Fee</label>\n                                <p class=\"mb-0\">{{ formatCurrency(currentRequest.base_fee) }}</p>\n                              </div>\n                            </div>\n                            <div class=\"col-6\">\n                              <div class=\"mb-2\">\n                                <label class=\"form-label fw-bold small\">Additional Fees</label>\n                                <p class=\"mb-0\">{{ formatCurrency(currentRequest.additional_fees) }}</p>\n                              </div>\n                            </div>\n                            <div class=\"col-6\">\n                              <div class=\"mb-2\">\n                                <label class=\"form-label fw-bold small\">Processing Fee</label>\n                                <p class=\"mb-0\">{{ formatCurrency(currentRequest.processing_fee) }}</p>\n                              </div>\n                            </div>\n                            <div class=\"col-6\">\n                              <div class=\"mb-2\">\n                                <label class=\"form-label fw-bold small\">Total Amount</label>\n                                <p class=\"mb-0 fw-bold text-primary\">{{ formatCurrency(currentRequest.total_fee) }}</p>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Status History Timeline -->\n                  <div class=\"card\">\n                    <div class=\"card-header\">\n                      <h6 class=\"mb-0\"><i class=\"fas fa-history me-2\"></i>Status History</h6>\n                    </div>\n                    <div class=\"card-body\">\n                      <div v-if=\"currentRequest.status_history && currentRequest.status_history.length > 0\" class=\"timeline\">\n                        <div\n                          v-for=\"(history, index) in currentRequest.status_history\"\n                          :key=\"history.id\"\n                          class=\"timeline-item\"\n                          :class=\"{ 'timeline-item-last': index === currentRequest.status_history.length - 1 }\"\n                        >\n                          <div class=\"timeline-marker\" :class=\"`bg-${getStatusColor(history.new_status_name)}`\">\n                            <i class=\"fas fa-circle\"></i>\n                          </div>\n                          <div class=\"timeline-content\">\n                            <div class=\"timeline-header\">\n                              <span class=\"badge\" :class=\"`bg-${getStatusColor(history.new_status_name)}`\">\n                                {{ formatStatus(history.new_status_name) }}\n                              </span>\n                              <small class=\"text-muted ms-2\">{{ formatDateTime(history.changed_at) }}</small>\n                            </div>\n                            <div class=\"timeline-body\">\n                              <p class=\"mb-1\">\n                                <strong>Changed by:</strong> {{ history.changed_by_name }}\n                              </p>\n                              <p v-if=\"history.old_status_name\" class=\"mb-1\">\n                                <strong>From:</strong> {{ formatStatus(history.old_status_name) }}\n                              </p>\n                              <p v-if=\"history.change_reason\" class=\"mb-0\">\n                                <strong>Reason:</strong> {{ history.change_reason }}\n                              </p>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                      <div v-else class=\"text-center text-muted py-3\">\n                        <i class=\"fas fa-history fa-2x mb-2\"></i>\n                        <p>No status history available</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <div class=\"modal-footer\">\n                  <button type=\"button\" class=\"btn btn-secondary\" @click=\"showRequestDetails = false\">\n                    <i class=\"fas fa-times me-1\"></i>\n                    Close\n                  </button>\n                  <button type=\"button\" class=\"btn btn-primary\" @click=\"refreshRequestDetails\">\n                    <i class=\"fas fa-sync-alt me-1\"></i>\n                    Refresh\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Quick Reject Modal -->\n          <div v-if=\"showQuickReject && selectedRequestForReject\" class=\"modal fade show d-block\" tabindex=\"-1\" style=\"background-color: rgba(0,0,0,0.5);\">\n            <div class=\"modal-dialog\">\n              <div class=\"modal-content\">\n                <div class=\"modal-header\">\n                  <h5 class=\"modal-title\">\n                    <i class=\"fas fa-times-circle text-danger me-2\"></i>\n                    Reject Request\n                  </h5>\n                  <button type=\"button\" class=\"btn-close\" @click=\"closeQuickRejectModal\"></button>\n                </div>\n                <div class=\"modal-body\">\n                  <div class=\"alert alert-warning\">\n                    <i class=\"fas fa-exclamation-triangle me-2\"></i>\n                    You are about to reject this document request. This action will notify the client immediately.\n                  </div>\n\n                  <div class=\"mb-3\">\n                    <strong>Request Details:</strong>\n                    <ul class=\"list-unstyled mt-2\">\n                      <li><strong>Request Number:</strong> {{ selectedRequestForReject.request_number }}</li>\n                      <li><strong>Document Type:</strong> {{ selectedRequestForReject.document_type }}</li>\n                      <li><strong>Client:</strong> {{ selectedRequestForReject.client_name }}</li>\n                    </ul>\n                  </div>\n\n                  <div class=\"mb-3\">\n                    <label for=\"rejectionReason\" class=\"form-label\">\n                      <strong>Rejection Reason <span class=\"text-danger\">*</span></strong>\n                    </label>\n                    <textarea\n                      id=\"rejectionReason\"\n                      v-model=\"quickRejectForm.reason\"\n                      class=\"form-control\"\n                      rows=\"4\"\n                      placeholder=\"Please provide a clear reason for rejecting this request...\"\n                      :class=\"{ 'is-invalid': quickRejectForm.error }\"\n                    ></textarea>\n                    <div v-if=\"quickRejectForm.error\" class=\"invalid-feedback\">\n                      {{ quickRejectForm.error }}\n                    </div>\n                    <div class=\"form-text\">\n                      This reason will be visible to the client and included in their notification.\n                    </div>\n                  </div>\n                </div>\n                <div class=\"modal-footer\">\n                  <button type=\"button\" class=\"btn btn-secondary\" @click=\"closeQuickRejectModal\" :disabled=\"quickRejectForm.loading\">\n                    <i class=\"fas fa-times me-1\"></i>\n                    Cancel\n                  </button>\n                  <button type=\"button\" class=\"btn btn-danger\" @click=\"confirmQuickReject\" :disabled=\"quickRejectForm.loading || !quickRejectForm.reason.trim()\">\n                    <i class=\"fas fa-times-circle me-1\"></i>\n                    <span v-if=\"quickRejectForm.loading\">\n                      <i class=\"fas fa-spinner fa-spin me-1\"></i>\n                      Rejecting...\n                    </span>\n                    <span v-else>Reject Request</span>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  </template>\n\n<script>\nimport AdminHeader from './AdminHeader.vue';\nimport AdminSidebar from './AdminSidebar.vue';\nimport adminAuthService from '@/services/adminAuthService';\nimport adminDocumentService from '@/services/adminDocumentService';\nimport notificationService from '@/services/notificationService';\n\nexport default {\n  name: 'AdminRequests',\n  components: {\n    AdminHeader,\n    AdminSidebar\n  },\n\n  data() {\n    return {\n      // UI State\n      loading: true,\n      sidebarCollapsed: false,\n      showUserDropdown: false,\n      isMobile: false,\n      adminData: null,\n      errorMessage: '',\n      viewMode: 'table', // 'card' or 'table' - default to table view\n\n      // Request Management Data\n      requests: [],\n      selectedRequests: [],\n      currentRequest: null,\n      statusOptions: [],\n\n      // Pagination\n      pagination: {\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n      },\n\n      // Filters\n      filters: {\n        status: '',\n        document_type: '',\n        priority: '',\n        search: '',\n        date_from: '',\n        date_to: ''\n      },\n\n      // Statistics\n      requestStats: {\n        total: 0,\n        pending: 0,\n        approved: 0,\n        completed: 0,\n        thisMonth: 0\n      },\n\n      // UI State\n      showFilters: false,\n      showBulkActions: false,\n      showRequestDetails: false,\n      showRejectForm: false,\n      showQuickReject: false,\n      bulkAction: '',\n      bulkReason: '',\n\n      // Status Update Forms\n      statusUpdateForm: {\n        status_id: '',\n        reason: ''\n      },\n      rejectForm: {\n        reason: ''\n      },\n      quickRejectForm: {\n        reason: '',\n        loading: false,\n        error: ''\n      },\n      selectedRequestForReject: null,\n\n      // Real-time features\n      refreshInterval: null,\n      autoRefreshEnabled: true,\n      refreshRate: 30000, // 30 seconds\n      lastRefresh: null\n    };\n  },\n\n  async mounted() {\n    // Check authentication\n    if (!adminAuthService.isLoggedIn()) {\n      this.$router.push('/admin/login');\n      return;\n    }\n\n    // Initialize UI state\n    this.initializeUI();\n\n    // Load component data\n    await this.loadComponentData();\n\n    // Initialize real-time features\n    this.initializeRealTimeFeatures();\n  },\n\n  beforeUnmount() {\n    if (this.handleResize) {\n      window.removeEventListener('resize', this.handleResize);\n    }\n\n    // Clean up real-time features\n    this.cleanupRealTimeFeatures();\n  },\n\n  computed: {\n    activeMenu() {\n      const path = this.$route.path;\n      if (path.includes('/admin/users')) return 'users';\n      if (path.includes('/admin/requests')) return 'requests';\n      if (path.includes('/admin/reports')) return 'reports';\n      if (path.includes('/admin/settings')) return 'settings';\n      if (path.includes('/admin/activity-logs')) return 'activity';\n      if (path.includes('/admin/profile')) return 'profile';\n      return 'dashboard';\n    }\n  },\n\n  methods: {\n    // Initialize UI state\n    initializeUI() {\n      this.isMobile = window.innerWidth <= 768;\n\n      // Load saved sidebar state (only on desktop)\n      if (!this.isMobile) {\n        const saved = localStorage.getItem('adminSidebarCollapsed');\n        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n      } else {\n        this.sidebarCollapsed = true; // Always collapsed on mobile\n      }\n\n      // Setup resize listener\n      this.handleResize = () => {\n        const wasMobile = this.isMobile;\n        this.isMobile = window.innerWidth <= 768;\n\n        if (this.isMobile && !wasMobile) {\n          this.sidebarCollapsed = true; // Collapse when switching to mobile\n        } else if (!this.isMobile && wasMobile) {\n          // Restore saved state when switching to desktop\n          const saved = localStorage.getItem('adminSidebarCollapsed');\n          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n        }\n      };\n      window.addEventListener('resize', this.handleResize);\n    },\n\n    // Sidebar toggle\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));\n    },\n\n    // Menu navigation\n    handleMenuChange(menu) {\n      const routes = {\n        'dashboard': '/admin/dashboard',\n        'users': '/admin/users',\n        'requests': '/admin/requests',\n        'reports': '/admin/reports',\n        'settings': '/admin/settings',\n        'activity': '/admin/activity-logs',\n        'profile': '/admin/profile'\n      };\n\n      // Close sidebar on mobile after navigation\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n\n      if (routes[menu]) {\n        this.$router.push(routes[menu]);\n      }\n    },\n\n    // User dropdown toggle\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n\n    // Menu actions\n    handleMenuAction(action) {\n      if (action === 'profile') {\n        this.$router.push('/admin/profile');\n      } else if (action === 'settings') {\n        this.$router.push('/admin/settings');\n      }\n      this.showUserDropdown = false;\n    },\n\n    // Close mobile sidebar\n    closeMobileSidebar() {\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n    },\n\n    // Logout\n    handleLogout() {\n      adminAuthService.logout();\n      this.$router.push('/admin/login');\n    },\n\n    // Load admin profile\n    async loadAdminProfile() {\n      try {\n        const response = await adminAuthService.getProfile();\n        if (response.success) {\n          this.adminData = response.data;\n        }\n      } catch (error) {\n        console.error('Failed to load admin profile:', error);\n        this.adminData = adminAuthService.getAdminData();\n      }\n    },\n\n    // Load all component data\n    async loadComponentData() {\n      this.loading = true;\n      try {\n        await Promise.all([\n          this.loadAdminProfile(),\n          this.loadStatusOptions(),\n          this.loadRequests(),\n          this.loadDashboardStats()\n        ]);\n      } catch (error) {\n        console.error('Failed to load component data:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load request data';\n\n        if (errorData.status === 401) {\n          adminAuthService.logout();\n          this.$router.push('/admin/login');\n        }\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // Load status options\n    async loadStatusOptions() {\n      try {\n        const response = await adminDocumentService.getStatusOptions();\n        if (response.success) {\n          this.statusOptions = response.data || [];\n        }\n      } catch (error) {\n        console.error('Failed to load status options:', error);\n        this.statusOptions = [];\n      }\n    },\n\n    // Load dashboard statistics\n    async loadDashboardStats() {\n      try {\n        const response = await adminDocumentService.getDashboardStats();\n        if (response.success) {\n          this.requestStats = {\n            total: response.data.totalRequests || 0,\n            pending: response.data.pendingRequests || 0,\n            approved: response.data.approvedRequests || 0,\n            completed: response.data.completedRequests || 0,\n            thisMonth: response.data.todayRequests || 0\n          };\n        }\n      } catch (error) {\n        console.error('Failed to load dashboard stats:', error);\n      }\n    },\n\n    // Load requests with current filters and pagination\n    async loadRequests() {\n      try {\n        const params = {\n          page: this.pagination.currentPage,\n          limit: this.pagination.itemsPerPage,\n          ...this.filters\n        };\n\n        const response = await adminDocumentService.getAllRequests(params);\n        if (response.success) {\n          this.requests = response.data.requests || [];\n          this.pagination = {\n            currentPage: response.data.pagination?.current_page || 1,\n            totalPages: response.data.pagination?.total_pages || 1,\n            totalItems: response.data.pagination?.total_records || 0,\n            itemsPerPage: response.data.pagination?.per_page || 10\n          };\n        }\n      } catch (error) {\n        console.error('Failed to load requests:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load requests';\n        this.requests = [];\n      }\n    },\n\n    // Filter and search methods\n    applyFilters() {\n      this.pagination.currentPage = 1;\n      this.loadRequests();\n    },\n\n    clearFilters() {\n      this.filters = {\n        status: '',\n        document_type: '',\n        priority: '',\n        search: '',\n        date_from: '',\n        date_to: ''\n      };\n      this.applyFilters();\n    },\n\n    // Pagination methods\n    changePage(page) {\n      if (page >= 1 && page <= this.pagination.totalPages) {\n        this.pagination.currentPage = page;\n        this.loadRequests();\n      }\n    },\n\n    changeItemsPerPage(itemsPerPage) {\n      this.pagination.itemsPerPage = itemsPerPage;\n      this.pagination.currentPage = 1;\n      this.loadRequests();\n    },\n\n    goBack() {\n      this.$router.push('/admin/dashboard');\n    },\n\n    // Request selection methods\n    toggleRequestSelection(requestId) {\n      const index = this.selectedRequests.indexOf(requestId);\n      if (index > -1) {\n        this.selectedRequests.splice(index, 1);\n      } else {\n        this.selectedRequests.push(requestId);\n      }\n    },\n\n    selectAllRequests() {\n      if (this.selectedRequests.length === this.requests.length) {\n        this.selectedRequests = [];\n      } else {\n        this.selectedRequests = this.requests.map(r => r.id);\n      }\n    },\n\n    // Request details\n    async viewRequestDetails(requestId) {\n      try {\n        const response = await adminDocumentService.getRequestDetails(requestId);\n        if (response.success) {\n          this.currentRequest = response.data;\n          this.showRequestDetails = true;\n          // Reset forms\n          this.statusUpdateForm = { status_id: '', reason: '' };\n          this.rejectForm = { reason: '' };\n          this.showRejectForm = false;\n        }\n      } catch (error) {\n        console.error('Failed to load request details:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to load request details';\n      }\n    },\n\n    // Refresh request details\n    async refreshRequestDetails() {\n      if (this.currentRequest) {\n        await this.viewRequestDetails(this.currentRequest.id);\n      }\n    },\n\n    // Update request status from modal\n    async updateRequestStatusFromModal() {\n      if (!this.statusUpdateForm.status_id || !this.currentRequest) return;\n\n      try {\n        const response = await adminDocumentService.updateRequestStatus(\n          this.currentRequest.id,\n          {\n            status_id: parseInt(this.statusUpdateForm.status_id),\n            reason: this.statusUpdateForm.reason\n          }\n        );\n\n        if (response.success) {\n          // Refresh the request details\n          await this.refreshRequestDetails();\n          // Refresh the main requests list\n          await this.loadRequests();\n          // Reset form\n          this.statusUpdateForm = { status_id: '', reason: '' };\n\n          // Show success message\n          this.errorMessage = '';\n          // You could add a success message here if needed\n        }\n      } catch (error) {\n        console.error('Failed to update request status:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to update request status';\n      }\n    },\n\n    // Approve request from modal\n    async approveRequestFromModal() {\n      if (!this.currentRequest) return;\n\n      try {\n        const response = await adminDocumentService.approveRequest(\n          this.currentRequest.id,\n          { reason: 'Approved from request details' }\n        );\n\n        if (response.success) {\n          await this.refreshRequestDetails();\n          await this.loadRequests();\n        }\n      } catch (error) {\n        console.error('Failed to approve request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to approve request';\n      }\n    },\n\n    // Reject request from modal\n    async rejectRequestFromModal() {\n      if (!this.currentRequest || !this.rejectForm.reason.trim()) return;\n\n      try {\n        const response = await adminDocumentService.rejectRequest(\n          this.currentRequest.id,\n          { reason: this.rejectForm.reason }\n        );\n\n        if (response.success) {\n          await this.refreshRequestDetails();\n          await this.loadRequests();\n          this.rejectForm = { reason: '' };\n          this.showRejectForm = false;\n        }\n      } catch (error) {\n        console.error('Failed to reject request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to reject request';\n      }\n    },\n\n    // Status update methods\n    async updateRequestStatus(requestId, statusId, reason = '') {\n      try {\n        const response = await adminDocumentService.updateRequestStatus(requestId, {\n          status_id: statusId,\n          reason: reason\n        });\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to update request status:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to update request status';\n      }\n    },\n\n    async approveRequest(requestId, reason = '') {\n      try {\n        const response = await adminDocumentService.approveRequest(requestId, { reason });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to approve request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to approve request';\n      }\n    },\n\n    async rejectRequest(requestId, reason) {\n      if (!reason || reason.trim() === '') {\n        this.errorMessage = 'Rejection reason is required';\n        return;\n      }\n\n      try {\n        const response = await adminDocumentService.rejectRequest(requestId, { reason });\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to reject request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to reject request';\n      }\n    },\n\n    // Quick approval/rejection methods\n    canApprove(request) {\n      return ['pending', 'under_review', 'additional_info_required'].includes(request.status_name);\n    },\n\n    canReject(request) {\n      return ['pending', 'under_review', 'additional_info_required', 'approved'].includes(request.status_name);\n    },\n\n    async quickApprove(request) {\n      try {\n        const response = await adminDocumentService.approveRequest(request.id, {\n          reason: 'Quick approval from admin interface'\n        });\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.showToast('Success', `Request ${request.request_number} approved successfully`, 'success');\n        }\n      } catch (error) {\n        console.error('Failed to approve request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.showToast('Error', errorData.message || 'Failed to approve request', 'error');\n      }\n    },\n\n    showQuickRejectModal(request) {\n      this.selectedRequestForReject = request;\n      this.quickRejectForm = {\n        reason: '',\n        loading: false,\n        error: ''\n      };\n      this.showQuickReject = true;\n    },\n\n    closeQuickRejectModal() {\n      this.showQuickReject = false;\n      this.selectedRequestForReject = null;\n      this.quickRejectForm = {\n        reason: '',\n        loading: false,\n        error: ''\n      };\n    },\n\n    async confirmQuickReject() {\n      if (!this.quickRejectForm.reason.trim()) {\n        this.quickRejectForm.error = 'Rejection reason is required';\n        return;\n      }\n\n      this.quickRejectForm.loading = true;\n      this.quickRejectForm.error = '';\n\n      try {\n        const response = await adminDocumentService.rejectRequest(\n          this.selectedRequestForReject.id,\n          { reason: this.quickRejectForm.reason }\n        );\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.showToast('Success', `Request ${this.selectedRequestForReject.request_number} rejected successfully`, 'success');\n          this.closeQuickRejectModal();\n        }\n      } catch (error) {\n        console.error('Failed to reject request:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.quickRejectForm.error = errorData.message || 'Failed to reject request';\n      } finally {\n        this.quickRejectForm.loading = false;\n      }\n    },\n\n    // Bulk operations\n    async performBulkAction() {\n      if (this.selectedRequests.length === 0) {\n        this.errorMessage = 'Please select at least one request';\n        return;\n      }\n\n      if (!this.bulkAction) {\n        this.errorMessage = 'Please select a bulk action';\n        return;\n      }\n\n      try {\n        const response = await adminDocumentService.bulkUpdateRequests({\n          request_ids: this.selectedRequests,\n          status_id: parseInt(this.bulkAction),\n          reason: this.bulkReason\n        });\n\n        if (response.success) {\n          await this.loadRequests();\n          await this.loadDashboardStats();\n          this.selectedRequests = [];\n          this.bulkAction = '';\n          this.bulkReason = '';\n          this.showBulkActions = false;\n          this.errorMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to perform bulk action:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to perform bulk action';\n      }\n    },\n\n    // Export functionality\n    async exportRequests() {\n      try {\n        const csvData = await adminDocumentService.exportRequests(this.filters);\n        const filename = `document_requests_${new Date().toISOString().split('T')[0]}.csv`;\n        adminDocumentService.downloadCSV(csvData, filename);\n      } catch (error) {\n        console.error('Failed to export requests:', error);\n        const errorData = adminDocumentService.parseError(error);\n        this.errorMessage = errorData.message || 'Failed to export requests';\n      }\n    },\n\n    // Utility methods\n    formatStatus(status) {\n      return adminDocumentService.formatStatus(status);\n    },\n\n    getStatusColor(status) {\n      return adminDocumentService.getStatusColor(status);\n    },\n\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    },\n\n    formatCurrency(amount) {\n      return new Intl.NumberFormat('en-PH', {\n        style: 'currency',\n        currency: 'PHP'\n      }).format(amount || 0);\n    },\n\n    formatDateTime(dateString) {\n      if (!dateString) return 'N/A';\n      return new Date(dateString).toLocaleString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    },\n\n    formatTime(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleTimeString('en-US', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n      });\n    },\n\n    // Real-time features\n    async initializeRealTimeFeatures() {\n      console.log('Initializing real-time features for AdminRequests');\n\n      try {\n        // Initialize notification service\n        await notificationService.init('admin');\n\n        // Listen for request-related notifications\n        notificationService.on('notification', this.handleRealTimeNotification);\n        notificationService.on('request_status_changed', this.handleStatusChange);\n        notificationService.on('new_request', this.handleNewRequest);\n\n        // Start auto-refresh if enabled\n        if (this.autoRefreshEnabled) {\n          this.startAutoRefresh();\n        }\n      } catch (error) {\n        console.error('Failed to initialize real-time features:', error);\n      }\n    },\n\n    cleanupRealTimeFeatures() {\n      console.log('Cleaning up real-time features for AdminRequests');\n\n      // Remove notification listeners\n      notificationService.off('notification', this.handleRealTimeNotification);\n      notificationService.off('request_status_changed', this.handleStatusChange);\n      notificationService.off('new_request', this.handleNewRequest);\n\n      // Cleanup (simplified)\n      notificationService.cleanup();\n\n      // Stop auto-refresh\n      this.stopAutoRefresh();\n    },\n\n    startAutoRefresh() {\n      if (this.refreshInterval) {\n        clearInterval(this.refreshInterval);\n      }\n\n      this.refreshInterval = setInterval(() => {\n        if (this.autoRefreshEnabled && !this.loading) {\n          console.log('Auto-refreshing requests data...');\n          this.refreshRequestsData();\n        }\n      }, this.refreshRate);\n\n      console.log(`Auto-refresh started with ${this.refreshRate / 1000}s interval`);\n    },\n\n    stopAutoRefresh() {\n      if (this.refreshInterval) {\n        clearInterval(this.refreshInterval);\n        this.refreshInterval = null;\n        console.log('Auto-refresh stopped');\n      }\n    },\n\n    toggleAutoRefresh() {\n      this.autoRefreshEnabled = !this.autoRefreshEnabled;\n\n      if (this.autoRefreshEnabled) {\n        this.startAutoRefresh();\n      } else {\n        this.stopAutoRefresh();\n      }\n    },\n\n    async refreshRequestsData() {\n      try {\n        this.lastRefresh = new Date();\n\n        // Refresh requests list\n        await this.loadRequests();\n\n        // Refresh statistics\n        await this.loadDashboardStats();\n\n        // If request details modal is open, refresh it\n        if (this.showRequestDetails && this.currentRequest) {\n          await this.refreshRequestDetails();\n        }\n\n        console.log('Requests data refreshed successfully');\n      } catch (error) {\n        console.error('Failed to refresh requests data:', error);\n      }\n    },\n\n    handleRealTimeNotification(notification) {\n      console.log('Real-time notification received:', notification);\n\n      // Handle different notification types\n      switch (notification.type) {\n        case 'request_status_changed':\n          this.handleStatusChange(notification.data);\n          break;\n        case 'new_request':\n          this.handleNewRequest(notification.data);\n          break;\n        case 'request_updated':\n          this.handleRequestUpdate(notification.data);\n          break;\n        case 'unread_count_update':\n        case 'heartbeat':\n          // Polling system notifications - handled by notification service\n          break;\n        default:\n          // Only log unknown types, not system types\n          if (!['unread_count_update', 'heartbeat'].includes(notification.type)) {\n            console.log('Unhandled notification type:', notification.type);\n          }\n      }\n    },\n\n    handleStatusChange(data) {\n      console.log('Request status changed:', data);\n\n      // Update the request in the list if it exists\n      const requestIndex = this.requests.findIndex(req => req.id === data.request_id);\n      if (requestIndex !== -1) {\n        // Refresh the specific request or reload all requests\n        this.refreshRequestsData();\n      }\n\n      // Show toast notification\n      this.showToast('Request status updated', `Request #${data.request_id} status changed to ${data.new_status}`, 'info');\n    },\n\n    handleNewRequest(data) {\n      console.log('New request received:', data);\n\n      // Refresh requests to show the new request\n      this.refreshRequestsData();\n\n      // Show toast notification\n      this.showToast('New Request', `New ${data.document_type} request received`, 'success');\n    },\n\n    handleRequestUpdate(data) {\n      console.log('Request updated:', data);\n\n      // If the updated request is currently being viewed, refresh details\n      if (this.currentRequest && this.currentRequest.id === data.request_id) {\n        this.refreshRequestDetails();\n      }\n\n      // Refresh the requests list\n      this.refreshRequestsData();\n    },\n\n    showToast(title, message, type = 'info') {\n      // Simple toast notification - you can enhance this with a proper toast library\n      console.log(`[${type.toUpperCase()}] ${title}: ${message}`);\n\n      // You can implement a proper toast notification system here\n      // For now, we'll just log to console and could show a temporary alert\n    }\n  }\n};\n</script>\n\n<style scoped>\n@import './css/adminDashboard.css';\n\n/* Additional styles specific to AdminRequests */\n.admin-requests {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);\n}\n\n/* Ensure proper spacing for request statistics cards */\n.card.border-left-primary {\n  border-left: 4px solid #3b82f6 !important;\n}\n\n.card.border-left-warning {\n  border-left: 4px solid #f59e0b !important;\n}\n\n.card.border-left-success {\n  border-left: 4px solid #059669 !important;\n}\n\n.card.border-left-info {\n  border-left: 4px solid #06b6d4 !important;\n}\n\n/* Bootstrap utility classes for compatibility */\n.text-xs {\n  font-size: 0.75rem !important;\n}\n\n.text-gray-800 {\n  color: #1f2937 !important;\n}\n\n.text-gray-300 {\n  color: #d1d5db !important;\n}\n\n.text-muted {\n  color: #6c757d !important;\n}\n\n.fw-bold {\n  font-weight: 700 !important;\n}\n\n.g-0 {\n  --bs-gutter-x: 0;\n  --bs-gutter-y: 0;\n}\n\n.me-2 {\n  margin-right: 0.5rem !important;\n}\n\n/* Improve button spacing */\n.d-flex.gap-2 {\n  gap: 0.5rem !important;\n}\n\n/* Timeline Styles */\n.timeline {\n  position: relative;\n  padding-left: 2rem;\n}\n\n.timeline::before {\n  content: '';\n  position: absolute;\n  left: 1rem;\n  top: 0;\n  bottom: 0;\n  width: 2px;\n  background: #e3e6f0;\n}\n\n.timeline-item {\n  position: relative;\n  margin-bottom: 2rem;\n}\n\n.timeline-item:last-child {\n  margin-bottom: 0;\n}\n\n.timeline-item.timeline-item-last::after {\n  display: none;\n}\n\n.timeline-marker {\n  position: absolute;\n  left: -2rem;\n  top: 0.25rem;\n  width: 2rem;\n  height: 2rem;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 0.75rem;\n  z-index: 1;\n}\n\n.timeline-content {\n  background: #f8f9fc;\n  border-radius: 8px;\n  padding: 1rem;\n  border-left: 3px solid #e3e6f0;\n}\n\n.timeline-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 0.5rem;\n}\n\n.timeline-body p {\n  margin-bottom: 0.25rem;\n  font-size: 0.875rem;\n}\n\n.timeline-body p:last-child {\n  margin-bottom: 0;\n}\n\n/* Modal Styles */\n.modal-xl {\n  max-width: 1200px;\n}\n\n.modal-dialog-scrollable .modal-body {\n  max-height: 70vh;\n  overflow-y: auto;\n}\n\n/* Real-time status indicator styles */\n.real-time-status .badge {\n  font-size: 0.75rem;\n  padding: 0.375rem 0.75rem;\n  border-radius: 1rem;\n}\n\n.pulse {\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n\n/* Card View Styles */\n.requests-grid {\n  min-height: 400px;\n}\n\n.empty-state {\n  background: #f8f9fa;\n  border-radius: 12px;\n  padding: 3rem 2rem;\n  margin: 2rem 0;\n}\n\n.empty-state-icon {\n  opacity: 0.5;\n}\n\n.request-card {\n  background: #ffffff;\n  border: 1px solid #e3e6f0;\n  border-radius: 12px;\n  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);\n  transition: all 0.3s ease;\n  overflow: hidden;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n.request-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);\n  border-color: #5a5c69;\n}\n\n.request-card.selected {\n  border-color: #4e73df;\n  box-shadow: 0 0 0 2px rgba(78, 115, 223, 0.25);\n}\n\n.request-card-header {\n  padding: 1rem 1.25rem 0.5rem;\n  border-bottom: 1px solid #f1f1f1;\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\n}\n\n.request-card-body {\n  padding: 1.25rem;\n  flex-grow: 1;\n}\n\n.request-card-footer {\n  padding: 0.75rem 1.25rem 1.25rem;\n  background: #f8f9fa;\n  border-top: 1px solid #e3e6f0;\n}\n\n.client-avatar {\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  color: white;\n  font-size: 1.2rem;\n}\n\n.client-info h6 {\n  color: #2c3e50;\n  font-weight: 600;\n}\n\n.document-type {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 0.75rem;\n  border-left: 4px solid #17a2b8;\n}\n\n.document-type .badge {\n  font-size: 0.875rem;\n  font-weight: 500;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n}\n\n.request-meta {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 0.75rem;\n}\n\n.meta-item {\n  text-align: center;\n}\n\n.meta-item small {\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.request-date {\n  padding-top: 0.75rem;\n  border-top: 1px solid #e9ecef;\n  margin-top: 0.75rem;\n}\n\n.request-actions .dropdown-toggle {\n  border: none;\n  background: transparent;\n  color: #6c757d;\n  padding: 0.25rem 0.5rem;\n  border-radius: 6px;\n}\n\n.request-actions .dropdown-toggle:hover {\n  background: #e9ecef;\n  color: #495057;\n}\n\n/* View Toggle Styles */\n.btn-check:checked + .btn-outline-primary {\n  background-color: #4e73df;\n  border-color: #4e73df;\n  color: white;\n}\n\n/* Badge Enhancements */\n.badge.bg-info-subtle {\n  background-color: #cff4fc !important;\n  color: #055160 !important;\n  border: 1px solid #b6effb;\n}\n\n/* Button Enhancements */\n.request-card-footer .btn {\n  border-radius: 6px;\n  font-weight: 500;\n  transition: all 0.2s ease;\n}\n\n.request-card-footer .btn:hover {\n  transform: translateY(-1px);\n}\n\n/* Modern Table Styles */\n.modern-table-container {\n  background: #ffffff;\n  border-radius: 16px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  overflow: visible;\n  border: 1px solid #e8ecef;\n}\n\n.modern-table-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 1.5rem 2rem;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.modern-table-header h5 {\n  color: white;\n  margin: 0;\n  font-weight: 600;\n}\n\n.table-actions .btn {\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  color: white;\n  backdrop-filter: blur(10px);\n}\n\n.table-actions .btn:hover {\n  background: rgba(255, 255, 255, 0.2);\n  border-color: rgba(255, 255, 255, 0.3);\n  color: white;\n}\n\n.modern-table-empty {\n  padding: 4rem 2rem;\n  text-align: center;\n  background: #f8f9fa;\n}\n\n.empty-content {\n  max-width: 400px;\n  margin: 0 auto;\n}\n\n.empty-icon {\n  font-size: 4rem;\n  color: #6c757d;\n  margin-bottom: 1.5rem;\n  opacity: 0.5;\n}\n\n.empty-title {\n  color: #495057;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n}\n\n.empty-text {\n  color: #6c757d;\n  margin: 0;\n}\n\n.modern-table {\n  background: white;\n}\n\n.table-row {\n  display: grid;\n  grid-template-columns: 50px 180px 1fr 160px 140px 120px 140px 160px;\n  align-items: center;\n  padding: 1.25rem 2rem;\n  border-bottom: 1px solid #f1f3f4;\n  transition: all 0.2s ease;\n  position: relative;\n}\n\n.table-row:hover {\n  background: #f8f9fa;\n  transform: translateX(4px);\n  box-shadow: 4px 0 0 #667eea;\n}\n\n.table-row.selected {\n  background: #e3f2fd;\n  border-left: 4px solid #2196f3;\n}\n\n.table-row:last-child {\n  border-bottom: none;\n}\n\n.table-cell {\n  display: flex;\n  align-items: center;\n  min-height: 60px;\n}\n\n/* Selection Cell */\n.selection-cell {\n  justify-content: center;\n}\n\n.selection-cell .form-check-input {\n  width: 18px;\n  height: 18px;\n  border-radius: 4px;\n  border: 2px solid #dee2e6;\n}\n\n.selection-cell .form-check-input:checked {\n  background-color: #667eea;\n  border-color: #667eea;\n}\n\n/* Request Number Cell */\n.request-number-content {\n  display: flex;\n  flex-direction: column;\n}\n\n.request-number {\n  font-weight: 700;\n  color: #667eea;\n  font-size: 1rem;\n  letter-spacing: 0.5px;\n}\n\n.request-id {\n  font-size: 0.75rem;\n  color: #6c757d;\n  margin-top: 2px;\n}\n\n/* Client Cell */\n.client-info {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.client-avatar-sm {\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-size: 1rem;\n  flex-shrink: 0;\n}\n\n.client-details {\n  min-width: 0;\n  flex: 1;\n}\n\n.client-name {\n  font-weight: 600;\n  color: #2c3e50;\n  font-size: 0.95rem;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.client-email {\n  font-size: 0.8rem;\n  color: #6c757d;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  margin-top: 2px;\n}\n\n/* Document Type Cell */\n.document-type-badge {\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\n  color: white;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-size: 0.85rem;\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);\n}\n\n/* Status Cell */\n.status-badge {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  padding: 0.5rem 1rem;\n  border-radius: 20px;\n  font-size: 0.85rem;\n  font-weight: 500;\n  text-transform: capitalize;\n}\n\n.status-indicator {\n  font-size: 0.6rem;\n  animation: pulse 2s infinite;\n}\n\n.status-success {\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);\n}\n\n.status-warning {\n  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);\n  color: #212529;\n  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);\n}\n\n.status-danger {\n  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);\n}\n\n.status-info {\n  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3);\n}\n\n.status-secondary {\n  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);\n  color: white;\n  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);\n}\n\n/* Amount Cell */\n.amount-content {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.amount {\n  font-weight: 700;\n  color: #28a745;\n  font-size: 1.1rem;\n}\n\n.currency {\n  font-size: 0.75rem;\n  color: #6c757d;\n  margin-top: 2px;\n}\n\n/* Date Cell */\n.date-content {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.date {\n  font-weight: 500;\n  color: #495057;\n  font-size: 0.9rem;\n}\n\n.time {\n  font-size: 0.75rem;\n  color: #6c757d;\n  margin-top: 2px;\n}\n\n/* Actions Cell */\n.action-buttons {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  position: relative;\n}\n\n.action-btn {\n  width: 36px;\n  height: 36px;\n  border: none;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 0.875rem;\n  transition: all 0.2s ease;\n  cursor: pointer;\n}\n\n.view-btn {\n  background: #e3f2fd;\n  color: #1976d2;\n}\n\n.view-btn:hover {\n  background: #bbdefb;\n  transform: translateY(-2px);\n}\n\n.approve-btn {\n  background: #e8f5e8;\n  color: #2e7d32;\n}\n\n.approve-btn:hover {\n  background: #c8e6c9;\n  transform: translateY(-2px);\n}\n\n.reject-btn {\n  background: #ffebee;\n  color: #c62828;\n}\n\n.reject-btn:hover {\n  background: #ffcdd2;\n  transform: translateY(-2px);\n}\n\n.more-btn {\n  background: #f5f5f5;\n  color: #666;\n}\n\n.more-btn:hover {\n  background: #e0e0e0;\n  transform: translateY(-2px);\n}\n\n.more-btn::after {\n  display: none;\n}\n\n/* Dropdown positioning fixes */\n.modern-table {\n  overflow: visible;\n}\n\n.table-row {\n  overflow: visible;\n}\n\n.action-buttons .dropdown {\n  position: static;\n}\n\n.action-buttons .dropdown-menu {\n  position: absolute !important;\n  top: 100% !important;\n  right: 0 !important;\n  left: auto !important;\n  z-index: 1050 !important;\n  transform: none !important;\n  margin-top: 0.25rem;\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  border-radius: 8px;\n  background: white;\n  min-width: 160px;\n}\n\n.action-buttons .dropdown-menu.show {\n  display: block !important;\n}\n\n/* Ensure dropdown appears above other elements */\n.action-buttons .dropdown.show {\n  z-index: 1051;\n}\n\n/* Pagination Container */\n.pagination-container {\n  background: white;\n  border-radius: 0 0 16px 16px;\n  padding: 1.5rem 2rem;\n  border-top: 1px solid #f1f3f4;\n  margin-top: -1px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n}\n\n.pagination-container .pagination {\n  margin: 0;\n}\n\n.pagination-container .page-link {\n  border: 1px solid #e3e6f0;\n  color: #667eea;\n  padding: 0.5rem 0.75rem;\n  margin: 0 2px;\n  border-radius: 8px;\n  transition: all 0.2s ease;\n}\n\n.pagination-container .page-link:hover {\n  background: #667eea;\n  border-color: #667eea;\n  color: white;\n  transform: translateY(-1px);\n}\n\n.pagination-container .page-item.active .page-link {\n  background: #667eea;\n  border-color: #667eea;\n  color: white;\n  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);\n}\n\n.pagination-container .page-item.disabled .page-link {\n  color: #6c757d;\n  background: #f8f9fa;\n  border-color: #e3e6f0;\n}\n\n/* Responsive improvements */\n@media (max-width: 768px) {\n  .d-flex.gap-2 {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .d-flex.gap-2 .btn {\n    margin-bottom: 0.5rem;\n  }\n\n  .modal-xl {\n    max-width: 95%;\n    margin: 1rem auto;\n  }\n\n  .timeline {\n    padding-left: 1.5rem;\n  }\n\n  .timeline-marker {\n    left: -1.5rem;\n    width: 1.5rem;\n    height: 1.5rem;\n    font-size: 0.625rem;\n  }\n\n  /* Modern table mobile adjustments */\n  .modern-table-header {\n    padding: 1rem;\n  }\n\n  .table-row {\n    grid-template-columns: 1fr;\n    padding: 1rem;\n    gap: 1rem;\n  }\n\n  .table-cell {\n    min-height: auto;\n    flex-direction: column;\n    align-items: flex-start;\n    padding: 0.5rem 0;\n    border-bottom: 1px solid #f1f3f4;\n  }\n\n  .table-cell:last-child {\n    border-bottom: none;\n  }\n\n  .selection-cell {\n    flex-direction: row;\n    justify-content: flex-start;\n  }\n\n  .client-info {\n    width: 100%;\n  }\n\n  .client-details {\n    flex: 1;\n  }\n\n  .document-type-badge,\n  .status-badge {\n    align-self: flex-start;\n  }\n\n  .amount-content,\n  .date-content {\n    align-items: flex-start;\n  }\n\n  .action-buttons {\n    width: 100%;\n    justify-content: space-between;\n  }\n\n  .action-btn {\n    flex: 1;\n    max-width: 60px;\n  }\n\n  /* Card view mobile adjustments */\n  .request-card {\n    margin-bottom: 1rem;\n  }\n\n  .request-card-header,\n  .request-card-body,\n  .request-card-footer {\n    padding: 1rem;\n  }\n\n  .client-info .d-flex {\n    flex-direction: column;\n    text-align: center;\n    gap: 0.5rem;\n  }\n\n  .client-avatar {\n    align-self: center;\n  }\n\n  .request-meta .row {\n    text-align: center;\n  }\n\n  .request-card-footer .d-flex {\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n\n  .request-card-footer .btn {\n    width: 100%;\n  }\n\n  /* View toggle mobile */\n  .btn-group {\n    width: 100%;\n  }\n\n  .btn-group .btn {\n    flex: 1;\n  }\n}\n\n@media (max-width: 576px) {\n  .empty-state {\n    padding: 2rem 1rem;\n  }\n\n  .empty-state-icon {\n    font-size: 3rem;\n  }\n\n  .request-card-body {\n    padding: 1rem;\n  }\n\n  .document-type,\n  .request-meta {\n    padding: 0.5rem;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAgB;;EAmBpBA,KAAK,EAAC;AAAqB;;;EAWRA,KAAK,EAAC,kDAAkD;EAACC,KAA0B,EAA1B;IAAA;EAAA;;;EAOjED,KAAK,EAAC;AAAsB;;;EAEbA,KAAK,EAAC,gDAAgD;EAACE,IAAI,EAAC;;;EAOhFF,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAQ;;EACZA,KAAK,EAAC;AAA6D;;EAGjEA,KAAK,EAAC;AAAiB;;;EAECA,KAAK,EAAC;;;EAM9BA,KAAK,EAAC;AAAiC;;EAErCA,KAAK,EAAC;AAAuB;;;EAE3BA,KAAK,EAAC;;;;EACNA,KAAK,EAAC;;;;;;EA0BhBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAA4C;;EAChDA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAAU;;EAIdA,KAAK,EAAC;AAA2B;;EAS3CA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAA4C;;EAChDA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAAU;;EAIdA,KAAK,EAAC;AAA2B;;EAS3CA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAA4C;;EAChDA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAAU;;EAIdA,KAAK,EAAC;AAA2B;;EAS3CA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAAyC;;EAC7CA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAA4B;;EAChCA,KAAK,EAAC;AAAU;;EAIdA,KAAK,EAAC;AAA2B;;;EAY1BA,KAAK,EAAC;;;EAIvBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;EAUrBA,KAAK,EAAC;AAAe;;;EASrBA,KAAK,EAAC;AAAe;;EAQrBA,KAAK,EAAC;AAAe;;EAIrBA,KAAK,EAAC;AAAe;;EAIrBA,KAAK,EAAC;AAAsC;;EAC1CA,KAAK,EAAC;AAAoB;;;EAcCA,KAAK,EAAC;;;EACvCA,KAAK,EAAC;AAA6B;;EAClCA,KAAK,EAAC;AAAuB;;EAK9BA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAe;;;EASrBA,KAAK,EAAC;AAAe;;EASrBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAc;;;EAgB5BA,KAAK,EAAC;AAAwD;;EAC5DA,KAAK,EAAC;AAAiC;;EACrCA,KAAK,EAAC,WAAW;EAACE,IAAI,EAAC,OAAO;EAAC,YAAU,EAAC;;;EAY1CF,KAAK,EAAC;AAAiC;;EACpCA,KAAK,EAAC;AAAkB;;EAc7BA,KAAK,EAAC;AAAiC;;;EASdA,KAAK,EAAC;;;;EAEFA,KAAK,EAAC;;;EAS5BA,KAAK,EAAC;AAAS;;EAIhBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAkD;;EACtDA,KAAK,EAAC;AAAiC;;;EAOrCA,KAAK,EAAC;AAAgB;;EACnBA,KAAK,EAAC;AAAkB;;EAG7BA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAU;;EAIfA,KAAK,EAAC;AAAiC;;;;;EAwB9CA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAsC;;EAKzCA,KAAK,EAAC;AAAc;;EACjBA,KAAK,EAAC;AAAY;;EAM1BA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAiC;;EAEpCA,KAAK,EAAC;AAAmD;;EAO9DA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAS;;EACbA,KAAK,EAAC;AAAO;;EACXA,KAAK,EAAC;AAAW;;EAOnBA,KAAK,EAAC;AAAO;;EACXA,KAAK,EAAC;AAAW;;EAEdA,KAAK,EAAC;AAAsB;;EAOrCA,KAAK,EAAC;AAAc;;EAChBA,KAAK,EAAC;AAAY;;EAQxBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAc;;;;;EA8BvBA,KAAK,EAAC;AAAwB;;EAEnCA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAmD;;EAKvDA,KAAK,EAAC;AAA4B;;;EAUTA,KAAK,EAAC;;;EAW5BA,KAAK,EAAC;AAAc;;EAIvBA,KAAK,EAAC;AAA2B;;;EAUjCA,KAAK,EAAC;AAAgC;;EACpCA,KAAK,EAAC;AAAwB;;EAC3BA,KAAK,EAAC;AAAgB;;EACtBA,KAAK,EAAC;AAAY;;EAKvBA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAAa;;EAIjBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAc;;EAM1BA,KAAK,EAAC;AAA+B;;EACnCA,KAAK,EAAC;AAAqB;;EAO7BA,KAAK,EAAC;AAAwB;;EAQ9BA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAAgB;;EACnBA,KAAK,EAAC;AAAQ;;EAMnBA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAc;;EACjBA,KAAK,EAAC;AAAM;;EACZA,KAAK,EAAC;AAAM;;EAKjBA,KAAK,EAAC;AAAyB;;EAC7BA,KAAK,EAAC;AAAgB;;;;;EAsBpBA,KAAK,EAAC;AAAU;;EAIfA,KAAK,EAAC;AAAiC;;;;;;EA0BjBA,KAAK,EAAC;;;EACnC,YAAU,EAAC;AAAqB;;EAC/BA,KAAK,EAAC;AAAsD;;;;EAyBrBA,KAAK,EAAC,yBAAyB;EAACG,QAAQ,EAAC,IAAI;EAACF,KAA0C,EAA1C;IAAA;EAAA;;;EACxFD,KAAK,EAAC;AAA+C;;EACnDA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAc;;EACnBA,KAAK,EAAC;AAAa;;EAMpBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAK;;EAETA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EACPA,KAAK,EAAC;AAAe;;EAG1BA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAGdA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAMZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAMZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAQpBA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;;EAKdA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;;EAIZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;;EAQmBA,KAAK,EAAC;;;EAI3CA,KAAK,EAAC;AAAW;;;;;EAGbA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAGdA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAQhBA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAGdA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAUxBA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAM;;;EASZA,KAAK,EAAC;AAAM;;EASZA,KAAK,EAAC;AAAc;;;;EAoBEA,KAAK,EAAC;;;EAC1BA,KAAK,EAAC;AAAM;;EAUZA,KAAK,EAAC;AAAQ;;;EAepBA,KAAK,EAAC;AAAW;;EAIfA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAMZA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAO;;EACXA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAGdA,KAAK,EAAC;AAAO;;EACXA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAGdA,KAAK,EAAC;AAAO;;EACXA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAAM;;EAGdA,KAAK,EAAC;AAAO;;EACXA,KAAK,EAAC;AAAM;;EAEZA,KAAK,EAAC;AAA2B;;EAU7CA,KAAK,EAAC;AAAM;;EAIVA,KAAK,EAAC;AAAW;;;EACkEA,KAAK,EAAC;;;EAUnFA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAiB;;EAInBA,KAAK,EAAC;AAAiB;;EAE3BA,KAAK,EAAC;AAAe;;EACrBA,KAAK,EAAC;AAAM;;;EAGmBA,KAAK,EAAC;;;;EAGRA,KAAK,EAAC;;;;EAOlCA,KAAK,EAAC;;;EAOnBA,KAAK,EAAC;AAAc;;;EAeyBA,KAAK,EAAC,yBAAyB;EAACG,QAAQ,EAAC,IAAI;EAACF,KAA0C,EAA1C;IAAA;EAAA;;;EAC/FD,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAc;;EAOpBA,KAAK,EAAC;AAAY;;EAMhBA,KAAK,EAAC;AAAM;;EAEXA,KAAK,EAAC;AAAoB;;EAO3BA,KAAK,EAAC;AAAM;;;EAYmBA,KAAK,EAAC;;;EAQvCA,KAAK,EAAC;AAAc;;;;;;;;;;;;uBAjgCvCI,mBAAA,CAohCQ,OAphCRC,UAohCQ,GAnhCNC,YAAA,CASEC,sBAAA;IARCC,QAAQ,EAAEC,KAAA,CAAAC,SAAS,EAAEC,UAAU;IAC/BC,gBAAgB,EAAEH,KAAA,CAAAG,gBAAgB;IAClCC,gBAAgB,EAAEJ,KAAA,CAAAI,gBAAgB;IAClCC,UAAU,EAAEC,QAAA,CAAAD,UAAU;IACtBE,eAAc,EAAED,QAAA,CAAAE,mBAAmB;IACnCC,oBAAoB,EAAEH,QAAA,CAAAI,wBAAwB;IAC9CC,YAAW,EAAEL,QAAA,CAAAM,gBAAgB;IAC7BC,QAAM,EAAEP,QAAA,CAAAQ;sKAGXC,mBAAA,oBAAuB,EACvBC,mBAAA,CAIO;IAHLzB,KAAK,EAAA0B,eAAA,EAAC,gBAAgB;MAAAC,MAAA,GACHlB,KAAA,CAAAI,gBAAgB,IAAIJ,KAAA,CAAAmB;IAAQ;IAC9CC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAiB,kBAAA,IAAAjB,QAAA,CAAAiB,kBAAA,IAAAD,IAAA,CAAkB;2BAG5BN,mBAAA,CAggCQ,OAhgCRQ,UAggCQ,GA//BN3B,YAAA,CAME4B,uBAAA;IALCC,SAAS,EAAE1B,KAAA,CAAAI,gBAAgB;IAC3BC,UAAU,EAAEC,QAAA,CAAAD,UAAU;IACtBsB,YAAW,EAAErB,QAAA,CAAAsB,gBAAgB;IAC7Bf,QAAM,EAAEP,QAAA,CAAAQ,YAAY;IACpBe,eAAc,EAAEvB,QAAA,CAAAE;uGAGnBQ,mBAAA,CAs/BS;IAt/BHzB,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,qBAAgCjB,KAAA,CAAAI;IAAgB;MACxEW,mBAAA,mBAAsB,EACXf,KAAA,CAAA8B,OAAO,I,cAAlBnC,mBAAA,CAIM,OAJNoC,UAIM,EAAAV,MAAA,SAAAA,MAAA,QAHJL,mBAAA,CAEM;IAFDzB,KAAK,EAAC,6BAA6B;IAACE,IAAI,EAAC;MAC5CuB,mBAAA,CAA+C;IAAzCzB,KAAK,EAAC;EAAiB,GAAC,YAAU,E,yCAK5CI,mBAAA,CAkkBQqC,SAAA;IAAAC,GAAA;EAAA,IAnkBRlB,mBAAA,kBAAqB,EACrBC,mBAAA,CAkkBQ,OAlkBRkB,UAkkBQ,GAjkBNnB,mBAAA,mBAAsB,EACXf,KAAA,CAAAmC,YAAY,I,cAAvBxC,mBAAA,CAIM,OAJNyC,UAIM,G,4BAHJpB,mBAAA,CAAgD;IAA7CzB,KAAK,EAAC;EAAkC,6B,iBAAK,GAChD,GAAA8C,gBAAA,CAAGrC,KAAA,CAAAmC,YAAY,IAAG,GAClB,iBAAAnB,mBAAA,CAA+F;IAAvFsB,IAAI,EAAC,QAAQ;IAAC/C,KAAK,EAAC,WAAW;IAAE6B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAkB,MAAA,IAAEvC,KAAA,CAAAmC,YAAY;IAAO,YAAU,EAAC;6CAGhFpB,mBAAA,iBAAoB,EACpBC,mBAAA,CAyCM,OAzCNwB,UAyCM,GAxCJxB,mBAAA,CAuCM,OAvCNyB,UAuCM,GAtCJzB,mBAAA,CAqCM,OArCN0B,UAqCM,GApCJ1B,mBAAA,CASM,c,4BARJA,mBAAA,CAAkE;IAA9DzB,KAAK,EAAC;EAAuB,GAAC,6BAA2B,sBAC7DyB,mBAAA,CAMI,KANJ2B,UAMI,G,6CANuB,wCAEzB,IAAY3C,KAAA,CAAA4C,WAAW,I,cAAvBjD,mBAAA,CAGO,QAHPkD,WAGO,G,4BAFL7B,mBAAA,CAAuC;IAApCzB,KAAK,EAAC;EAAyB,6B,iBAAK,iBACzB,GAAA8C,gBAAA,CAAG/B,QAAA,CAAAwC,UAAU,CAAC9C,KAAA,CAAA4C,WAAW,kB,4CAI7C5B,mBAAA,CAyBM,OAzBN+B,WAyBM,GAxBJhC,mBAAA,gCAAmC,EACnCC,mBAAA,CAMM,OANNgC,WAMM,GALJhC,mBAAA,CAIO;IAJDzB,KAAK,EAAA0B,eAAA,EAAC,OAAO,EAASjB,KAAA,CAAAiD,kBAAkB;MACPjD,KAAA,CAAAiD,kBAAkB,I,cAAvDtD,mBAAA,CAA6D,KAA7DuD,WAA6D,M,cAC7DvD,mBAAA,CAAmC,KAAnCwD,WAAmC,I,iBAAA,GACnC,GAAAd,gBAAA,CAAGrC,KAAA,CAAAiD,kBAAkB,qC,oBAIzBjC,mBAAA,CAES;IAFDzB,KAAK,EAAC,kCAAkC;IAAE6B,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAA8C,iBAAA,IAAA9C,QAAA,CAAA8C,iBAAA,IAAA9B,IAAA,CAAiB;IAAG+B,KAAK,EAAErD,KAAA,CAAAiD,kBAAkB;MACpGjC,mBAAA,CAAwE;IAArEzB,KAAK,EAAA0B,eAAA,EAAC,KAAK,EAASjB,KAAA,CAAAiD,kBAAkB;yDAE3CjC,mBAAA,CAGS;IAHDzB,KAAK,EAAC,gCAAgC;IAAE6B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAkB,MAAA,IAAEvC,KAAA,CAAAsD,WAAW,IAAItD,KAAA,CAAAsD,WAAW;kCAC/EtC,mBAAA,CAAkC;IAA/BzB,KAAK,EAAC;EAAoB,6B,iBAAK,GAClC,GAAA8C,gBAAA,CAAGrC,KAAA,CAAAsD,WAAW,sBAAqB,WACrC,gB,GACAtC,mBAAA,CAGS;IAHDzB,KAAK,EAAC,wBAAwB;IAAE6B,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAiD,cAAA,IAAAjD,QAAA,CAAAiD,cAAA,IAAAjC,IAAA,CAAc;IAAGkC,QAAQ,EAAExD,KAAA,CAAA8B;kCACxEd,mBAAA,CAAoC;IAAjCzB,KAAK,EAAC;EAAsB,4B,iBAAK,cAEtC,E,gCACAyB,mBAAA,CAGS;IAHDzB,KAAK,EAAC,wBAAwB;IAAE6B,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAmD,mBAAA,IAAAnD,QAAA,CAAAmD,mBAAA,IAAAnC,IAAA,CAAmB;IAAGkC,QAAQ,EAAExD,KAAA,CAAA8B;MAC7Ed,mBAAA,CAAoE;IAAjEzB,KAAK,EAAA0B,eAAA,EAAC,sBAAsB;MAAA,WAAsBjB,KAAA,CAAA8B;IAAO;wEAAQ,WAEtE,G,uCAMRf,mBAAA,wBAA2B,EAC3BC,mBAAA,CAqEM,OArEN0C,WAqEM,GApEJ1C,mBAAA,CAgBM,OAhBN2C,WAgBM,GAfJ3C,mBAAA,CAcM,OAdN4C,WAcM,GAbJ5C,mBAAA,CAYM,OAZN6C,WAYM,GAXJ7C,mBAAA,CAUM,OAVN8C,WAUM,GATJ9C,mBAAA,CAKM,OALN+C,WAKM,G,4BAJJ/C,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAkD,GAAC,kBAE9D,sBACAyB,mBAAA,CAA0E,OAA1EgD,WAA0E,EAAA3B,gBAAA,CAAhCrC,KAAA,CAAAiE,YAAY,CAACC,KAAK,sB,+BAE9DlD,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAU,IACnByB,mBAAA,CAAgD;IAA7CzB,KAAK,EAAC;EAAkC,G,8BAMrDyB,mBAAA,CAgBM,OAhBNmD,WAgBM,GAfJnD,mBAAA,CAcM,OAdNoD,WAcM,GAbJpD,mBAAA,CAYM,OAZNqD,WAYM,GAXJrD,mBAAA,CAUM,OAVNsD,WAUM,GATJtD,mBAAA,CAKM,OALNuD,WAKM,G,4BAJJvD,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAkD,GAAC,WAE9D,sBACAyB,mBAAA,CAA4E,OAA5EwD,WAA4E,EAAAnC,gBAAA,CAAlCrC,KAAA,CAAAiE,YAAY,CAACQ,OAAO,sB,+BAEhEzD,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAU,IACnByB,mBAAA,CAA6C;IAA1CzB,KAAK,EAAC;EAA+B,G,8BAMlDyB,mBAAA,CAgBM,OAhBN0D,WAgBM,GAfJ1D,mBAAA,CAcM,OAdN2D,WAcM,GAbJ3D,mBAAA,CAYM,OAZN4D,WAYM,GAXJ5D,mBAAA,CAUM,OAVN6D,WAUM,GATJ7D,mBAAA,CAKM,OALN8D,WAKM,G,4BAJJ9D,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAkD,GAAC,aAE9D,sBACAyB,mBAAA,CAA8E,OAA9E+D,WAA8E,EAAA1C,gBAAA,CAApCrC,KAAA,CAAAiE,YAAY,CAACe,SAAS,sB,+BAElEhE,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAU,IACnByB,mBAAA,CAAoD;IAAjDzB,KAAK,EAAC;EAAsC,G,8BAMzDyB,mBAAA,CAgBM,OAhBNiE,WAgBM,GAfJjE,mBAAA,CAcM,OAdNkE,WAcM,GAbJlE,mBAAA,CAYM,OAZNmE,WAYM,GAXJnE,mBAAA,CAUM,OAVNoE,WAUM,GATJpE,mBAAA,CAKM,OALNqE,WAKM,G,4BAJJrE,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAA+C,GAAC,YAE3D,sBACAyB,mBAAA,CAA6E,OAA7EsE,WAA6E,EAAAjD,gBAAA,CAAnCrC,KAAA,CAAAiE,YAAY,CAACsB,QAAQ,sB,+BAEjEvE,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAU,IACnByB,mBAAA,CAAiD;IAA9CzB,KAAK,EAAC;EAAmC,G,gCAQxDwB,mBAAA,mBAAsB,EACXf,KAAA,CAAAsD,WAAW,I,cAAtB3D,mBAAA,CAqDM,OArDN6F,WAqDM,G,4BApDJxE,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAkB,IAC3ByB,mBAAA,CAAyD;IAArDzB,KAAK,EAAC;EAA0B,GAAC,iBAAe,E,sBAEtDyB,mBAAA,CAgDM,OAhDNyE,WAgDM,GA/CJzE,mBAAA,CA8CM,OA9CN0E,WA8CM,GA7CJ1E,mBAAA,CASM,OATN2E,WASM,G,4BARJ3E,mBAAA,CAAwC;IAAjCzB,KAAK,EAAC;EAAY,GAAC,QAAM,sB,gBAChCyB,mBAAA,CAMC;IALCsB,IAAI,EAAC,MAAM;IACX/C,KAAK,EAAC,cAAc;+DACXS,KAAA,CAAA4F,OAAO,CAACC,MAAM,GAAAtD,MAAA;IACvBuD,WAAW,EAAC,0CAA0C;IACrDC,OAAK,EAAA1E,MAAA,QAAAA,MAAA,MAAA2E,SAAA,KAAA1E,IAAA,KAAQhB,QAAA,CAAA2F,YAAA,IAAA3F,QAAA,CAAA2F,YAAA,IAAA3E,IAAA,CAAY;iEAFjBtB,KAAA,CAAA4F,OAAO,CAACC,MAAM,E,KAK3B7E,mBAAA,CAQM,OARNkF,WAQM,G,4BAPJlF,mBAAA,CAAwC;IAAjCzB,KAAK,EAAC;EAAY,GAAC,QAAM,sB,gBAChCyB,mBAAA,CAKS;IALDzB,KAAK,EAAC,aAAa;+DAAUS,KAAA,CAAA4F,OAAO,CAACO,MAAM,GAAA5D,MAAA;kCACjDvB,mBAAA,CAAsC;IAA9BoF,KAAK,EAAC;EAAE,GAAC,cAAY,uB,kBAC7BzG,mBAAA,CAESqC,SAAA,QAAAqE,WAAA,CAFgBrG,KAAA,CAAAsG,aAAa,EAAvBH,MAAM;yBAArBxG,mBAAA,CAES;MAFgCsC,GAAG,EAAEkE,MAAM,CAACI,EAAE;MAAGH,KAAK,EAAED,MAAM,CAACK;wBACnElG,QAAA,CAAAmG,YAAY,CAACN,MAAM,CAACK,WAAW,yBAAAE,WAAA;2EAHD1G,KAAA,CAAA4F,OAAO,CAACO,MAAM,E,KAOrDnF,mBAAA,CAOM,OAPN2F,WAOM,G,4BANJ3F,mBAAA,CAA+C;IAAxCzB,KAAK,EAAC;EAAY,GAAC,eAAa,sB,gBACvCyB,mBAAA,CAIS;IAJDzB,KAAK,EAAC,aAAa;+DAAUS,KAAA,CAAA4F,OAAO,CAACgB,aAAa,GAAArE,MAAA;kCACxDvB,mBAAA,CAAmC;IAA3BoF,KAAK,EAAC;EAAE,GAAC,WAAS,qBAC1BpF,mBAAA,CAA8D;IAAtDoF,KAAK,EAAC;EAAoB,GAAC,oBAAkB,qBACrDpF,mBAAA,CAAsC;IAA9BoF,KAAK,EAAC;EAAQ,GAAC,QAAM,oB,2CAHMpG,KAAA,CAAA4F,OAAO,CAACgB,aAAa,E,KAM5D5F,mBAAA,CAGM,OAHN6F,WAGM,G,4BAFJ7F,mBAAA,CAA2C;IAApCzB,KAAK,EAAC;EAAY,GAAC,WAAS,sB,gBACnCyB,mBAAA,CAAoE;IAA7DsB,IAAI,EAAC,MAAM;IAAC/C,KAAK,EAAC,cAAc;iEAAUS,KAAA,CAAA4F,OAAO,CAACkB,SAAS,GAAAvE,MAAA;iDAAjBvC,KAAA,CAAA4F,OAAO,CAACkB,SAAS,E,KAEpE9F,mBAAA,CAGM,OAHN+F,WAGM,G,4BAFJ/F,mBAAA,CAAyC;IAAlCzB,KAAK,EAAC;EAAY,GAAC,SAAO,sB,gBACjCyB,mBAAA,CAAkE;IAA3DsB,IAAI,EAAC,MAAM;IAAC/C,KAAK,EAAC,cAAc;iEAAUS,KAAA,CAAA4F,OAAO,CAACoB,OAAO,GAAAzE,MAAA;iDAAfvC,KAAA,CAAA4F,OAAO,CAACoB,OAAO,E,KAElEhG,mBAAA,CASM,OATNiG,WASM,GARJjG,mBAAA,CAOM,OAPNkG,WAOM,GANJlG,mBAAA,CAES;IAFDzB,KAAK,EAAC,wBAAwB;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAA2F,YAAA,IAAA3F,QAAA,CAAA2F,YAAA,IAAA3E,IAAA,CAAY;kCACzDN,mBAAA,CAA6B;IAA1BzB,KAAK,EAAC;EAAe,2B,IAE1ByB,mBAAA,CAES;IAFDzB,KAAK,EAAC,kCAAkC;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAA6G,YAAA,IAAA7G,QAAA,CAAA6G,YAAA,IAAA7F,IAAA,CAAY;kCACnEN,mBAAA,CAA4B;IAAzBzB,KAAK,EAAC;EAAc,2B,mDAQnCwB,mBAAA,wBAA2B,EAChBf,KAAA,CAAAoH,gBAAgB,CAACC,MAAM,Q,cAAlC1H,mBAAA,CAyCM,OAzCN2H,WAyCM,GAxCJtG,mBAAA,CAKM,OALNuG,WAKM,GAJJvG,mBAAA,CAGK,MAHLwG,WAGK,G,4BAFHxG,mBAAA,CAAiC;IAA9BzB,KAAK,EAAC;EAAmB,6B,iBAAK,iBACnB,GAAA8C,gBAAA,CAAGrC,KAAA,CAAAoH,gBAAgB,CAACC,MAAM,IAAG,aAC7C,gB,KAEFrG,mBAAA,CAiCM,OAjCNyG,WAiCM,GAhCJzG,mBAAA,CA+BM,OA/BN0G,WA+BM,GA9BJ1G,mBAAA,CAQM,OARN2G,WAQM,G,4BAPJ3G,mBAAA,CAAwC;IAAjCzB,KAAK,EAAC;EAAY,GAAC,QAAM,sB,gBAChCyB,mBAAA,CAKS;IALDzB,KAAK,EAAC,aAAa;iEAAUS,KAAA,CAAA4H,UAAU,GAAArF,MAAA;kCAC7CvB,mBAAA,CAAuC;IAA/BoF,KAAK,EAAC;EAAE,GAAC,eAAa,uB,kBAC9BzG,mBAAA,CAESqC,SAAA,QAAAqE,WAAA,CAFgBrG,KAAA,CAAAsG,aAAa,EAAvBH,MAAM;yBAArBxG,mBAAA,CAES;MAFgCsC,GAAG,EAAEkE,MAAM,CAACI,EAAE;MAAGH,KAAK,EAAED,MAAM,CAACI;OAAI,aAChE,GAAAlE,gBAAA,CAAG/B,QAAA,CAAAmG,YAAY,CAACN,MAAM,CAACK,WAAW,yBAAAqB,WAAA;2EAHX7H,KAAA,CAAA4H,UAAU,E,KAOjD5G,mBAAA,CAQM,OARN8G,WAQM,G,4BAPJ9G,mBAAA,CAAmD;IAA5CzB,KAAK,EAAC;EAAY,GAAC,mBAAiB,sB,gBAC3CyB,mBAAA,CAKC;IAJCsB,IAAI,EAAC,MAAM;IACX/C,KAAK,EAAC,cAAc;iEACXS,KAAA,CAAA+H,UAAU,GAAAxF,MAAA;IACnBuD,WAAW,EAAC;iDADH9F,KAAA,CAAA+H,UAAU,E,KAIvB/G,mBAAA,CAWM,OAXNgH,WAWM,GAVJhH,mBAAA,CASM,OATNiH,WASM,GARJjH,mBAAA,CAGS;IAHDzB,KAAK,EAAC,iBAAiB;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAA4H,iBAAA,IAAA5H,QAAA,CAAA4H,iBAAA,IAAA5G,IAAA,CAAiB;IAAGkC,QAAQ,GAAGxD,KAAA,CAAA4H;kCACrE5G,mBAAA,CAAgC;IAA7BzB,KAAK,EAAC;EAAkB,4B,iBAAK,SAElC,E,gCACAyB,mBAAA,CAGS;IAHDzB,KAAK,EAAC,2BAA2B;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAkB,MAAA,IAAEvC,KAAA,CAAAoH,gBAAgB;kCAChEpG,mBAAA,CAAiC;IAA9BzB,KAAK,EAAC;EAAmB,4B,iBAAK,UAEnC,E,mDAOVwB,mBAAA,iBAAoB,EACpBC,mBAAA,CAmCM,OAnCNmH,WAmCM,GAlCJnH,mBAAA,CA0BM,OA1BNoH,WA0BM,GAzBJpH,mBAAA,CAUM,OAVNqH,WAUM,G,gBATJrH,mBAAA,CAAuH;IAAhHsB,IAAI,EAAC,OAAO;IAAC/C,KAAK,EAAC,WAAW;IAAC+I,IAAI,EAAC,UAAU;IAAC/B,EAAE,EAAC,UAAU;iEAAUvG,KAAA,CAAAuI,QAAQ,GAAAhG,MAAA;IAAE6D,KAAK,EAAC,MAAM;IAACoC,YAAY,EAAC;kDAApCxI,KAAA,CAAAuI,QAAQ,E,+BACrFvH,mBAAA,CAEQ;IAFDzB,KAAK,EAAC,gCAAgC;IAACkJ,GAAG,EAAC;MAChDzH,mBAAA,CAAoC;IAAjCzB,KAAK,EAAC;EAAsB,I,iBAAK,QACtC,E,sCAEAyB,mBAAA,CAAyH;IAAlHsB,IAAI,EAAC,OAAO;IAAC/C,KAAK,EAAC,WAAW;IAAC+I,IAAI,EAAC,UAAU;IAAC/B,EAAE,EAAC,WAAW;iEAAUvG,KAAA,CAAAuI,QAAQ,GAAAhG,MAAA;IAAE6D,KAAK,EAAC,OAAO;IAACoC,YAAY,EAAC;kDAArCxI,KAAA,CAAAuI,QAAQ,E,+BACtFvH,mBAAA,CAEQ;IAFDzB,KAAK,EAAC,gCAAgC;IAACkJ,GAAG,EAAC;MAChDzH,mBAAA,CAAiC;IAA9BzB,KAAK,EAAC;EAAmB,I,iBAAK,QACnC,E,wBAGFyB,mBAAA,CAYM,OAZN0H,WAYM,GAXJ1H,mBAAA,CAIO,QAJP2H,WAIO,EAJwB,WACrB,GAAAtG,gBAAA,EAAKrC,KAAA,CAAA4I,UAAU,CAACC,WAAW,QAAQ7I,KAAA,CAAA4I,UAAU,CAACE,YAAY,QAAQ,KAC1E,GAAAzG,gBAAA,CAAG0G,IAAI,CAACC,GAAG,CAAChJ,KAAA,CAAA4I,UAAU,CAACC,WAAW,GAAG7I,KAAA,CAAA4I,UAAU,CAACE,YAAY,EAAE9I,KAAA,CAAA4I,UAAU,CAACK,UAAU,KAAI,MACpF,GAAA5G,gBAAA,CAAGrC,KAAA,CAAA4I,UAAU,CAACK,UAAU,IAAG,YAChC,iB,gBACAjI,mBAAA,CAKS;IALDzB,KAAK,EAAC,4BAA4B;IAACC,KAAoB,EAApB;MAAA;IAAA,CAAoB;iEAAUQ,KAAA,CAAA4I,UAAU,CAACE,YAAY,GAAAvG,MAAA;IAAG2G,QAAM,EAAA7H,MAAA,SAAAA,MAAA,OAAAkB,MAAA,IAAEjC,QAAA,CAAA6I,kBAAkB,CAACnJ,KAAA,CAAA4I,UAAU,CAACE,YAAY;kCACnJ9H,mBAAA,CAAuC;IAA/BoF,KAAK,EAAC;EAAI,GAAC,aAAW,qBAC9BpF,mBAAA,CAAuC;IAA/BoF,KAAK,EAAC;EAAI,GAAC,aAAW,qBAC9BpF,mBAAA,CAAuC;IAA/BoF,KAAK,EAAC;EAAI,GAAC,aAAW,qBAC9BpF,mBAAA,CAAyC;IAAjCoF,KAAK,EAAC;EAAK,GAAC,cAAY,oB,2DAJuCpG,KAAA,CAAA4I,UAAU,CAACE,YAAY,E,OASpG9H,mBAAA,CAKM,OALNoI,WAKM,GAJ8EpJ,KAAA,CAAAqJ,QAAQ,CAAChC,MAAM,Q,cAAjG1H,mBAAA,CAGS;;IAHDJ,KAAK,EAAC,kCAAkC;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAAgJ,iBAAA,IAAAhJ,QAAA,CAAAgJ,iBAAA,IAAAhI,IAAA,CAAiB;kCACxEN,mBAAA,CAAwC;IAArCzB,KAAK,EAAC;EAA0B,6B,iBAAK,GACxC,GAAA8C,gBAAA,CAAGrC,KAAA,CAAAoH,gBAAgB,CAACC,MAAM,KAAKrH,KAAA,CAAAqJ,QAAQ,CAAChC,MAAM,iD,4CAKpDtG,mBAAA,eAAkB,EACPf,KAAA,CAAAuI,QAAQ,e,cAAnB5I,mBAAA,CA2IM,OA3IN4J,WA2IM,GA1IJxI,mBAAA,iBAAoB,EACTf,KAAA,CAAAqJ,QAAQ,CAAChC,MAAM,U,cAA1B1H,mBAAA,CAMM,OANN6J,WAMM,EAAAnI,MAAA,SAAAA,MAAA,QALJL,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAuB,IAChCyB,mBAAA,CAA6C;IAA1CzB,KAAK,EAAC;EAA+B,G,qBAE1CyB,mBAAA,CAA2D;IAAvDzB,KAAK,EAAC;EAAiB,GAAC,4BAA0B,qBACtDyB,mBAAA,CAAuF;IAApFzB,KAAK,EAAC;EAAY,GAAC,+DAA6D,oB,qBAIrFI,mBAAA,CA+HMqC,SAAA;IAAAC,GAAA;EAAA,IAhINlB,mBAAA,mBAAsB,EACtBC,mBAAA,CA+HM,OA/HNyI,WA+HM,I,kBA9HJ9J,mBAAA,CA6HMqC,SAAA,QAAAqE,WAAA,CA7HiBrG,KAAA,CAAAqJ,QAAQ,EAAnBK,OAAO;yBAAnB/J,mBAAA,CA6HM;MA7H4BsC,GAAG,EAAEyH,OAAO,CAACnD,EAAE;MAAEhH,KAAK,EAAC;QACvDyB,mBAAA,CA2HM;MA3HDzB,KAAK,EAAA0B,eAAA,EAAC,cAAc;QAAA,YAAuBjB,KAAA,CAAAoH,gBAAgB,CAACuC,QAAQ,CAACD,OAAO,CAACnD,EAAE;MAAA;QAClFxF,mBAAA,iBAAoB,EACpBC,mBAAA,CAuCM,OAvCN4I,WAuCM,GAtCJ5I,mBAAA,CAqCM,OArCN6I,WAqCM,GApCJ7I,mBAAA,CAUM,OAVN8I,WAUM,GATJ9I,mBAAA,CAKC;MAJCsB,IAAI,EAAC,UAAU;MACf/C,KAAK,EAAC,kBAAkB;MACvBwK,OAAO,EAAE/J,KAAA,CAAAoH,gBAAgB,CAACuC,QAAQ,CAACD,OAAO,CAACnD,EAAE;MAC7C2C,QAAM,EAAA3G,MAAA,IAAEjC,QAAA,CAAA0J,sBAAsB,CAACN,OAAO,CAACnD,EAAE;2DAE5CvF,mBAAA,CAEM,OAFNiJ,WAEM,GADJjJ,mBAAA,CAAkE,QAAlEkJ,WAAkE,EAAA7H,gBAAA,CAAhCqH,OAAO,CAACS,cAAc,iB,KAG5DnJ,mBAAA,CAwBM,OAxBNoJ,WAwBM,GAvBJpJ,mBAAA,CAsBM,OAtBNqJ,WAsBM,G,4BArBJrJ,mBAAA,CAES;MAFDzB,KAAK,EAAC,sCAAsC;MAAC+C,IAAI,EAAC,QAAQ;MAAC,gBAAc,EAAC,UAAU;MAAC,eAAa,EAAC;QACzGtB,mBAAA,CAAiC;MAA9BzB,KAAK,EAAC;IAAmB,G,sBAE9ByB,mBAAA,CAiBK,MAjBLsJ,WAiBK,GAhBHtJ,mBAAA,CAIK,aAHHA,mBAAA,CAEI;MAFDzB,KAAK,EAAC,eAAe;MAACgL,IAAI,EAAC,GAAG;MAAEnJ,OAAK,EAAAoJ,cAAA,CAAAjI,MAAA,IAAUjC,QAAA,CAAAmK,kBAAkB,CAACf,OAAO,CAACnD,EAAE;yCAC7EvF,mBAAA,CAA+B;MAA5BzB,KAAK,EAAC;IAAiB,4B,iBAAK,eACjC,E,gEAEFyB,mBAAA,CAAsC,aAAlCA,mBAAA,CAA6B;MAAzBzB,KAAK,EAAC;IAAkB,G,sBAChCyB,mBAAA,CAIK,aAHHA,mBAAA,CAEI;MAFDzB,KAAK,EAAC,4BAA4B;MAACgL,IAAI,EAAC,GAAG;MAAEnJ,OAAK,EAAAoJ,cAAA,CAAAjI,MAAA,IAAUjC,QAAA,CAAAoK,cAAc,CAAChB,OAAO,CAACnD,EAAE;yCACtFvF,mBAAA,CAAiC;MAA9BzB,KAAK,EAAC;IAAmB,4B,iBAAK,UACnC,E,oCAEFyB,mBAAA,CAIK,aAHHA,mBAAA,CAEI;MAFDzB,KAAK,EAAC,2BAA2B;MAACgL,IAAI,EAAC,GAAG;MAAEnJ,OAAK,EAAAoJ,cAAA,CAAAjI,MAAA,IAAUjC,QAAA,CAAAqK,aAAa,CAACjB,OAAO,CAACnD,EAAE,EAAEqE,IAAA,CAAAC,MAAM;yCAC5F7J,mBAAA,CAAiC;MAA9BzB,KAAK,EAAC;IAAmB,4B,iBAAK,SACnC,E,8CAQZwB,mBAAA,eAAkB,EAClBC,mBAAA,CAmDM,OAnDN8J,WAmDM,GAlDJ/J,mBAAA,iBAAoB,EACpBC,mBAAA,CAUM,OAVN+J,WAUM,GATJ/J,mBAAA,CAQM,OARNgK,WAQM,G,4BAPJhK,mBAAA,CAEM;MAFDzB,KAAK,EAAC;IAAe,IACxByB,mBAAA,CAAqD;MAAlDzB,KAAK,EAAC;IAAuC,G,sBAElDyB,mBAAA,CAGM,cAFJA,mBAAA,CAAuD,MAAvDiK,WAAuD,EAAA5I,gBAAA,CAA3BqH,OAAO,CAACwB,WAAW,kBAC/ClK,mBAAA,CAA4D,SAA5DmK,WAA4D,EAAA9I,gBAAA,CAA/BqH,OAAO,CAAC0B,YAAY,iB,OAKvDrK,mBAAA,mBAAsB,EACtBC,mBAAA,CAOM,OAPNqK,WAOM,GANJrK,mBAAA,CAKM,OALNsK,WAKM,G,4BAJJtK,mBAAA,CAAyC;MAAtCzB,KAAK,EAAC;IAA2B,6BACpCyB,mBAAA,CAEO,QAFPuK,WAEO,EAAAlJ,gBAAA,CADFqH,OAAO,CAAC9C,aAAa,iB,KAK9B7F,mBAAA,uBAA0B,EAC1BC,mBAAA,CAiBM,OAjBNwK,WAiBM,GAhBJxK,mBAAA,CAeM,OAfNyK,WAeM,GAdJzK,mBAAA,CAOM,OAPN0K,WAOM,GANJ1K,mBAAA,CAKM,OALN2K,WAKM,G,4BAJJ3K,mBAAA,CAAgD;MAAzCzB,KAAK,EAAC;IAAoB,GAAC,QAAM,sBACxCyB,mBAAA,CAEO;MAFDzB,KAAK,EAAA0B,eAAA,EAAC,OAAO,QAAeX,QAAA,CAAAsL,cAAc,CAAClC,OAAO,CAAClD,WAAW;wBAC/DlG,QAAA,CAAAmG,YAAY,CAACiD,OAAO,CAAClD,WAAW,yB,KAIzCxF,mBAAA,CAKM,OALN6K,WAKM,GAJJ7K,mBAAA,CAGM,OAHN8K,WAGM,G,4BAFJ9K,mBAAA,CAAgD;MAAzCzB,KAAK,EAAC;IAAoB,GAAC,QAAM,sBACxCyB,mBAAA,CAAiF,QAAjF+K,YAAiF,EAAA1J,gBAAA,CAA3C/B,QAAA,CAAA0L,cAAc,CAACtC,OAAO,CAACuC,SAAS,kB,SAM9ElL,mBAAA,UAAa,EACbC,mBAAA,CAKM,OALNkL,YAKM,GAJJlL,mBAAA,CAGQ,SAHRmL,YAGQ,G,4BAFNnL,mBAAA,CAAwC;MAArCzB,KAAK,EAAC;IAA0B,6B,iBAAK,aAC9B,GAAA8C,gBAAA,CAAG/B,QAAA,CAAA8L,UAAU,CAAC1C,OAAO,CAAC2C,YAAY,kB,OAKlDtL,mBAAA,iBAAoB,EACpBC,mBAAA,CAwBM,OAxBNsL,YAwBM,GAvBJtL,mBAAA,CAsBM,OAtBNuL,YAsBM,GArBJvL,mBAAA,CAES;MAFDzB,KAAK,EAAC,0CAA0C;MAAE6B,OAAK,EAAAmB,MAAA,IAAEjC,QAAA,CAAAmK,kBAAkB,CAACf,OAAO,CAACnD,EAAE;yCAC5FvF,mBAAA,CAA+B;MAA5BzB,KAAK,EAAC;IAAiB,4B,iBAAK,OACjC,E,mCAEQe,QAAA,CAAAkM,UAAU,CAAC9C,OAAO,K,cAD1B/J,mBAAA,CAQS;;MANPJ,KAAK,EAAC,wBAAwB;MAC7B6B,OAAK,EAAAmB,MAAA,IAAEjC,QAAA,CAAAmM,YAAY,CAAC/C,OAAO;MAC3BrG,KAAK,EAAE,iBAAiB;MACxBG,QAAQ,EAAExD,KAAA,CAAA8B;yCAEXd,mBAAA,CAA4B;MAAzBzB,KAAK,EAAC;IAAc,2B,wEAGjBe,QAAA,CAAAoM,SAAS,CAAChD,OAAO,K,cADzB/J,mBAAA,CAQS;;MANPJ,KAAK,EAAC,uBAAuB;MAC5B6B,OAAK,EAAAmB,MAAA,IAAEjC,QAAA,CAAAqM,oBAAoB,CAACjD,OAAO;MACnCrG,KAAK,EAAE,gBAAgB;MACvBG,QAAQ,EAAExD,KAAA,CAAA8B;yCAEXd,mBAAA,CAA4B;MAAzBzB,KAAK,EAAC;IAAc,2B;yGAUrCI,mBAAA,CAkJMqC,SAAA;IAAAC,GAAA;EAAA,IAnJNlB,mBAAA,gBAAmB,EACnBC,mBAAA,CAkJM,OAlJN4L,YAkJM,GAjJJ7L,mBAAA,kBAAqB,EACrBC,mBAAA,CAaM,OAbN6L,YAaM,GAZJ7L,mBAAA,CAWM,OAXN8L,YAWM,G,4BAVJ9L,mBAAA,CAGK;IAHDzB,KAAK,EAAC;EAAwB,IAChCyB,mBAAA,CAAiD;IAA9CzB,KAAK,EAAC;EAAmC,I,iBAAK,qBAEnD,E,sBACAyB,mBAAA,CAKM,OALN+L,YAKM,GAJ8E/M,KAAA,CAAAqJ,QAAQ,CAAChC,MAAM,Q,cAAjG1H,mBAAA,CAGS;;IAHDJ,KAAK,EAAC,kCAAkC;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAAgJ,iBAAA,IAAAhJ,QAAA,CAAAgJ,iBAAA,IAAAhI,IAAA,CAAiB;kCACxEN,mBAAA,CAAwC;IAArCzB,KAAK,EAAC;EAA0B,6B,iBAAK,GACxC,GAAA8C,gBAAA,CAAGrC,KAAA,CAAAoH,gBAAgB,CAACC,MAAM,KAAKrH,KAAA,CAAAqJ,QAAQ,CAAChC,MAAM,iD,8CAMtDtG,mBAAA,iBAAoB,EACTf,KAAA,CAAAqJ,QAAQ,CAAChC,MAAM,U,cAA1B1H,mBAAA,CAQM,OARNqN,YAQM,EAAA3L,MAAA,SAAAA,MAAA,Q,kXAGN1B,mBAAA,CAoHMqC,SAAA;IAAAC,GAAA;EAAA,IArHNlB,mBAAA,kBAAqB,EACrBC,mBAAA,CAoHM,OApHNiM,YAoHM,GAnHJlM,mBAAA,eAAkB,G,kBAClBpB,mBAAA,CAiHMqC,SAAA,QAAAqE,WAAA,CAjHiBrG,KAAA,CAAAqJ,QAAQ,EAAnBK,OAAO;yBAAnB/J,mBAAA,CAiHM;MAjH4BsC,GAAG,EAAEyH,OAAO,CAACnD,EAAE;MAAEhH,KAAK,EAAA0B,eAAA,EAAC,WAAW;QAAA,YAAuBjB,KAAA,CAAAoH,gBAAgB,CAACuC,QAAQ,CAACD,OAAO,CAACnD,EAAE;MAAA;QAC7HxF,mBAAA,sBAAyB,EACzBC,mBAAA,CAOM,OAPNkM,YAOM,GANJlM,mBAAA,CAKC;MAJCsB,IAAI,EAAC,UAAU;MACf/C,KAAK,EAAC,kBAAkB;MACvBwK,OAAO,EAAE/J,KAAA,CAAAoH,gBAAgB,CAACuC,QAAQ,CAACD,OAAO,CAACnD,EAAE;MAC7C2C,QAAM,EAAA3G,MAAA,IAAEjC,QAAA,CAAA0J,sBAAsB,CAACN,OAAO,CAACnD,EAAE;8DAI9CxF,mBAAA,2BAA8B,EAC9BC,mBAAA,CAKM,OALNmM,YAKM,GAJJnM,mBAAA,CAGM,OAHNoM,YAGM,GAFJpM,mBAAA,CAAgE,QAAhEqM,YAAgE,EAAAhL,gBAAA,CAAhCqH,OAAO,CAACS,cAAc,kBACtDnJ,mBAAA,CAAoD,QAApDsM,YAAoD,EAA3B,MAAI,GAAAjL,gBAAA,CAAGqH,OAAO,CAACnD,EAAE,iB,KAI9CxF,mBAAA,mBAAsB,EACtBC,mBAAA,CAUM,OAVNuM,YAUM,GATJvM,mBAAA,CAQM,OARNwM,YAQM,G,4BAPJxM,mBAAA,CAEM;MAFDzB,KAAK,EAAC;IAAkB,IAC3ByB,mBAAA,CAA2B;MAAxBzB,KAAK,EAAC;IAAa,G,sBAExByB,mBAAA,CAGM,OAHNyM,YAGM,GAFJzM,mBAAA,CAAwD,OAAxD0M,YAAwD,EAAArL,gBAAA,CAA5BqH,OAAO,CAACwB,WAAW,kBAC/ClK,mBAAA,CAA0D,OAA1D2M,YAA0D,EAAAtL,gBAAA,CAA7BqH,OAAO,CAAC0B,YAAY,iB,OAKvDrK,mBAAA,0BAA6B,EAC7BC,mBAAA,CAKM,OALN4M,YAKM,GAJJ5M,mBAAA,CAGM,OAHN6M,YAGM,G,4BAFJ7M,mBAAA,CAAoC;MAAjCzB,KAAK,EAAC;IAAsB,6BAC/ByB,mBAAA,CAAwC,cAAAqB,gBAAA,CAA/BqH,OAAO,CAAC9C,aAAa,iB,KAIlC7F,mBAAA,mBAAsB,EACtBC,mBAAA,CAKM,OALN8M,YAKM,GAJJ9M,mBAAA,CAGO;MAHDzB,KAAK,EAAA0B,eAAA,EAAC,cAAc,YAAmBX,QAAA,CAAAsL,cAAc,CAAClC,OAAO,CAAClD,WAAW;oCAC7ExF,mBAAA,CAA8C;MAA3CzB,KAAK,EAAC;IAAgC,6B,iBAAK,GAC9C,GAAA8C,gBAAA,CAAG/B,QAAA,CAAAmG,YAAY,CAACiD,OAAO,CAAClD,WAAW,kB,oBAIvCzF,mBAAA,mBAAsB,EACtBC,mBAAA,CAKM,OALN+M,YAKM,GAJJ/M,mBAAA,CAGM,OAHNgN,YAGM,GAFJhN,mBAAA,CAAmE,QAAnEiN,YAAmE,EAAA5L,gBAAA,CAA3C/B,QAAA,CAAA0L,cAAc,CAACtC,OAAO,CAACuC,SAAS,mB,4BACxDjL,mBAAA,CAAiC;MAA3BzB,KAAK,EAAC;IAAU,GAAC,KAAG,qB,KAI9BwB,mBAAA,iBAAoB,EACpBC,mBAAA,CAKM,OALNkN,YAKM,GAJJlN,mBAAA,CAGM,OAHNmN,YAGM,GAFJnN,mBAAA,CAAgE,QAAhEoN,YAAgE,EAAA/L,gBAAA,CAA1C/B,QAAA,CAAA8L,UAAU,CAAC1C,OAAO,CAAC2C,YAAY,mBACrDrL,mBAAA,CAAgE,QAAhEqN,YAAgE,EAAAhM,gBAAA,CAA1C/B,QAAA,CAAAwC,UAAU,CAAC4G,OAAO,CAAC2C,YAAY,kB,KAIzDtL,mBAAA,oBAAuB,EACvBC,mBAAA,CA+CM,OA/CNsN,YA+CM,GA9CJtN,mBAAA,CA6CM,OA7CNuN,YA6CM,GA5CJvN,mBAAA,CAES;MAFDzB,KAAK,EAAC,qBAAqB;MAAE6B,OAAK,EAAAmB,MAAA,IAAEjC,QAAA,CAAAmK,kBAAkB,CAACf,OAAO,CAACnD,EAAE;MAAGlD,KAAK,EAAC;yCAChFrC,mBAAA,CAA0B;MAAvBzB,KAAK,EAAC;IAAY,2B,mCAGfe,QAAA,CAAAkM,UAAU,CAAC9C,OAAO,K,cAD1B/J,mBAAA,CAQS;;MANPJ,KAAK,EAAC,wBAAwB;MAC7B6B,OAAK,EAAAmB,MAAA,IAAEjC,QAAA,CAAAmM,YAAY,CAAC/C,OAAO;MAC5BrG,KAAK,EAAC,SAAS;MACdG,QAAQ,EAAExD,KAAA,CAAA8B;yCAEXd,mBAAA,CAA4B;MAAzBzB,KAAK,EAAC;IAAc,2B,wEAGjBe,QAAA,CAAAoM,SAAS,CAAChD,OAAO,K,cADzB/J,mBAAA,CAQS;;MANPJ,KAAK,EAAC,uBAAuB;MAC5B6B,OAAK,EAAAmB,MAAA,IAAEjC,QAAA,CAAAqM,oBAAoB,CAACjD,OAAO;MACpCrG,KAAK,EAAC,QAAQ;MACbG,QAAQ,EAAExD,KAAA,CAAA8B;yCAEXd,mBAAA,CAA4B;MAAzBzB,KAAK,EAAC;IAAc,2B,wEAEzByB,mBAAA,CAsBM,OAtBNwN,YAsBM,G,8BArBJxN,mBAAA,CAES;MAFDzB,KAAK,EAAC,qCAAqC;MAAC+C,IAAI,EAAC,QAAQ;MAAC,gBAAc,EAAC,UAAU;MAAC,eAAa,EAAC,OAAO;MAACe,KAAK,EAAC;QACtHrC,mBAAA,CAAiC;MAA9BzB,KAAK,EAAC;IAAmB,G,sBAE9ByB,mBAAA,CAiBK,MAjBLyN,YAiBK,GAhBHzN,mBAAA,CAIK,aAHHA,mBAAA,CAEI;MAFDzB,KAAK,EAAC,eAAe;MAACgL,IAAI,EAAC,GAAG;MAAEnJ,OAAK,EAAAoJ,cAAA,CAAAjI,MAAA,IAAUjC,QAAA,CAAAmK,kBAAkB,CAACf,OAAO,CAACnD,EAAE;2CAC7EvF,mBAAA,CAA+B;MAA5BzB,KAAK,EAAC;IAAiB,4B,iBAAK,eACjC,E,mEAEFyB,mBAAA,CAAsC,aAAlCA,mBAAA,CAA6B;MAAzBzB,KAAK,EAAC;IAAkB,G,sBAChCyB,mBAAA,CAIK,aAHHA,mBAAA,CAEI;MAFDzB,KAAK,EAAC,4BAA4B;MAACgL,IAAI,EAAC,GAAG;MAAEnJ,OAAK,EAAAoJ,cAAA,CAAAjI,MAAA,IAAUjC,QAAA,CAAAoK,cAAc,CAAChB,OAAO,CAACnD,EAAE;2CACtFvF,mBAAA,CAAiC;MAA9BzB,KAAK,EAAC;IAAmB,4B,iBAAK,UACnC,E,qCAEFyB,mBAAA,CAIK,aAHHA,mBAAA,CAEI;MAFDzB,KAAK,EAAC,2BAA2B;MAACgL,IAAI,EAAC,GAAG;MAAEnJ,OAAK,EAAAoJ,cAAA,CAAAjI,MAAA,IAAUjC,QAAA,CAAAqK,aAAa,CAACjB,OAAO,CAACnD,EAAE,EAAEqE,IAAA,CAAAC,MAAM;2CAC5F7J,mBAAA,CAAiC;MAA9BzB,KAAK,EAAC;IAAmB,4B,iBAAK,SACnC,E;0IAUhBwB,mBAAA,gBAAmB,EACRf,KAAA,CAAA4I,UAAU,CAAC8F,UAAU,Q,cAAhC/O,mBAAA,CAuBQ,OAvBRgP,YAuBQ,GAtBJ3N,mBAAA,CAqBM,OArBN4N,YAqBM,GApBJ5N,mBAAA,CAmBK,MAnBL6N,YAmBK,GAlBH7N,mBAAA,CAIK;IAJDzB,KAAK,EAAA0B,eAAA,EAAC,WAAW;MAAAuC,QAAA,EAAqBxD,KAAA,CAAA4I,UAAU,CAACC,WAAW;IAAA;MAC9D7H,mBAAA,CAEI;IAFDzB,KAAK,EAAC,WAAW;IAACgL,IAAI,EAAC,GAAG;IAAEnJ,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAmJ,cAAA,CAAAjI,MAAA,IAAUjC,QAAA,CAAAwO,UAAU,CAAC9O,KAAA,CAAA4I,UAAU,CAACC,WAAW;oCAC7E7H,mBAAA,CAAmC;IAAhCzB,KAAK,EAAC;EAAqB,2B,wCAGlCI,mBAAA,CAOKqC,SAAA,QAAAqE,WAAA,CANY0C,IAAI,CAACC,GAAG,CAAChJ,KAAA,CAAA4I,UAAU,CAAC8F,UAAU,OAAtCK,IAAI;yBADbpP,mBAAA,CAOK;MALFsC,GAAG,EAAE8M,IAAI;MACVxP,KAAK,EAAA0B,eAAA,EAAC,WAAW;QAAAC,MAAA,EACC6N,IAAI,KAAK/O,KAAA,CAAA4I,UAAU,CAACC;MAAW;QAEjD7H,mBAAA,CAA8E;MAA3EzB,KAAK,EAAC,WAAW;MAACgL,IAAI,EAAC,GAAG;MAAEnJ,OAAK,EAAAoJ,cAAA,CAAAjI,MAAA,IAAUjC,QAAA,CAAAwO,UAAU,CAACC,IAAI;wBAAMA,IAAI,wBAAAC,YAAA,E;kCAEzEhO,mBAAA,CAIK;IAJDzB,KAAK,EAAA0B,eAAA,EAAC,WAAW;MAAAuC,QAAA,EAAqBxD,KAAA,CAAA4I,UAAU,CAACC,WAAW,KAAK7I,KAAA,CAAA4I,UAAU,CAAC8F;IAAU;MACxF1N,mBAAA,CAEI;IAFDzB,KAAK,EAAC,WAAW;IAACgL,IAAI,EAAC,GAAG;IAAEnJ,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAmJ,cAAA,CAAAjI,MAAA,IAAUjC,QAAA,CAAAwO,UAAU,CAAC9O,KAAA,CAAA4I,UAAU,CAACC,WAAW;oCAC7E7H,mBAAA,CAAoC;IAAjCzB,KAAK,EAAC;EAAsB,2B,oHAQ3CwB,mBAAA,2BAA8B,EACnBf,KAAA,CAAAiP,kBAAkB,IAAIjP,KAAA,CAAAkP,cAAc,I,cAA/CvP,mBAAA,CAuWM,OAvWNwP,YAuWM,GAtWJnO,mBAAA,CAqWM,OArWNoO,YAqWM,GApWJpO,mBAAA,CAmWM,OAnWNqO,YAmWM,GAlWJrO,mBAAA,CAMM,OANNsO,YAMM,GALJtO,mBAAA,CAGK,MAHLuO,YAGK,G,8BAFHvO,mBAAA,CAAoC;IAAjCzB,KAAK,EAAC;EAAsB,6B,iBAAK,qBAClB,GAAA8C,gBAAA,CAAGrC,KAAA,CAAAkP,cAAc,CAAC/E,cAAc,iB,GAEpDnJ,mBAAA,CAAqF;IAA7EsB,IAAI,EAAC,QAAQ;IAAC/C,KAAK,EAAC,WAAW;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAkB,MAAA,IAAEvC,KAAA,CAAAiP,kBAAkB;QAEpEjO,mBAAA,CAgVM,OAhVNwO,YAgVM,GA/UJxO,mBAAA,CAkSM,OAlSNyO,YAkSM,GAjSJ1O,mBAAA,uCAA0C,EAC1CC,mBAAA,CAwKM,OAxKN0O,YAwKM,GAvKJ3O,mBAAA,uBAA0B,EAC1BC,mBAAA,CAsDM,OAtDN2O,YAsDM,G,8BArDJ3O,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAa,IACtByB,mBAAA,CAAgF;IAA5EzB,KAAK,EAAC;EAAM,IAACyB,mBAAA,CAAuC;IAApCzB,KAAK,EAAC;EAAyB,I,iBAAK,qBAAmB,E,wBAE7EyB,mBAAA,CAiDM,OAjDN4O,YAiDM,GAhDJ5O,mBAAA,CA+CM,OA/CN6O,YA+CM,GA9CJ7O,mBAAA,CAmBM,OAnBN8O,YAmBM,GAlBJ9O,mBAAA,CAGM,OAHN+O,YAGM,G,8BAFJ/O,mBAAA,CAAwD;IAAjDzB,KAAK,EAAC;EAAoB,GAAC,gBAAc,sBAChDyB,mBAAA,CAAuD,KAAvDgP,YAAuD,EAAA3N,gBAAA,CAApCrC,KAAA,CAAAkP,cAAc,CAAC/E,cAAc,iB,GAElDnJ,mBAAA,CAKM,OALNiP,YAKM,G,8BAJJjP,mBAAA,CAAuD;IAAhDzB,KAAK,EAAC;EAAoB,GAAC,eAAa,sBAC/CyB,mBAAA,CAEI,KAFJkP,YAEI,GADFlP,mBAAA,CAAqE,QAArEmP,YAAqE,EAAA9N,gBAAA,CAAtCrC,KAAA,CAAAkP,cAAc,CAACtI,aAAa,iB,KAG/D5F,mBAAA,CAGM,OAHNoP,YAGM,G,8BAFJpP,mBAAA,CAA0D;IAAnDzB,KAAK,EAAC;EAAoB,GAAC,kBAAgB,sBAClDyB,mBAAA,CAAyD,KAAzDqP,YAAyD,EAAAhO,gBAAA,CAAtCrC,KAAA,CAAAkP,cAAc,CAACoB,gBAAgB,iB,GAEpDtP,mBAAA,CAGM,OAHNuP,YAGM,G,8BAFJvP,mBAAA,CAAyD;IAAlDzB,KAAK,EAAC;EAAoB,GAAC,iBAAe,sBACjDyB,mBAAA,CAA2E,KAA3EwP,YAA2E,EAAAnO,gBAAA,CAAxDrC,KAAA,CAAAkP,cAAc,CAACuB,eAAe,oC,KAGrDzP,mBAAA,CAyBM,OAzBN0P,YAyBM,GAxBJ1P,mBAAA,CAOM,OAPN2P,YAOM,G,8BANJ3P,mBAAA,CAAwD;IAAjDzB,KAAK,EAAC;EAAoB,GAAC,gBAAc,sBAChDyB,mBAAA,CAII,KAJJ4P,YAII,GAHF5P,mBAAA,CAEO;IAFDzB,KAAK,EAAA0B,eAAA,EAAC,OAAO,QAAeX,QAAA,CAAAsL,cAAc,CAAC5L,KAAA,CAAAkP,cAAc,CAAC1I,WAAW;sBACtElG,QAAA,CAAAmG,YAAY,CAACzG,KAAA,CAAAkP,cAAc,CAAC1I,WAAW,yB,KAIhDxF,mBAAA,CAOM,OAPN6P,YAOM,G,8BANJ7P,mBAAA,CAAkD;IAA3CzB,KAAK,EAAC;EAAoB,GAAC,UAAQ,sBAC1CyB,mBAAA,CAII,KAJJ8P,YAII,GAHF9P,mBAAA,CAEO;IAFDzB,KAAK,EAAA0B,eAAA,EAAC,OAAO,EAASjB,KAAA,CAAAkP,cAAc,CAAC6B,QAAQ,4BAA4B/Q,KAAA,CAAAkP,cAAc,CAAC6B,QAAQ;sBACjG/Q,KAAA,CAAAkP,cAAc,CAAC6B,QAAQ,oC,KAIhC/P,mBAAA,CAGM,OAHNgQ,YAGM,G,8BAFJhQ,mBAAA,CAAyD;IAAlDzB,KAAK,EAAC;EAAoB,GAAC,iBAAe,sBACjDyB,mBAAA,CAAoE,KAApEiQ,YAAoE,EAAA5O,gBAAA,CAAjDrC,KAAA,CAAAkP,cAAc,CAACgC,eAAe,6B,GAEnDlQ,mBAAA,CAGM,OAHNmQ,YAGM,G,8BAFJnQ,mBAAA,CAAwD;IAAjDzB,KAAK,EAAC;EAAoB,GAAC,gBAAc,sBAChDyB,mBAAA,CAAqE,KAArEoQ,YAAqE,EAAA/O,gBAAA,CAAlD/B,QAAA,CAAA+Q,cAAc,CAACrR,KAAA,CAAAkP,cAAc,CAAC7C,YAAY,kB,WAOvEtL,mBAAA,wBAA2B,EAC3BC,mBAAA,CAgCM,OAhCNsQ,YAgCM,G,8BA/BJtQ,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAa,IACtByB,mBAAA,CAAwE;IAApEzB,KAAK,EAAC;EAAM,IAACyB,mBAAA,CAAgC;IAA7BzB,KAAK,EAAC;EAAkB,I,iBAAK,oBAAkB,E,wBAErEyB,mBAAA,CA2BM,OA3BNuQ,YA2BM,GA1BJvQ,mBAAA,CAyBM,OAzBNwQ,YAyBM,GAxBJxQ,mBAAA,CAWM,OAXNyQ,YAWM,GAVJzQ,mBAAA,CAGM,OAHN0Q,YAGM,G,8BAFJ1Q,mBAAA,CAAmD;IAA5CzB,KAAK,EAAC;EAAoB,GAAC,WAAS,sBAC3CyB,mBAAA,CAAoD,KAApD2Q,YAAoD,EAAAtP,gBAAA,CAAjCrC,KAAA,CAAAkP,cAAc,CAAChE,WAAW,iB,GAE/ClK,mBAAA,CAKM,OALN4Q,YAKM,G,8BAJJ5Q,mBAAA,CAAuD;IAAhDzB,KAAK,EAAC;EAAoB,GAAC,eAAa,sBAC/CyB,mBAAA,CAEI,KAFJ6Q,YAEI,GADF7Q,mBAAA,CAAwF;IAApFuJ,IAAI,YAAYvK,KAAA,CAAAkP,cAAc,CAAC9D,YAAY;sBAAOpL,KAAA,CAAAkP,cAAc,CAAC9D,YAAY,wBAAA0G,YAAA,E,OAIvF9Q,mBAAA,CAWM,OAXN+Q,YAWM,GAVJ/Q,mBAAA,CAKM,OALNgR,YAKM,G,8BAJJhR,mBAAA,CAAsD;IAA/CzB,KAAK,EAAC;EAAoB,GAAC,cAAY,sBAC9CyB,mBAAA,CAEI,KAFJiR,YAEI,GADFjR,mBAAA,CAAuG;IAAnGuJ,IAAI,SAASvK,KAAA,CAAAkP,cAAc,CAACgD,YAAY;sBAAOlS,KAAA,CAAAkP,cAAc,CAACgD,YAAY,0CAAAC,YAAA,E,KAGlFnR,mBAAA,CAGM,OAHNoR,YAGM,G,8BAFJpR,mBAAA,CAAiD;IAA1CzB,KAAK,EAAC;EAAoB,GAAC,SAAO,sBACzCyB,mBAAA,CAAyE,KAAzEqR,YAAyE,EAAAhQ,gBAAA,CAAtDrC,KAAA,CAAAkP,cAAc,CAACoD,cAAc,mC,WAO1DvR,mBAAA,+BAAkC,EACvBf,KAAA,CAAAkP,cAAc,CAACqD,gBAAgB,I,cAA1C5S,mBAAA,CAyEM,OAzEN6S,YAyEM,G,8BAxEJxR,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAa,IACtByB,mBAAA,CAA6F;IAAzFzB,KAAK,EAAC;EAAM,IAACyB,mBAAA,CAA0C;IAAvCzB,KAAK,EAAC;EAA4B,I,iBAAK,+BAA6B,E,wBAE1FyB,mBAAA,CAoEM,OApENyR,YAoEM,GAnEJ1R,mBAAA,gCAAmC,EACxBf,KAAA,CAAAkP,cAAc,CAACtI,aAAa,6B,cAAvCjH,mBAAA,CAuBM,OAAA+S,YAAA,GAtBJ1R,mBAAA,CAqBM,OArBN2R,YAqBM,GApBJ3R,mBAAA,CASM,OATN4R,YASM,GARJ5R,mBAAA,CAGM,OAHN6R,YAGM,G,8BAFJ7R,mBAAA,CAA0D;IAAnDzB,KAAK,EAAC;EAAoB,GAAC,kBAAgB,sBAClDyB,mBAAA,CAA6F,KAA7F8R,YAA6F,EAAAzQ,gBAAA,CAA1ErC,KAAA,CAAAkP,cAAc,CAACqD,gBAAgB,CAACQ,gBAAgB,oC,GAErE/R,mBAAA,CAGM,OAHNgS,YAGM,G,8BAFJhS,mBAAA,CAAsD;IAA/CzB,KAAK,EAAC;EAAoB,GAAC,cAAY,sBAC9CyB,mBAAA,CAAyF,KAAzFiS,YAAyF,EAAA5Q,gBAAA,CAAtErC,KAAA,CAAAkP,cAAc,CAACqD,gBAAgB,CAACW,YAAY,oC,KAGnElS,mBAAA,CASM,OATNmS,YASM,GARJnS,mBAAA,CAGM,OAHNoS,YAGM,G,8BAFJpS,mBAAA,CAAoD;IAA7CzB,KAAK,EAAC;EAAoB,GAAC,YAAU,sBAC5CyB,mBAAA,CAAuF,KAAvFqS,YAAuF,EAAAhR,gBAAA,CAApErC,KAAA,CAAAkP,cAAc,CAACqD,gBAAgB,CAACe,UAAU,oC,GAE/DtS,mBAAA,CAGM,OAHNuS,YAGM,G,8BAFJvS,mBAAA,CAAwD;IAAjDzB,KAAK,EAAC;EAAoB,GAAC,gBAAc,sBAChDyB,mBAAA,CAA2G,KAA3GwS,YAA2G,EAAAnR,gBAAA,CAAxF/B,QAAA,CAAA0L,cAAc,CAAChM,KAAA,CAAAkP,cAAc,CAACqD,gBAAgB,CAACkB,cAAc,qC,WAOxEzT,KAAA,CAAAkP,cAAc,CAACtI,aAAa,iB,cAA5CjH,mBAAA,CAuCMqC,SAAA;IAAAC,GAAA;EAAA,IAxCNlB,mBAAA,oBAAuB,EACvBC,mBAAA,CAuCM,cAtCJA,mBAAA,CAqCM,OArCN0S,YAqCM,GApCJ1S,mBAAA,CAiBM,OAjBN2S,YAiBM,GAhBJ3S,mBAAA,CAGM,OAHN4S,YAGM,G,8BAFJ5S,mBAAA,CAAoD;IAA7CzB,KAAK,EAAC;EAAoB,GAAC,YAAU,sBAC5CyB,mBAAA,CAAmG,KAAnG6S,YAAmG,EAAAxR,gBAAA,CAAhF/B,QAAA,CAAA8L,UAAU,CAACpM,KAAA,CAAAkP,cAAc,CAACqD,gBAAgB,CAACuB,UAAU,qC,GAE1E9S,mBAAA,CAGM,OAHN+S,YAGM,G,8BAFJ/S,mBAAA,CAAqD;IAA9CzB,KAAK,EAAC;EAAoB,GAAC,aAAW,sBAC7CyB,mBAAA,CAAwF,KAAxFgT,YAAwF,EAAA3R,gBAAA,CAArErC,KAAA,CAAAkP,cAAc,CAACqD,gBAAgB,CAAC0B,WAAW,oC,GAEhEjT,mBAAA,CAGM,OAHNkT,YAGM,G,8BAFJlT,mBAAA,CAAsD;IAA/CzB,KAAK,EAAC;EAAoB,GAAC,cAAY,sBAC9CyB,mBAAA,CAAyF,KAAzFmT,YAAyF,EAAA9R,gBAAA,CAAtErC,KAAA,CAAAkP,cAAc,CAACqD,gBAAgB,CAACW,YAAY,oC,GAEjElS,mBAAA,CAGM,OAHNoT,YAGM,G,8BAFJpT,mBAAA,CAAqD;IAA9CzB,KAAK,EAAC;EAAoB,GAAC,aAAW,sBAC7CyB,mBAAA,CAAwF,KAAxFqT,YAAwF,EAAAhS,gBAAA,CAArErC,KAAA,CAAAkP,cAAc,CAACqD,gBAAgB,CAAC+B,WAAW,oC,KAGlEtT,mBAAA,CAiBM,OAjBNuT,YAiBM,GAhBJvT,mBAAA,CAGM,OAHNwT,YAGM,G,8BAFJxT,mBAAA,CAAoD;IAA7CzB,KAAK,EAAC;EAAoB,GAAC,YAAU,sBAC5CyB,mBAAA,CAAuF,KAAvFyT,YAAuF,EAAApS,gBAAA,CAApErC,KAAA,CAAAkP,cAAc,CAACqD,gBAAgB,CAACe,UAAU,oC,GAE/DtS,mBAAA,CAGM,OAHN0T,YAGM,G,8BAFJ1T,mBAAA,CAAuD;IAAhDzB,KAAK,EAAC;EAAoB,GAAC,eAAa,sBAC/CyB,mBAAA,CAA0G,KAA1G2T,YAA0G,EAAAtS,gBAAA,CAAvF/B,QAAA,CAAA0L,cAAc,CAAChM,KAAA,CAAAkP,cAAc,CAACqD,gBAAgB,CAACqC,aAAa,qC,GAEjF5T,mBAAA,CAGM,OAHN6T,YAGM,G,8BAFJ7T,mBAAA,CAAoD;IAA7CzB,KAAK,EAAC;EAAoB,GAAC,YAAU,sBAC5CyB,mBAAA,CAAwG,KAAxG8T,YAAwG,EAAAzS,gBAAA,CAArF/B,QAAA,CAAA0L,cAAc,CAAChM,KAAA,CAAAkP,cAAc,CAACqD,gBAAgB,CAACwC,UAAU,sC,GAE9E/T,mBAAA,CAGM,OAHNgU,YAGM,G,8BAFJhU,mBAAA,CAA0D;IAAnDzB,KAAK,EAAC;EAAoB,GAAC,kBAAgB,sBAClDyB,mBAAA,CAAoG,KAApGiU,YAAoG,EAAA5S,gBAAA,CAAjF/B,QAAA,CAAA0L,cAAc,CAAChM,KAAA,CAAAkP,cAAc,CAACqD,gBAAgB,CAAC2C,gBAAgB,4B,0IAShGnU,mBAAA,sCAAyC,EACzCC,mBAAA,CAoHM,OApHNmU,YAoHM,GAnHJpU,mBAAA,uBAA0B,EAC1BC,mBAAA,CAkEM,OAlENoU,YAkEM,G,8BAjEJpU,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAa,IACtByB,mBAAA,CAAwE;IAApEzB,KAAK,EAAC;EAAM,IAACyB,mBAAA,CAAiC;IAA9BzB,KAAK,EAAC;EAAmB,I,iBAAK,mBAAiB,E,wBAErEyB,mBAAA,CA6DM,OA7DNqU,YA6DM,GA5DJrU,mBAAA,CAQM,OARNsU,YAQM,G,8BAPJtU,mBAAA,CAAuD;IAAhDzB,KAAK,EAAC;EAAoB,GAAC,eAAa,sB,gBAC/CyB,mBAAA,CAKS;IALDzB,KAAK,EAAC,aAAa;iEAAUS,KAAA,CAAAuV,gBAAgB,CAACC,SAAS,GAAAjT,MAAA;oCAC7DvB,mBAAA,CAA2C;IAAnCoF,KAAK,EAAC;EAAE,GAAC,mBAAiB,uB,kBAClCzG,mBAAA,CAESqC,SAAA,QAAAqE,WAAA,CAFgBrG,KAAA,CAAAsG,aAAa,EAAvBH,MAAM;yBAArBxG,mBAAA,CAES;MAFgCsC,GAAG,EAAEkE,MAAM,CAACI,EAAE;MAAGH,KAAK,EAAED,MAAM,CAACI;wBACnEjG,QAAA,CAAAmG,YAAY,CAACN,MAAM,CAACK,WAAW,yBAAAiP,YAAA;2EAHDzV,KAAA,CAAAuV,gBAAgB,CAACC,SAAS,E,KAOjExU,mBAAA,CAQM,OARN0U,YAQM,G,8BAPJ1U,mBAAA,CAAsD;IAA/CzB,KAAK,EAAC;EAAoB,GAAC,cAAY,sB,gBAC9CyB,mBAAA,CAKY;IAJVzB,KAAK,EAAC,cAAc;IACpBoW,IAAI,EAAC,GAAG;iEACC3V,KAAA,CAAAuV,gBAAgB,CAACK,MAAM,GAAArT,MAAA;IAChCuD,WAAW,EAAC;iDADH9F,KAAA,CAAAuV,gBAAgB,CAACK,MAAM,E,KAIpC5U,mBAAA,CAiBM,OAjBN6U,YAiBM,GAhBJ7U,mBAAA,CAOS;IANPzB,KAAK,EAAC,iBAAiB;IACtB6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAAwV,4BAAA,IAAAxV,QAAA,CAAAwV,4BAAA,IAAAxU,IAAA,CAA4B;IACnCkC,QAAQ,GAAGxD,KAAA,CAAAuV,gBAAgB,CAACC;oCAE7BxU,mBAAA,CAAgC;IAA7BzB,KAAK,EAAC;EAAkB,4B,iBAAK,iBAElC,E,iCACAyB,mBAAA,CAGS;IAHDzB,KAAK,EAAC,iBAAiB;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAAyV,uBAAA,IAAAzV,QAAA,CAAAyV,uBAAA,IAAAzU,IAAA,CAAuB;oCAC7DN,mBAAA,CAAiC;IAA9BzB,KAAK,EAAC;EAAmB,4B,iBAAK,iBAEnC,E,IACAyB,mBAAA,CAGS;IAHDzB,KAAK,EAAC,gBAAgB;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAkB,MAAA,IAAEvC,KAAA,CAAAgW,cAAc,IAAIhW,KAAA,CAAAgW,cAAc;oCACrEhV,mBAAA,CAAiC;IAA9BzB,KAAK,EAAC;EAAmB,6B,iBAAK,GACjC,GAAA8C,gBAAA,CAAGrC,KAAA,CAAAgW,cAAc,+C,KAIrBjV,mBAAA,oBAAuB,EACZf,KAAA,CAAAgW,cAAc,I,cAAzBrW,mBAAA,CAqBM,OArBNsW,YAqBM,GApBJjV,mBAAA,CASM,OATNkV,YASM,G,8BARJlV,mBAAA,CAAwE;IAAjEzB,KAAK,EAAC;EAAgC,GAAC,oBAAkB,sB,gBAChEyB,mBAAA,CAMY;IALVzB,KAAK,EAAC,cAAc;IACpBoW,IAAI,EAAC,GAAG;iEACC3V,KAAA,CAAAmW,UAAU,CAACP,MAAM,GAAArT,MAAA;IAC1BuD,WAAW,EAAC,gDAAgD;IAC5DsQ,QAAQ,EAAR;iDAFSpW,KAAA,CAAAmW,UAAU,CAACP,MAAM,E,KAK9B5U,mBAAA,CASM,OATNqV,YASM,GARJrV,mBAAA,CAOS;IANPzB,KAAK,EAAC,gBAAgB;IACrB6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAAgW,sBAAA,IAAAhW,QAAA,CAAAgW,sBAAA,IAAAhV,IAAA,CAAsB;IAC7BkC,QAAQ,GAAGxD,KAAA,CAAAmW,UAAU,CAACP,MAAM,IAAI5V,KAAA,CAAAmW,UAAU,CAACP,MAAM,CAACW,IAAI;oCAEvDvV,mBAAA,CAAiC;IAA9BzB,KAAK,EAAC;EAAmB,4B,iBAAK,qBAEnC,E,8EAMRwB,mBAAA,yBAA4B,EAC5BC,mBAAA,CA4CM,OA5CNwV,YA4CM,G,8BA3CJxV,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAa,IACtByB,mBAAA,CAAgF;IAA5EzB,KAAK,EAAC;EAAM,IAACyB,mBAAA,CAAuC;IAApCzB,KAAK,EAAC;EAAyB,I,iBAAK,qBAAmB,E,wBAE7EyB,mBAAA,CAuCM,OAvCNyV,YAuCM,GAtCJzV,mBAAA,CAGM,OAHN0V,YAGM,G,8BAFJ1V,mBAAA,CAAwD;IAAjDzB,KAAK,EAAC;EAAoB,GAAC,gBAAc,sBAChDyB,mBAAA,CAA0E,KAA1E2V,YAA0E,EAAAtU,gBAAA,CAAvDrC,KAAA,CAAAkP,cAAc,CAAC0H,cAAc,oC,GAElD5V,mBAAA,CAOM,OAPN6V,YAOM,G,8BANJ7V,mBAAA,CAAwD;IAAjDzB,KAAK,EAAC;EAAoB,GAAC,gBAAc,sBAChDyB,mBAAA,CAII,KAJJ8V,YAII,GAHF9V,mBAAA,CAEO;IAFDzB,KAAK,EAAA0B,eAAA,EAAC,OAAO,EAASjB,KAAA,CAAAkP,cAAc,CAAC6H,cAAc,6BAA6B/W,KAAA,CAAAkP,cAAc,CAAC6H,cAAc;sBAC9G/W,KAAA,CAAAkP,cAAc,CAAC6H,cAAc,oC,KAItC/V,mBAAA,CAyBM,OAzBNgW,YAyBM,GAxBJhW,mBAAA,CAKM,OALNiW,YAKM,GAJJjW,mBAAA,CAGM,OAHNkW,YAGM,G,8BAFJlW,mBAAA,CAAwD;IAAjDzB,KAAK,EAAC;EAA0B,GAAC,UAAQ,sBAChDyB,mBAAA,CAAiE,KAAjEmW,YAAiE,EAAA9U,gBAAA,CAA9C/B,QAAA,CAAA0L,cAAc,CAAChM,KAAA,CAAAkP,cAAc,CAACkI,QAAQ,kB,KAG7DpW,mBAAA,CAKM,OALNqW,YAKM,GAJJrW,mBAAA,CAGM,OAHNsW,YAGM,G,8BAFJtW,mBAAA,CAA+D;IAAxDzB,KAAK,EAAC;EAA0B,GAAC,iBAAe,sBACvDyB,mBAAA,CAAwE,KAAxEuW,YAAwE,EAAAlV,gBAAA,CAArD/B,QAAA,CAAA0L,cAAc,CAAChM,KAAA,CAAAkP,cAAc,CAACsI,eAAe,kB,KAGpExW,mBAAA,CAKM,OALNyW,YAKM,GAJJzW,mBAAA,CAGM,OAHN0W,YAGM,G,8BAFJ1W,mBAAA,CAA8D;IAAvDzB,KAAK,EAAC;EAA0B,GAAC,gBAAc,sBACtDyB,mBAAA,CAAuE,KAAvE2W,YAAuE,EAAAtV,gBAAA,CAApD/B,QAAA,CAAA0L,cAAc,CAAChM,KAAA,CAAAkP,cAAc,CAAC0I,cAAc,kB,KAGnE5W,mBAAA,CAKM,OALN6W,YAKM,GAJJ7W,mBAAA,CAGM,OAHN8W,YAGM,G,8BAFJ9W,mBAAA,CAA4D;IAArDzB,KAAK,EAAC;EAA0B,GAAC,cAAY,sBACpDyB,mBAAA,CAAuF,KAAvF+W,YAAuF,EAAA1V,gBAAA,CAA/C/B,QAAA,CAAA0L,cAAc,CAAChM,KAAA,CAAAkP,cAAc,CAACjD,SAAS,kB,eAS7FlL,mBAAA,6BAAgC,EAChCC,mBAAA,CAyCM,OAzCNgX,YAyCM,G,8BAxCJhX,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAa,IACtByB,mBAAA,CAAuE;IAAnEzB,KAAK,EAAC;EAAM,IAACyB,mBAAA,CAAmC;IAAhCzB,KAAK,EAAC;EAAqB,I,iBAAK,gBAAc,E,wBAEpEyB,mBAAA,CAoCM,OApCNiX,YAoCM,GAnCOjY,KAAA,CAAAkP,cAAc,CAACgJ,cAAc,IAAIlY,KAAA,CAAAkP,cAAc,CAACgJ,cAAc,CAAC7Q,MAAM,Q,cAAhF1H,mBAAA,CA8BM,OA9BNwY,YA8BM,I,kBA7BJxY,mBAAA,CA4BMqC,SAAA,QAAAqE,WAAA,CA3BuBrG,KAAA,CAAAkP,cAAc,CAACgJ,cAAc,GAAhDE,OAAO,EAAEC,KAAK;yBADxB1Y,mBAAA,CA4BM;MA1BHsC,GAAG,EAAEmW,OAAO,CAAC7R,EAAE;MAChBhH,KAAK,EAAA0B,eAAA,EAAC,eAAe;QAAA,sBACWoX,KAAK,KAAKrY,KAAA,CAAAkP,cAAc,CAACgJ,cAAc,CAAC7Q,MAAM;MAAA;QAE9ErG,mBAAA,CAEM;MAFDzB,KAAK,EAAA0B,eAAA,EAAC,iBAAiB,QAAeX,QAAA,CAAAsL,cAAc,CAACwM,OAAO,CAACE,eAAe;2CAC/EtX,mBAAA,CAA6B;MAA1BzB,KAAK,EAAC;IAAe,2B,qBAE1ByB,mBAAA,CAkBM,OAlBNuX,YAkBM,GAjBJvX,mBAAA,CAKM,OALNwX,YAKM,GAJJxX,mBAAA,CAEO;MAFDzB,KAAK,EAAA0B,eAAA,EAAC,OAAO,QAAeX,QAAA,CAAAsL,cAAc,CAACwM,OAAO,CAACE,eAAe;wBACnEhY,QAAA,CAAAmG,YAAY,CAAC2R,OAAO,CAACE,eAAe,0BAEzCtX,mBAAA,CAA+E,SAA/EyX,YAA+E,EAAApW,gBAAA,CAA7C/B,QAAA,CAAA+Q,cAAc,CAAC+G,OAAO,CAACM,UAAU,kB,GAErE1X,mBAAA,CAUM,OAVN2X,YAUM,GATJ3X,mBAAA,CAEI,KAFJ4X,YAEI,G,8BADF5X,mBAAA,CAA4B,gBAApB,aAAW,sB,iBAAS,GAAC,GAAAqB,gBAAA,CAAG+V,OAAO,CAACS,eAAe,iB,GAEhDT,OAAO,CAACU,eAAe,I,cAAhCnZ,mBAAA,CAEI,KAFJoZ,YAEI,G,8BADF/X,mBAAA,CAAsB,gBAAd,OAAK,sB,iBAAS,GAAC,GAAAqB,gBAAA,CAAG/B,QAAA,CAAAmG,YAAY,CAAC2R,OAAO,CAACU,eAAe,kB,wCAEvDV,OAAO,CAACY,aAAa,I,cAA9BrZ,mBAAA,CAEI,KAFJsZ,YAEI,G,8BADFjY,mBAAA,CAAwB,gBAAhB,SAAO,sB,iBAAS,GAAC,GAAAqB,gBAAA,CAAG+V,OAAO,CAACY,aAAa,iB;qDAM3DrZ,mBAAA,CAGM,OAHNuZ,YAGM,EAAA7X,MAAA,UAAAA,MAAA,SAFJL,mBAAA,CAAyC;IAAtCzB,KAAK,EAAC;EAA2B,4BACpCyB,mBAAA,CAAkC,WAA/B,6BAA2B,oB,WAKtCA,mBAAA,CASM,OATNmY,YASM,GARJnY,mBAAA,CAGS;IAHDsB,IAAI,EAAC,QAAQ;IAAC/C,KAAK,EAAC,mBAAmB;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAkB,MAAA,IAAEvC,KAAA,CAAAiP,kBAAkB;oCACxEjO,mBAAA,CAAiC;IAA9BzB,KAAK,EAAC;EAAmB,4B,iBAAK,SAEnC,E,IACAyB,mBAAA,CAGS;IAHDsB,IAAI,EAAC,QAAQ;IAAC/C,KAAK,EAAC,iBAAiB;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAA8Y,qBAAA,IAAA9Y,QAAA,CAAA8Y,qBAAA,IAAA9X,IAAA,CAAqB;oCACzEN,mBAAA,CAAoC;IAAjCzB,KAAK,EAAC;EAAsB,4B,iBAAK,WAEtC,E,iDAMRwB,mBAAA,wBAA2B,EAChBf,KAAA,CAAAqZ,eAAe,IAAIrZ,KAAA,CAAAsZ,wBAAwB,I,cAAtD3Z,mBAAA,CA6DM,OA7DN4Z,YA6DM,GA5DJvY,mBAAA,CA2DM,OA3DNwY,YA2DM,GA1DJxY,mBAAA,CAyDM,OAzDNyY,YAyDM,GAxDJzY,mBAAA,CAMM,OANN0Y,YAMM,G,8BALJ1Y,mBAAA,CAGK;IAHDzB,KAAK,EAAC;EAAa,IACrByB,mBAAA,CAAoD;IAAjDzB,KAAK,EAAC;EAAsC,I,iBAAK,kBAEtD,E,sBACAyB,mBAAA,CAAgF;IAAxEsB,IAAI,EAAC,QAAQ;IAAC/C,KAAK,EAAC,WAAW;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAAqZ,qBAAA,IAAArZ,QAAA,CAAAqZ,qBAAA,IAAArY,IAAA,CAAqB;QAEvEN,mBAAA,CAkCM,OAlCN4Y,YAkCM,G,8BAjCJ5Y,mBAAA,CAGM;IAHDzB,KAAK,EAAC;EAAqB,IAC9ByB,mBAAA,CAAgD;IAA7CzB,KAAK,EAAC;EAAkC,I,iBAAK,kGAElD,E,sBAEAyB,mBAAA,CAOM,OAPN6Y,YAOM,G,8BANJ7Y,mBAAA,CAAiC,gBAAzB,kBAAgB,sBACxBA,mBAAA,CAIK,MAJL8Y,YAIK,GAHH9Y,mBAAA,CAAuF,a,8BAAnFA,mBAAA,CAAgC,gBAAxB,iBAAe,sB,iBAAS,GAAC,GAAAqB,gBAAA,CAAGrC,KAAA,CAAAsZ,wBAAwB,CAACnP,cAAc,iB,GAC/EnJ,mBAAA,CAAqF,a,8BAAjFA,mBAAA,CAA+B,gBAAvB,gBAAc,sB,iBAAS,GAAC,GAAAqB,gBAAA,CAAGrC,KAAA,CAAAsZ,wBAAwB,CAAC1S,aAAa,iB,GAC7E5F,mBAAA,CAA4E,a,8BAAxEA,mBAAA,CAAwB,gBAAhB,SAAO,sB,iBAAS,GAAC,GAAAqB,gBAAA,CAAGrC,KAAA,CAAAsZ,wBAAwB,CAACpO,WAAW,iB,OAIxElK,mBAAA,CAkBM,OAlBN+Y,YAkBM,G,8BAjBJ/Y,mBAAA,CAEQ;IAFDyH,GAAG,EAAC,iBAAiB;IAAClJ,KAAK,EAAC;MACjCyB,mBAAA,CAAoE,iB,iBAA5D,mBAAiB,GAAAA,mBAAA,CAAkC;IAA5BzB,KAAK,EAAC;EAAa,GAAC,GAAC,E,wCAEtDyB,mBAAA,CAOY;IANVuF,EAAE,EAAC,iBAAiB;iEACXvG,KAAA,CAAAga,eAAe,CAACpE,MAAM,GAAArT,MAAA;IAC/BhD,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,cAGIjB,KAAA,CAAAga,eAAe,CAACC;IAAK;IAF7CtE,IAAI,EAAC,GAAG;IACR7P,WAAW,EAAC;0CAHH9F,KAAA,CAAAga,eAAe,CAACpE,MAAM,E,GAMtB5V,KAAA,CAAAga,eAAe,CAACC,KAAK,I,cAAhCta,mBAAA,CAEM,OAFNua,YAEM,EAAA7X,gBAAA,CADDrC,KAAA,CAAAga,eAAe,CAACC,KAAK,oB,iEAE1BjZ,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAW,GAAC,iFAEvB,qB,KAGJyB,mBAAA,CAaM,OAbNmZ,YAaM,GAZJnZ,mBAAA,CAGS;IAHDsB,IAAI,EAAC,QAAQ;IAAC/C,KAAK,EAAC,mBAAmB;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAAqZ,qBAAA,IAAArZ,QAAA,CAAAqZ,qBAAA,IAAArY,IAAA,CAAqB;IAAGkC,QAAQ,EAAExD,KAAA,CAAAga,eAAe,CAAClY;oCACxGd,mBAAA,CAAiC;IAA9BzB,KAAK,EAAC;EAAmB,4B,iBAAK,UAEnC,E,iCACAyB,mBAAA,CAOS;IAPDsB,IAAI,EAAC,QAAQ;IAAC/C,KAAK,EAAC,gBAAgB;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAA8Z,kBAAA,IAAA9Z,QAAA,CAAA8Z,kBAAA,IAAA9Y,IAAA,CAAkB;IAAGkC,QAAQ,EAAExD,KAAA,CAAAga,eAAe,CAAClY,OAAO,KAAK9B,KAAA,CAAAga,eAAe,CAACpE,MAAM,CAACW,IAAI;oCACzIvV,mBAAA,CAAwC;IAArCzB,KAAK,EAAC;EAA0B,6BACvBS,KAAA,CAAAga,eAAe,CAAClY,OAAO,I,cAAnCnC,mBAAA,CAGO,QAAA0a,YAAA,EAAAhZ,MAAA,UAAAA,MAAA,SAFLL,mBAAA,CAA2C;IAAxCzB,KAAK,EAAC;EAA6B,4B,iBAAK,gBAE7C,E,qBACAI,mBAAA,CAAkC,QAAA2a,YAAA,EAArB,gBAAc,G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}