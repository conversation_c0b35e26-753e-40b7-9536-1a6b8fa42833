{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, toDisplayString as _toDisplayString, vModelSelect as _vModelSelect, withDirectives as _withDirectives, vModelText as _vModelText, openBlock as _openBlock, createElementBlock as _createElementBlock, Fragment as _Fragment, renderList as _renderList, withModifiers as _withModifiers, createStaticVNode as _createStaticVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"admin-users\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-container\"\n};\nconst _hoisted_3 = {\n  class: \"container-fluid p-4\"\n};\nconst _hoisted_4 = {\n  class: \"row mb-4\"\n};\nconst _hoisted_5 = {\n  class: \"col-12\"\n};\nconst _hoisted_6 = {\n  class: \"d-flex justify-content-between align-items-center flex-wrap\"\n};\nconst _hoisted_7 = {\n  class: \"d-flex gap-2\"\n};\nconst _hoisted_8 = [\"disabled\"];\nconst _hoisted_9 = {\n  class: \"row mb-4\"\n};\nconst _hoisted_10 = {\n  class: \"col-md-3 mb-3\"\n};\nconst _hoisted_11 = {\n  class: \"card border-left-primary shadow h-100 py-2\"\n};\nconst _hoisted_12 = {\n  class: \"card-body\"\n};\nconst _hoisted_13 = {\n  class: \"row no-gutters align-items-center\"\n};\nconst _hoisted_14 = {\n  class: \"col mr-2\"\n};\nconst _hoisted_15 = {\n  class: \"h5 mb-0 font-weight-bold text-gray-800\"\n};\nconst _hoisted_16 = {\n  class: \"col-md-3 mb-3\"\n};\nconst _hoisted_17 = {\n  class: \"card border-left-success shadow h-100 py-2\"\n};\nconst _hoisted_18 = {\n  class: \"card-body\"\n};\nconst _hoisted_19 = {\n  class: \"row no-gutters align-items-center\"\n};\nconst _hoisted_20 = {\n  class: \"col mr-2\"\n};\nconst _hoisted_21 = {\n  class: \"h5 mb-0 font-weight-bold text-gray-800\"\n};\nconst _hoisted_22 = {\n  class: \"col-md-3 mb-3\"\n};\nconst _hoisted_23 = {\n  class: \"card border-left-warning shadow h-100 py-2\"\n};\nconst _hoisted_24 = {\n  class: \"card-body\"\n};\nconst _hoisted_25 = {\n  class: \"row no-gutters align-items-center\"\n};\nconst _hoisted_26 = {\n  class: \"col mr-2\"\n};\nconst _hoisted_27 = {\n  class: \"h5 mb-0 font-weight-bold text-gray-800\"\n};\nconst _hoisted_28 = {\n  class: \"col-md-3 mb-3\"\n};\nconst _hoisted_29 = {\n  class: \"card border-left-info shadow h-100 py-2\"\n};\nconst _hoisted_30 = {\n  class: \"card-body\"\n};\nconst _hoisted_31 = {\n  class: \"row no-gutters align-items-center\"\n};\nconst _hoisted_32 = {\n  class: \"col mr-2\"\n};\nconst _hoisted_33 = {\n  class: \"h5 mb-0 font-weight-bold text-gray-800\"\n};\nconst _hoisted_34 = {\n  class: \"row\"\n};\nconst _hoisted_35 = {\n  class: \"col-12\"\n};\nconst _hoisted_36 = {\n  class: \"card shadow\"\n};\nconst _hoisted_37 = {\n  class: \"card-header py-3 d-flex justify-content-between align-items-center\"\n};\nconst _hoisted_38 = {\n  class: \"d-flex gap-2\"\n};\nconst _hoisted_39 = {\n  class: \"card-body\"\n};\nconst _hoisted_40 = {\n  class: \"row mb-3\"\n};\nconst _hoisted_41 = {\n  class: \"col-md-6\"\n};\nconst _hoisted_42 = {\n  class: \"input-group\"\n};\nconst _hoisted_43 = {\n  class: \"col-md-6 text-end\"\n};\nconst _hoisted_44 = {\n  class: \"text-muted\"\n};\nconst _hoisted_45 = {\n  key: 0,\n  class: \"text-center py-4\"\n};\nconst _hoisted_46 = {\n  class: \"text-center py-5\"\n};\nconst _hoisted_47 = {\n  class: \"text-muted\"\n};\nconst _hoisted_48 = {\n  class: \"table-responsive\"\n};\nconst _hoisted_49 = {\n  class: \"table table-hover\"\n};\nconst _hoisted_50 = {\n  class: \"d-flex align-items-center\"\n};\nconst _hoisted_51 = {\n  class: \"user-avatar me-3\"\n};\nconst _hoisted_52 = [\"src\", \"alt\"];\nconst _hoisted_53 = {\n  key: 1,\n  class: \"avatar-placeholder rounded-circle\"\n};\nconst _hoisted_54 = {\n  class: \"fw-bold\"\n};\nconst _hoisted_55 = {\n  class: \"text-muted small\"\n};\nconst _hoisted_56 = {\n  class: \"btn-group btn-group-sm\"\n};\nconst _hoisted_57 = [\"onClick\"];\nconst _hoisted_58 = [\"onClick\"];\nconst _hoisted_59 = [\"onClick\", \"title\"];\nconst _hoisted_60 = [\"onClick\"];\nconst _hoisted_61 = {\n  key: 3,\n  class: \"d-flex justify-content-between align-items-center mt-3\"\n};\nconst _hoisted_62 = {\n  class: \"text-muted\"\n};\nconst _hoisted_63 = {\n  class: \"pagination pagination-sm mb-0\"\n};\nconst _hoisted_64 = [\"disabled\"];\nconst _hoisted_65 = [\"onClick\"];\nconst _hoisted_66 = [\"disabled\"];\nconst _hoisted_67 = {\n  class: \"modal fade\",\n  id: \"addUserModal\",\n  tabindex: \"-1\",\n  \"aria-labelledby\": \"addUserModalLabel\",\n  \"aria-hidden\": \"true\"\n};\nconst _hoisted_68 = {\n  class: \"modal-dialog modal-lg\"\n};\nconst _hoisted_69 = {\n  class: \"modal-content\"\n};\nconst _hoisted_70 = {\n  class: \"modal-body\"\n};\nconst _hoisted_71 = {\n  class: \"row\"\n};\nconst _hoisted_72 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_73 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_74 = {\n  class: \"row\"\n};\nconst _hoisted_75 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_76 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_77 = {\n  class: \"row\"\n};\nconst _hoisted_78 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_79 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_80 = {\n  class: \"mb-3\"\n};\nconst _hoisted_81 = {\n  class: \"modal-footer\"\n};\nconst _hoisted_82 = [\"disabled\"];\nconst _hoisted_83 = {\n  key: 0,\n  class: \"spinner-border spinner-border-sm me-2\",\n  role: \"status\"\n};\nconst _hoisted_84 = {\n  key: 1,\n  class: \"fas fa-plus me-2\"\n};\nconst _hoisted_85 = {\n  class: \"modal fade\",\n  id: \"editUserModal\",\n  tabindex: \"-1\",\n  \"aria-labelledby\": \"editUserModalLabel\",\n  \"aria-hidden\": \"true\"\n};\nconst _hoisted_86 = {\n  class: \"modal-dialog modal-lg\"\n};\nconst _hoisted_87 = {\n  class: \"modal-content\"\n};\nconst _hoisted_88 = {\n  class: \"modal-body\"\n};\nconst _hoisted_89 = {\n  class: \"row\"\n};\nconst _hoisted_90 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_91 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_92 = {\n  class: \"row\"\n};\nconst _hoisted_93 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_94 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_95 = {\n  class: \"row\"\n};\nconst _hoisted_96 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_97 = {\n  class: \"col-md-6 mb-3\"\n};\nconst _hoisted_98 = {\n  class: \"mb-3\"\n};\nconst _hoisted_99 = {\n  class: \"modal-footer\"\n};\nconst _hoisted_100 = [\"disabled\"];\nconst _hoisted_101 = {\n  key: 0,\n  class: \"spinner-border spinner-border-sm me-2\",\n  role: \"status\"\n};\nconst _hoisted_102 = {\n  key: 1,\n  class: \"fas fa-save me-2\"\n};\nconst _hoisted_103 = {\n  class: \"modal fade\",\n  id: \"viewUserModal\",\n  tabindex: \"-1\",\n  \"aria-labelledby\": \"viewUserModalLabel\",\n  \"aria-hidden\": \"true\"\n};\nconst _hoisted_104 = {\n  class: \"modal-dialog modal-lg\"\n};\nconst _hoisted_105 = {\n  class: \"modal-content\"\n};\nconst _hoisted_106 = {\n  key: 0,\n  class: \"modal-body\"\n};\nconst _hoisted_107 = {\n  class: \"row\"\n};\nconst _hoisted_108 = {\n  class: \"col-md-4 text-center mb-4\"\n};\nconst _hoisted_109 = {\n  class: \"user-avatar-large mx-auto mb-3\"\n};\nconst _hoisted_110 = [\"src\", \"alt\"];\nconst _hoisted_111 = {\n  key: 1,\n  class: \"avatar-placeholder-large rounded-circle\"\n};\nconst _hoisted_112 = {\n  class: \"col-md-8\"\n};\nconst _hoisted_113 = {\n  class: \"row mb-3\"\n};\nconst _hoisted_114 = {\n  class: \"col-sm-8\"\n};\nconst _hoisted_115 = {\n  class: \"row mb-3\"\n};\nconst _hoisted_116 = {\n  class: \"col-sm-8\"\n};\nconst _hoisted_117 = {\n  class: \"row mb-3\"\n};\nconst _hoisted_118 = {\n  class: \"col-sm-8\"\n};\nconst _hoisted_119 = {\n  class: \"row mb-3\"\n};\nconst _hoisted_120 = {\n  class: \"col-sm-8\"\n};\nconst _hoisted_121 = {\n  class: \"row mb-3\"\n};\nconst _hoisted_122 = {\n  class: \"col-sm-8\"\n};\nconst _hoisted_123 = {\n  class: \"row mb-3\"\n};\nconst _hoisted_124 = {\n  class: \"col-sm-8\"\n};\nconst _hoisted_125 = {\n  class: \"modal-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_AdminHeader = _resolveComponent(\"AdminHeader\");\n  const _component_AdminSidebar = _resolveComponent(\"AdminSidebar\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_AdminHeader, {\n    userName: $data.adminData?.first_name || 'Admin',\n    showUserDropdown: $data.showUserDropdown,\n    sidebarCollapsed: $data.sidebarCollapsed,\n    activeMenu: $options.activeMenu,\n    onSidebarToggle: $options.handleSidebarToggle,\n    onUserDropdownToggle: $options.handleUserDropdownToggle,\n    onMenuAction: $options.handleMenuAction,\n    onLogout: $options.handleLogout\n  }, null, 8 /* PROPS */, [\"userName\", \"showUserDropdown\", \"sidebarCollapsed\", \"activeMenu\", \"onSidebarToggle\", \"onUserDropdownToggle\", \"onMenuAction\", \"onLogout\"]), _createCommentVNode(\" Mobile Overlay \"), _createElementVNode(\"div\", {\n    class: _normalizeClass([\"mobile-overlay\", {\n      active: !$data.sidebarCollapsed && $data.isMobile\n    }]),\n    onClick: _cache[0] || (_cache[0] = (...args) => $options.closeMobileSidebar && $options.closeMobileSidebar(...args))\n  }, null, 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_AdminSidebar, {\n    collapsed: $data.sidebarCollapsed,\n    activeMenu: $options.activeMenu,\n    onMenuChange: $options.handleMenuChange,\n    onLogout: $options.handleLogout,\n    onToggleSidebar: $options.handleSidebarToggle\n  }, null, 8 /* PROPS */, [\"collapsed\", \"activeMenu\", \"onMenuChange\", \"onLogout\", \"onToggleSidebar\"]), _createElementVNode(\"main\", {\n    class: _normalizeClass([\"main-content\", {\n      'sidebar-collapsed': $data.sidebarCollapsed\n    }])\n  }, [_createElementVNode(\"div\", _hoisted_3, [_createCommentVNode(\" Page Header \"), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"button\", {\n    class: \"btn btn-outline-success btn-sm\",\n    onClick: _cache[1] || (_cache[1] = (...args) => $options.loadUsers && $options.loadUsers(...args)),\n    disabled: $data.loading\n  }, [_createElementVNode(\"i\", {\n    class: _normalizeClass([\"fas fa-sync-alt me-1\", {\n      'fa-spin': $data.loading\n    }])\n  }, null, 2 /* CLASS */), _cache[30] || (_cache[30] = _createTextVNode(\" Refresh \"))], 8 /* PROPS */, _hoisted_8), _createElementVNode(\"button\", {\n    class: \"btn btn-success btn-sm\",\n    onClick: _cache[2] || (_cache[2] = (...args) => $options.showAddUserModal && $options.showAddUserModal(...args))\n  }, _cache[31] || (_cache[31] = [_createElementVNode(\"i\", {\n    class: \"fas fa-user-plus me-1\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Add User \")]))])])])]), _createCommentVNode(\" User Statistics \"), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_cache[32] || (_cache[32] = _createElementVNode(\"div\", {\n    class: \"text-xs font-weight-bold text-primary text-uppercase mb-1\"\n  }, \" Total Users \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_15, _toDisplayString($data.userStats.total || 0), 1 /* TEXT */)]), _cache[33] || (_cache[33] = _createElementVNode(\"div\", {\n    class: \"col-auto\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-users fa-2x text-gray-300\"\n  })], -1 /* HOISTED */))])])])]), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_cache[34] || (_cache[34] = _createElementVNode(\"div\", {\n    class: \"text-xs font-weight-bold text-success text-uppercase mb-1\"\n  }, \" Active Users \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_21, _toDisplayString($data.userStats.active || 0), 1 /* TEXT */)]), _cache[35] || (_cache[35] = _createElementVNode(\"div\", {\n    class: \"col-auto\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user-check fa-2x text-gray-300\"\n  })], -1 /* HOISTED */))])])])]), _createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"div\", _hoisted_26, [_cache[36] || (_cache[36] = _createElementVNode(\"div\", {\n    class: \"text-xs font-weight-bold text-warning text-uppercase mb-1\"\n  }, \" Pending Verification \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_27, _toDisplayString($data.userStats.pending || 0), 1 /* TEXT */)]), _cache[37] || (_cache[37] = _createElementVNode(\"div\", {\n    class: \"col-auto\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user-clock fa-2x text-gray-300\"\n  })], -1 /* HOISTED */))])])])]), _createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"div\", _hoisted_30, [_createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, [_cache[38] || (_cache[38] = _createElementVNode(\"div\", {\n    class: \"text-xs font-weight-bold text-info text-uppercase mb-1\"\n  }, \" Admin Users \", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_33, _toDisplayString($data.userStats.admins || 0), 1 /* TEXT */)]), _cache[39] || (_cache[39] = _createElementVNode(\"div\", {\n    class: \"col-auto\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user-shield fa-2x text-gray-300\"\n  })], -1 /* HOISTED */))])])])])]), _createCommentVNode(\" Users Table \"), _createElementVNode(\"div\", _hoisted_34, [_createElementVNode(\"div\", _hoisted_35, [_createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"div\", _hoisted_37, [_cache[42] || (_cache[42] = _createElementVNode(\"h6\", {\n    class: \"m-0 font-weight-bold text-primary\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-users me-2\"\n  }), _createTextVNode(\" User List \")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_38, [_withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select form-select-sm\",\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $data.filterStatus = $event),\n    onChange: _cache[4] || (_cache[4] = (...args) => $options.filterUsers && $options.filterUsers(...args))\n  }, _cache[40] || (_cache[40] = [_createStaticVNode(\"<option value=\\\"\\\" data-v-f270332e>All Status</option><option value=\\\"active\\\" data-v-f270332e>Active</option><option value=\\\"inactive\\\" data-v-f270332e>Inactive</option><option value=\\\"pending\\\" data-v-f270332e>Pending</option><option value=\\\"suspended\\\" data-v-f270332e>Suspended</option>\", 5)]), 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.filterStatus]]), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select form-select-sm\",\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $data.filterType = $event),\n    onChange: _cache[6] || (_cache[6] = (...args) => $options.filterUsers && $options.filterUsers(...args))\n  }, _cache[41] || (_cache[41] = [_createElementVNode(\"option\", {\n    value: \"\"\n  }, \"All Types\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"client\"\n  }, \"Clients\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"admin\"\n  }, \"Admins\", -1 /* HOISTED */)]), 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelSelect, $data.filterType]])])]), _createElementVNode(\"div\", _hoisted_39, [_createCommentVNode(\" Search Bar \"), _createElementVNode(\"div\", _hoisted_40, [_createElementVNode(\"div\", _hoisted_41, [_createElementVNode(\"div\", _hoisted_42, [_cache[43] || (_cache[43] = _createElementVNode(\"span\", {\n    class: \"input-group-text\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-search\"\n  })], -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    placeholder: \"Search users by name, email, or username...\",\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $data.searchQuery = $event),\n    onInput: _cache[8] || (_cache[8] = (...args) => $options.searchUsers && $options.searchUsers(...args))\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $data.searchQuery]])])]), _createElementVNode(\"div\", _hoisted_43, [_createElementVNode(\"span\", _hoisted_44, \" Showing \" + _toDisplayString($data.filteredUsers.length) + \" of \" + _toDisplayString($data.users.length) + \" users \", 1 /* TEXT */)])]), _createCommentVNode(\" Loading State \"), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_45, _cache[44] || (_cache[44] = [_createElementVNode(\"div\", {\n    class: \"spinner-border text-primary\",\n    role: \"status\"\n  }, [_createElementVNode(\"span\", {\n    class: \"visually-hidden\"\n  }, \"Loading...\")], -1 /* HOISTED */), _createElementVNode(\"p\", {\n    class: \"text-muted mt-2\"\n  }, \"Loading users...\", -1 /* HOISTED */)]))) : $data.filteredUsers.length === 0 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" Empty State \"), _createElementVNode(\"div\", _hoisted_46, [_cache[45] || (_cache[45] = _createElementVNode(\"i\", {\n    class: \"fas fa-users fa-3x text-gray-300 mb-3\"\n  }, null, -1 /* HOISTED */)), _cache[46] || (_cache[46] = _createElementVNode(\"h5\", {\n    class: \"text-gray-600\"\n  }, \"No users found\", -1 /* HOISTED */)), _createElementVNode(\"p\", _hoisted_47, _toDisplayString($data.searchQuery ? 'Try adjusting your search criteria.' : 'No users have been registered yet.'), 1 /* TEXT */)])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createCommentVNode(\" Users Table \"), _createElementVNode(\"div\", _hoisted_48, [_createElementVNode(\"table\", _hoisted_49, [_cache[50] || (_cache[50] = _createElementVNode(\"thead\", {\n    class: \"table-light\"\n  }, [_createElementVNode(\"tr\", null, [_createElementVNode(\"th\", null, \"User\"), _createElementVNode(\"th\", null, \"Email\"), _createElementVNode(\"th\", null, \"Type\"), _createElementVNode(\"th\", null, \"Status\"), _createElementVNode(\"th\", null, \"Registered\"), _createElementVNode(\"th\", null, \"Actions\")])], -1 /* HOISTED */)), _createElementVNode(\"tbody\", null, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.paginatedUsers, user => {\n    return _openBlock(), _createElementBlock(\"tr\", {\n      key: user.id\n    }, [_createElementVNode(\"td\", null, [_createElementVNode(\"div\", _hoisted_50, [_createElementVNode(\"div\", _hoisted_51, [user.profile_picture ? (_openBlock(), _createElementBlock(\"img\", {\n      key: 0,\n      src: user.profile_picture,\n      alt: user.full_name,\n      class: \"rounded-circle\"\n    }, null, 8 /* PROPS */, _hoisted_52)) : (_openBlock(), _createElementBlock(\"div\", _hoisted_53, _toDisplayString($options.getInitials(user.full_name)), 1 /* TEXT */))]), _createElementVNode(\"div\", null, [_createElementVNode(\"div\", _hoisted_54, _toDisplayString(user.full_name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_55, \"@\" + _toDisplayString(user.username), 1 /* TEXT */)])])]), _createElementVNode(\"td\", null, _toDisplayString(user.email), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"span\", {\n      class: _normalizeClass([\"badge\", user.type === 'admin' ? 'bg-primary' : 'bg-info'])\n    }, _toDisplayString(user.type === 'admin' ? 'Admin' : 'Client'), 3 /* TEXT, CLASS */)]), _createElementVNode(\"td\", null, [_createElementVNode(\"span\", {\n      class: _normalizeClass([\"badge\", $options.getStatusBadgeClass(user.status)])\n    }, _toDisplayString($options.formatStatus(user.status)), 3 /* TEXT, CLASS */)]), _createElementVNode(\"td\", null, _toDisplayString($options.formatDate(user.created_at)), 1 /* TEXT */), _createElementVNode(\"td\", null, [_createElementVNode(\"div\", _hoisted_56, [_createElementVNode(\"button\", {\n      class: \"btn btn-outline-primary\",\n      onClick: $event => $options.viewUser(user),\n      title: \"View Details\"\n    }, [...(_cache[47] || (_cache[47] = [_createElementVNode(\"i\", {\n      class: \"fas fa-eye\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_57), _createElementVNode(\"button\", {\n      class: \"btn btn-outline-warning\",\n      onClick: $event => $options.editUser(user),\n      title: \"Edit User\"\n    }, [...(_cache[48] || (_cache[48] = [_createElementVNode(\"i\", {\n      class: \"fas fa-edit\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_58), _createElementVNode(\"button\", {\n      class: _normalizeClass([\"btn\", user.status === 'active' ? 'btn-outline-warning' : 'btn-outline-success']),\n      onClick: $event => $options.toggleUserStatus(user),\n      title: user.status === 'active' ? 'Suspend User' : 'Activate User'\n    }, [_createElementVNode(\"i\", {\n      class: _normalizeClass(user.status === 'active' ? 'fas fa-pause' : 'fas fa-play')\n    }, null, 2 /* CLASS */)], 10 /* CLASS, PROPS */, _hoisted_59), _createElementVNode(\"button\", {\n      class: \"btn btn-outline-danger\",\n      onClick: $event => $options.deleteUser(user),\n      title: \"Delete User\"\n    }, [...(_cache[49] || (_cache[49] = [_createElementVNode(\"i\", {\n      class: \"fas fa-trash\"\n    }, null, -1 /* HOISTED */)]))], 8 /* PROPS */, _hoisted_60)])])]);\n  }), 128 /* KEYED_FRAGMENT */))])])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" Pagination \"), $options.totalPages > 1 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_61, [_createElementVNode(\"div\", _hoisted_62, \" Page \" + _toDisplayString($data.currentPage) + \" of \" + _toDisplayString($options.totalPages), 1 /* TEXT */), _createElementVNode(\"nav\", null, [_createElementVNode(\"ul\", _hoisted_63, [_createElementVNode(\"li\", {\n    class: _normalizeClass([\"page-item\", {\n      disabled: $data.currentPage === 1\n    }])\n  }, [_createElementVNode(\"button\", {\n    class: \"page-link\",\n    onClick: _cache[9] || (_cache[9] = $event => $options.changePage($data.currentPage - 1)),\n    disabled: $data.currentPage === 1\n  }, \" Previous \", 8 /* PROPS */, _hoisted_64)], 2 /* CLASS */), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.visiblePages, page => {\n    return _openBlock(), _createElementBlock(\"li\", {\n      key: page,\n      class: _normalizeClass([\"page-item\", {\n        active: page === $data.currentPage\n      }])\n    }, [_createElementVNode(\"button\", {\n      class: \"page-link\",\n      onClick: $event => $options.changePage(page)\n    }, _toDisplayString(page), 9 /* TEXT, PROPS */, _hoisted_65)], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */)), _createElementVNode(\"li\", {\n    class: _normalizeClass([\"page-item\", {\n      disabled: $data.currentPage === $options.totalPages\n    }])\n  }, [_createElementVNode(\"button\", {\n    class: \"page-link\",\n    onClick: _cache[10] || (_cache[10] = $event => $options.changePage($data.currentPage + 1)),\n    disabled: $data.currentPage === $options.totalPages\n  }, \" Next \", 8 /* PROPS */, _hoisted_66)], 2 /* CLASS */)])])])) : _createCommentVNode(\"v-if\", true)])])])])])], 2 /* CLASS */)]), _createCommentVNode(\" Add User Modal \"), _createElementVNode(\"div\", _hoisted_67, [_createElementVNode(\"div\", _hoisted_68, [_createElementVNode(\"div\", _hoisted_69, [_cache[60] || (_cache[60] = _createElementVNode(\"div\", {\n    class: \"modal-header\"\n  }, [_createElementVNode(\"h5\", {\n    class: \"modal-title\",\n    id: \"addUserModalLabel\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user-plus me-2\"\n  }), _createTextVNode(\" Add New User \")]), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn-close\",\n    \"data-bs-dismiss\": \"modal\",\n    \"aria-label\": \"Close\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_70, [_createElementVNode(\"form\", {\n    onSubmit: _cache[18] || (_cache[18] = _withModifiers((...args) => $options.submitAddUser && $options.submitAddUser(...args), [\"prevent\"]))\n  }, [_createElementVNode(\"div\", _hoisted_71, [_createElementVNode(\"div\", _hoisted_72, [_cache[51] || (_cache[51] = _createElementVNode(\"label\", {\n    for: \"addUsername\",\n    class: \"form-label\"\n  }, \"Username *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    id: \"addUsername\",\n    \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $data.addUserForm.username = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addUserForm.username]])]), _createElementVNode(\"div\", _hoisted_73, [_cache[52] || (_cache[52] = _createElementVNode(\"label\", {\n    for: \"addEmail\",\n    class: \"form-label\"\n  }, \"Email *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"email\",\n    class: \"form-control\",\n    id: \"addEmail\",\n    \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $data.addUserForm.email = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addUserForm.email]])])]), _createElementVNode(\"div\", _hoisted_74, [_createElementVNode(\"div\", _hoisted_75, [_cache[53] || (_cache[53] = _createElementVNode(\"label\", {\n    for: \"addFirstName\",\n    class: \"form-label\"\n  }, \"First Name *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    id: \"addFirstName\",\n    \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $data.addUserForm.first_name = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addUserForm.first_name]])]), _createElementVNode(\"div\", _hoisted_76, [_cache[54] || (_cache[54] = _createElementVNode(\"label\", {\n    for: \"addLastName\",\n    class: \"form-label\"\n  }, \"Last Name *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    id: \"addLastName\",\n    \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $data.addUserForm.last_name = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addUserForm.last_name]])])]), _createElementVNode(\"div\", _hoisted_77, [_createElementVNode(\"div\", _hoisted_78, [_cache[56] || (_cache[56] = _createElementVNode(\"label\", {\n    for: \"addRole\",\n    class: \"form-label\"\n  }, \"Role *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select\",\n    id: \"addRole\",\n    \"onUpdate:modelValue\": _cache[15] || (_cache[15] = $event => $data.addUserForm.role = $event),\n    required: \"\"\n  }, _cache[55] || (_cache[55] = [_createElementVNode(\"option\", {\n    value: \"\"\n  }, \"Select Role\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"admin\"\n  }, \"Administrator\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"client\"\n  }, \"Client\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.addUserForm.role]])]), _createElementVNode(\"div\", _hoisted_79, [_cache[57] || (_cache[57] = _createElementVNode(\"label\", {\n    for: \"addPassword\",\n    class: \"form-label\"\n  }, \"Password *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"password\",\n    class: \"form-control\",\n    id: \"addPassword\",\n    \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $data.addUserForm.password = $event),\n    required: \"\",\n    minlength: \"6\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addUserForm.password]])])]), _createElementVNode(\"div\", _hoisted_80, [_cache[58] || (_cache[58] = _createElementVNode(\"label\", {\n    for: \"addPhone\",\n    class: \"form-label\"\n  }, \"Phone Number\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"tel\",\n    class: \"form-control\",\n    id: \"addPhone\",\n    \"onUpdate:modelValue\": _cache[17] || (_cache[17] = $event => $data.addUserForm.phone_number = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.addUserForm.phone_number]])])], 32 /* NEED_HYDRATION */)]), _createElementVNode(\"div\", _hoisted_81, [_cache[59] || (_cache[59] = _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-secondary\",\n    \"data-bs-dismiss\": \"modal\"\n  }, \"Cancel\", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-primary\",\n    onClick: _cache[19] || (_cache[19] = (...args) => $options.submitAddUser && $options.submitAddUser(...args)),\n    disabled: $data.addUserLoading\n  }, [$data.addUserLoading ? (_openBlock(), _createElementBlock(\"span\", _hoisted_83)) : (_openBlock(), _createElementBlock(\"i\", _hoisted_84)), _createTextVNode(\" \" + _toDisplayString($data.addUserLoading ? 'Creating...' : 'Create User'), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_82)])])])]), _createCommentVNode(\" Edit User Modal \"), _createElementVNode(\"div\", _hoisted_85, [_createElementVNode(\"div\", _hoisted_86, [_createElementVNode(\"div\", _hoisted_87, [_cache[71] || (_cache[71] = _createElementVNode(\"div\", {\n    class: \"modal-header\"\n  }, [_createElementVNode(\"h5\", {\n    class: \"modal-title\",\n    id: \"editUserModalLabel\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user-edit me-2\"\n  }), _createTextVNode(\" Edit User \")]), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn-close\",\n    \"data-bs-dismiss\": \"modal\",\n    \"aria-label\": \"Close\"\n  })], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_88, [$data.editUserForm.id ? (_openBlock(), _createElementBlock(\"form\", {\n    key: 0,\n    onSubmit: _cache[27] || (_cache[27] = _withModifiers((...args) => $options.submitEditUser && $options.submitEditUser(...args), [\"prevent\"]))\n  }, [_createElementVNode(\"div\", _hoisted_89, [_createElementVNode(\"div\", _hoisted_90, [_cache[61] || (_cache[61] = _createElementVNode(\"label\", {\n    for: \"editUsername\",\n    class: \"form-label\"\n  }, \"Username *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    id: \"editUsername\",\n    \"onUpdate:modelValue\": _cache[20] || (_cache[20] = $event => $data.editUserForm.username = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.editUserForm.username]])]), _createElementVNode(\"div\", _hoisted_91, [_cache[62] || (_cache[62] = _createElementVNode(\"label\", {\n    for: \"editEmail\",\n    class: \"form-label\"\n  }, \"Email *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"email\",\n    class: \"form-control\",\n    id: \"editEmail\",\n    \"onUpdate:modelValue\": _cache[21] || (_cache[21] = $event => $data.editUserForm.email = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.editUserForm.email]])])]), _createElementVNode(\"div\", _hoisted_92, [_createElementVNode(\"div\", _hoisted_93, [_cache[63] || (_cache[63] = _createElementVNode(\"label\", {\n    for: \"editFirstName\",\n    class: \"form-label\"\n  }, \"First Name *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    id: \"editFirstName\",\n    \"onUpdate:modelValue\": _cache[22] || (_cache[22] = $event => $data.editUserForm.first_name = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.editUserForm.first_name]])]), _createElementVNode(\"div\", _hoisted_94, [_cache[64] || (_cache[64] = _createElementVNode(\"label\", {\n    for: \"editLastName\",\n    class: \"form-label\"\n  }, \"Last Name *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    class: \"form-control\",\n    id: \"editLastName\",\n    \"onUpdate:modelValue\": _cache[23] || (_cache[23] = $event => $data.editUserForm.last_name = $event),\n    required: \"\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.editUserForm.last_name]])])]), _createElementVNode(\"div\", _hoisted_95, [_createElementVNode(\"div\", _hoisted_96, [_cache[66] || (_cache[66] = _createElementVNode(\"label\", {\n    for: \"editRole\",\n    class: \"form-label\"\n  }, \"Role *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select\",\n    id: \"editRole\",\n    \"onUpdate:modelValue\": _cache[24] || (_cache[24] = $event => $data.editUserForm.role = $event),\n    required: \"\"\n  }, _cache[65] || (_cache[65] = [_createElementVNode(\"option\", {\n    value: \"admin\"\n  }, \"Administrator\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"client\"\n  }, \"Client\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.editUserForm.role]])]), _createElementVNode(\"div\", _hoisted_97, [_cache[68] || (_cache[68] = _createElementVNode(\"label\", {\n    for: \"editStatus\",\n    class: \"form-label\"\n  }, \"Status *\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"select\", {\n    class: \"form-select\",\n    id: \"editStatus\",\n    \"onUpdate:modelValue\": _cache[25] || (_cache[25] = $event => $data.editUserForm.status = $event),\n    required: \"\"\n  }, _cache[67] || (_cache[67] = [_createElementVNode(\"option\", {\n    value: \"active\"\n  }, \"Active\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"inactive\"\n  }, \"Inactive\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"suspended\"\n  }, \"Suspended\", -1 /* HOISTED */), _createElementVNode(\"option\", {\n    value: \"pending\"\n  }, \"Pending\", -1 /* HOISTED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.editUserForm.status]])])]), _createElementVNode(\"div\", _hoisted_98, [_cache[69] || (_cache[69] = _createElementVNode(\"label\", {\n    for: \"editPhone\",\n    class: \"form-label\"\n  }, \"Phone Number\", -1 /* HOISTED */)), _withDirectives(_createElementVNode(\"input\", {\n    type: \"tel\",\n    class: \"form-control\",\n    id: \"editPhone\",\n    \"onUpdate:modelValue\": _cache[26] || (_cache[26] = $event => $data.editUserForm.phone_number = $event)\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.editUserForm.phone_number]])])], 32 /* NEED_HYDRATION */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_99, [_cache[70] || (_cache[70] = _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-secondary\",\n    \"data-bs-dismiss\": \"modal\"\n  }, \"Cancel\", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-primary\",\n    onClick: _cache[28] || (_cache[28] = (...args) => $options.submitEditUser && $options.submitEditUser(...args)),\n    disabled: $data.editUserLoading\n  }, [$data.editUserLoading ? (_openBlock(), _createElementBlock(\"span\", _hoisted_101)) : (_openBlock(), _createElementBlock(\"i\", _hoisted_102)), _createTextVNode(\" \" + _toDisplayString($data.editUserLoading ? 'Updating...' : 'Update User'), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_100)])])])]), _createCommentVNode(\" View User Modal \"), _createElementVNode(\"div\", _hoisted_103, [_createElementVNode(\"div\", _hoisted_104, [_createElementVNode(\"div\", _hoisted_105, [_cache[80] || (_cache[80] = _createElementVNode(\"div\", {\n    class: \"modal-header\"\n  }, [_createElementVNode(\"h5\", {\n    class: \"modal-title\",\n    id: \"viewUserModalLabel\"\n  }, [_createElementVNode(\"i\", {\n    class: \"fas fa-user me-2\"\n  }), _createTextVNode(\" User Details \")]), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn-close\",\n    \"data-bs-dismiss\": \"modal\",\n    \"aria-label\": \"Close\"\n  })], -1 /* HOISTED */)), $data.viewUserData ? (_openBlock(), _createElementBlock(\"div\", _hoisted_106, [_createElementVNode(\"div\", _hoisted_107, [_createElementVNode(\"div\", _hoisted_108, [_createElementVNode(\"div\", _hoisted_109, [$data.viewUserData.profile_picture ? (_openBlock(), _createElementBlock(\"img\", {\n    key: 0,\n    src: $data.viewUserData.profile_picture,\n    alt: $data.viewUserData.full_name,\n    class: \"rounded-circle\"\n  }, null, 8 /* PROPS */, _hoisted_110)) : (_openBlock(), _createElementBlock(\"div\", _hoisted_111, _toDisplayString($options.getInitials($data.viewUserData.full_name)), 1 /* TEXT */))]), _createElementVNode(\"h5\", null, _toDisplayString($data.viewUserData.full_name), 1 /* TEXT */), _createElementVNode(\"span\", {\n    class: _normalizeClass([\"badge\", $options.getStatusBadgeClass($data.viewUserData.status)])\n  }, _toDisplayString($options.formatStatus($data.viewUserData.status)), 3 /* TEXT, CLASS */)]), _createElementVNode(\"div\", _hoisted_112, [_createElementVNode(\"div\", _hoisted_113, [_cache[72] || (_cache[72] = _createElementVNode(\"div\", {\n    class: \"col-sm-4\"\n  }, [_createElementVNode(\"strong\", null, \"Username:\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_114, _toDisplayString($data.viewUserData.username), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_115, [_cache[73] || (_cache[73] = _createElementVNode(\"div\", {\n    class: \"col-sm-4\"\n  }, [_createElementVNode(\"strong\", null, \"Email:\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_116, _toDisplayString($data.viewUserData.email), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_117, [_cache[74] || (_cache[74] = _createElementVNode(\"div\", {\n    class: \"col-sm-4\"\n  }, [_createElementVNode(\"strong\", null, \"Type:\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_118, [_createElementVNode(\"span\", {\n    class: _normalizeClass([\"badge\", $data.viewUserData.type === 'admin' ? 'bg-primary' : 'bg-info'])\n  }, _toDisplayString($data.viewUserData.type === 'admin' ? 'Administrator' : 'Client'), 3 /* TEXT, CLASS */)])]), _createElementVNode(\"div\", _hoisted_119, [_cache[75] || (_cache[75] = _createElementVNode(\"div\", {\n    class: \"col-sm-4\"\n  }, [_createElementVNode(\"strong\", null, \"Phone:\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_120, _toDisplayString($data.viewUserData.phone_number || 'N/A'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_121, [_cache[76] || (_cache[76] = _createElementVNode(\"div\", {\n    class: \"col-sm-4\"\n  }, [_createElementVNode(\"strong\", null, \"Registered:\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_122, _toDisplayString($options.formatDate($data.viewUserData.created_at)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_123, [_cache[77] || (_cache[77] = _createElementVNode(\"div\", {\n    class: \"col-sm-4\"\n  }, [_createElementVNode(\"strong\", null, \"Last Login:\")], -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_124, _toDisplayString($data.viewUserData.last_login ? $options.formatDate($data.viewUserData.last_login) : 'Never'), 1 /* TEXT */)])])])])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_125, [_cache[79] || (_cache[79] = _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-secondary\",\n    \"data-bs-dismiss\": \"modal\"\n  }, \"Close\", -1 /* HOISTED */)), _createElementVNode(\"button\", {\n    type: \"button\",\n    class: \"btn btn-primary\",\n    onClick: _cache[29] || (_cache[29] = $event => $options.editUser($data.viewUserData)),\n    \"data-bs-dismiss\": \"modal\"\n  }, _cache[78] || (_cache[78] = [_createElementVNode(\"i\", {\n    class: \"fas fa-edit me-2\"\n  }, null, -1 /* HOISTED */), _createTextVNode(\" Edit User \")]))])])])])]);\n}", "map": {"version": 3, "names": ["class", "id", "tabindex", "role", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_AdminHeader", "userName", "$data", "adminData", "first_name", "showUserDropdown", "sidebarCollapsed", "activeMenu", "$options", "onSidebarToggle", "handleSidebarToggle", "onUserDropdownToggle", "handleUserDropdownToggle", "onMenuAction", "handleMenuAction", "onLogout", "handleLogout", "_createCommentVNode", "_createElementVNode", "_normalizeClass", "active", "isMobile", "onClick", "_cache", "args", "closeMobileSidebar", "_hoisted_2", "_component_AdminSidebar", "collapsed", "onMenuChange", "handleMenuChange", "onToggleSidebar", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "loadUsers", "disabled", "loading", "showAddUserModal", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_toDisplayString", "userStats", "total", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "pending", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_hoisted_31", "_hoisted_32", "_hoisted_33", "admins", "_hoisted_34", "_hoisted_35", "_hoisted_36", "_hoisted_37", "_hoisted_38", "filterStatus", "$event", "onChange", "filterUsers", "filterType", "value", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "type", "placeholder", "searchQuery", "onInput", "searchUsers", "_hoisted_43", "_hoisted_44", "filteredUsers", "length", "users", "_hoisted_45", "_Fragment", "key", "_hoisted_46", "_hoisted_47", "_hoisted_48", "_hoisted_49", "_renderList", "paginatedUsers", "user", "_hoisted_50", "_hoisted_51", "profile_picture", "src", "alt", "full_name", "_hoisted_53", "getInitials", "_hoisted_54", "_hoisted_55", "username", "email", "getStatusBadgeClass", "status", "formatStatus", "formatDate", "created_at", "_hoisted_56", "viewUser", "title", "editUser", "toggleUserStatus", "deleteUser", "totalPages", "_hoisted_61", "_hoisted_62", "currentPage", "_hoisted_63", "changePage", "_hoisted_64", "visiblePages", "page", "_hoisted_65", "_hoisted_66", "_hoisted_67", "_hoisted_68", "_hoisted_69", "_hoisted_70", "onSubmit", "_withModifiers", "submitAddUser", "_hoisted_71", "_hoisted_72", "for", "addUserForm", "required", "_hoisted_73", "_hoisted_74", "_hoisted_75", "_hoisted_76", "last_name", "_hoisted_77", "_hoisted_78", "_hoisted_79", "password", "minlength", "_hoisted_80", "phone_number", "_hoisted_81", "addUserLoading", "_hoisted_83", "_hoisted_84", "_hoisted_85", "_hoisted_86", "_hoisted_87", "_hoisted_88", "editUserForm", "submitEditUser", "_hoisted_89", "_hoisted_90", "_hoisted_91", "_hoisted_92", "_hoisted_93", "_hoisted_94", "_hoisted_95", "_hoisted_96", "_hoisted_97", "_hoisted_98", "_hoisted_99", "editUserLoading", "_hoisted_101", "_hoisted_102", "_hoisted_103", "_hoisted_104", "_hoisted_105", "viewUserData", "_hoisted_106", "_hoisted_107", "_hoisted_108", "_hoisted_109", "_hoisted_111", "_hoisted_112", "_hoisted_113", "_hoisted_114", "_hoisted_115", "_hoisted_116", "_hoisted_117", "_hoisted_118", "_hoisted_119", "_hoisted_120", "_hoisted_121", "_hoisted_122", "_hoisted_123", "_hoisted_124", "last_login", "_hoisted_125"], "sources": ["D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminUsers.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-users\">\n    <AdminHeader\n      :userName=\"adminData?.first_name || 'Admin'\"\n      :showUserDropdown=\"showUserDropdown\"\n      :sidebarCollapsed=\"sidebarCollapsed\"\n      :activeMenu=\"activeMenu\"\n      @sidebar-toggle=\"handleSidebarToggle\"\n      @user-dropdown-toggle=\"handleUserDropdownToggle\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"handleLogout\"\n    />\n\n    <!-- Mobile Overlay -->\n    <div\n      class=\"mobile-overlay\"\n      :class=\"{ active: !sidebarCollapsed && isMobile }\"\n      @click=\"closeMobileSidebar\"\n    ></div>\n\n    <div class=\"dashboard-container\">\n      <AdminSidebar\n        :collapsed=\"sidebarCollapsed\"\n        :activeMenu=\"activeMenu\"\n        @menu-change=\"handleMenuChange\"\n        @logout=\"handleLogout\"\n        @toggle-sidebar=\"handleSidebarToggle\"\n      />\n\n      <main class=\"main-content\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n        <div class=\"container-fluid p-4\">\n          <!-- Page Header -->\n          <div class=\"row mb-4\">\n            <div class=\"col-12\">\n              <div class=\"d-flex justify-content-between align-items-center flex-wrap\">\n\n                <div class=\"d-flex gap-2\">\n                  <button class=\"btn btn-outline-success btn-sm\" @click=\"loadUsers\" :disabled=\"loading\">\n                    <i class=\"fas fa-sync-alt me-1\" :class=\"{ 'fa-spin': loading }\"></i>\n                    Refresh\n                  </button>\n                  <button class=\"btn btn-success btn-sm\" @click=\"showAddUserModal\">\n                    <i class=\"fas fa-user-plus me-1\"></i>\n                    Add User\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- User Statistics -->\n          <div class=\"row mb-4\">\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-primary shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-primary text-uppercase mb-1\">\n                        Total Users\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.total || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-users fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-success shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-success text-uppercase mb-1\">\n                        Active Users\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.active || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-user-check fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-warning shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-warning text-uppercase mb-1\">\n                        Pending Verification\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.pending || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-user-clock fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-info shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-info text-uppercase mb-1\">\n                        Admin Users\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.admins || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-user-shield fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Users Table -->\n          <div class=\"row\">\n            <div class=\"col-12\">\n              <div class=\"card shadow\">\n                <div class=\"card-header py-3 d-flex justify-content-between align-items-center\">\n                  <h6 class=\"m-0 font-weight-bold text-primary\">\n                    <i class=\"fas fa-users me-2\"></i>\n                    User List\n                  </h6>\n                  <div class=\"d-flex gap-2\">\n                    <select class=\"form-select form-select-sm\" v-model=\"filterStatus\" @change=\"filterUsers\">\n                      <option value=\"\">All Status</option>\n                      <option value=\"active\">Active</option>\n                      <option value=\"inactive\">Inactive</option>\n                      <option value=\"pending\">Pending</option>\n                      <option value=\"suspended\">Suspended</option>\n                    </select>\n                    <select class=\"form-select form-select-sm\" v-model=\"filterType\" @change=\"filterUsers\">\n                      <option value=\"\">All Types</option>\n                      <option value=\"client\">Clients</option>\n                      <option value=\"admin\">Admins</option>\n                    </select>\n                  </div>\n                </div>\n                <div class=\"card-body\">\n                  <!-- Search Bar -->\n                  <div class=\"row mb-3\">\n                    <div class=\"col-md-6\">\n                      <div class=\"input-group\">\n                        <span class=\"input-group-text\">\n                          <i class=\"fas fa-search\"></i>\n                        </span>\n                        <input\n                          type=\"text\"\n                          class=\"form-control\"\n                          placeholder=\"Search users by name, email, or username...\"\n                          v-model=\"searchQuery\"\n                          @input=\"searchUsers\"\n                        >\n                      </div>\n                    </div>\n                    <div class=\"col-md-6 text-end\">\n                      <span class=\"text-muted\">\n                        Showing {{ filteredUsers.length }} of {{ users.length }} users\n                      </span>\n                    </div>\n                  </div>\n\n                  <!-- Loading State -->\n                  <div v-if=\"loading\" class=\"text-center py-4\">\n                    <div class=\"spinner-border text-primary\" role=\"status\">\n                      <span class=\"visually-hidden\">Loading...</span>\n                    </div>\n                    <p class=\"text-muted mt-2\">Loading users...</p>\n                  </div>\n\n                  <!-- Empty State -->\n                  <div v-else-if=\"filteredUsers.length === 0\" class=\"text-center py-5\">\n                    <i class=\"fas fa-users fa-3x text-gray-300 mb-3\"></i>\n                    <h5 class=\"text-gray-600\">No users found</h5>\n                    <p class=\"text-muted\">\n                      {{ searchQuery ? 'Try adjusting your search criteria.' : 'No users have been registered yet.' }}\n                    </p>\n                  </div>\n\n                  <!-- Users Table -->\n                  <div v-else class=\"table-responsive\">\n                    <table class=\"table table-hover\">\n                      <thead class=\"table-light\">\n                        <tr>\n                          <th>User</th>\n                          <th>Email</th>\n                          <th>Type</th>\n                          <th>Status</th>\n                          <th>Registered</th>\n                          <th>Actions</th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        <tr v-for=\"user in paginatedUsers\" :key=\"user.id\">\n                          <td>\n                            <div class=\"d-flex align-items-center\">\n                              <div class=\"user-avatar me-3\">\n                                <img v-if=\"user.profile_picture\" :src=\"user.profile_picture\" :alt=\"user.full_name\" class=\"rounded-circle\">\n                                <div v-else class=\"avatar-placeholder rounded-circle\">\n                                  {{ getInitials(user.full_name) }}\n                                </div>\n                              </div>\n                              <div>\n                                <div class=\"fw-bold\">{{ user.full_name }}</div>\n                                <div class=\"text-muted small\">@{{ user.username }}</div>\n                              </div>\n                            </div>\n                          </td>\n                          <td>{{ user.email }}</td>\n                          <td>\n                            <span class=\"badge\" :class=\"user.type === 'admin' ? 'bg-primary' : 'bg-info'\">\n                              {{ user.type === 'admin' ? 'Admin' : 'Client' }}\n                            </span>\n                          </td>\n                          <td>\n                            <span class=\"badge\" :class=\"getStatusBadgeClass(user.status)\">\n                              {{ formatStatus(user.status) }}\n                            </span>\n                          </td>\n                          <td>{{ formatDate(user.created_at) }}</td>\n                          <td>\n                            <div class=\"btn-group btn-group-sm\">\n                              <button class=\"btn btn-outline-primary\" @click=\"viewUser(user)\" title=\"View Details\">\n                                <i class=\"fas fa-eye\"></i>\n                              </button>\n                              <button class=\"btn btn-outline-warning\" @click=\"editUser(user)\" title=\"Edit User\">\n                                <i class=\"fas fa-edit\"></i>\n                              </button>\n                              <button\n                                class=\"btn\"\n                                :class=\"user.status === 'active' ? 'btn-outline-warning' : 'btn-outline-success'\"\n                                @click=\"toggleUserStatus(user)\"\n                                :title=\"user.status === 'active' ? 'Suspend User' : 'Activate User'\"\n                              >\n                                <i :class=\"user.status === 'active' ? 'fas fa-pause' : 'fas fa-play'\"></i>\n                              </button>\n                              <button class=\"btn btn-outline-danger\" @click=\"deleteUser(user)\" title=\"Delete User\">\n                                <i class=\"fas fa-trash\"></i>\n                              </button>\n                            </div>\n                          </td>\n                        </tr>\n                      </tbody>\n                    </table>\n                  </div>\n\n                  <!-- Pagination -->\n                  <div v-if=\"totalPages > 1\" class=\"d-flex justify-content-between align-items-center mt-3\">\n                    <div class=\"text-muted\">\n                      Page {{ currentPage }} of {{ totalPages }}\n                    </div>\n                    <nav>\n                      <ul class=\"pagination pagination-sm mb-0\">\n                        <li class=\"page-item\" :class=\"{ disabled: currentPage === 1 }\">\n                          <button class=\"page-link\" @click=\"changePage(currentPage - 1)\" :disabled=\"currentPage === 1\">\n                            Previous\n                          </button>\n                        </li>\n                        <li\n                          v-for=\"page in visiblePages\"\n                          :key=\"page\"\n                          class=\"page-item\"\n                          :class=\"{ active: page === currentPage }\"\n                        >\n                          <button class=\"page-link\" @click=\"changePage(page)\">{{ page }}</button>\n                        </li>\n                        <li class=\"page-item\" :class=\"{ disabled: currentPage === totalPages }\">\n                          <button class=\"page-link\" @click=\"changePage(currentPage + 1)\" :disabled=\"currentPage === totalPages\">\n                            Next\n                          </button>\n                        </li>\n                      </ul>\n                    </nav>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n\n    <!-- Add User Modal -->\n    <div class=\"modal fade\" id=\"addUserModal\" tabindex=\"-1\" aria-labelledby=\"addUserModalLabel\" aria-hidden=\"true\">\n      <div class=\"modal-dialog modal-lg\">\n        <div class=\"modal-content\">\n          <div class=\"modal-header\">\n            <h5 class=\"modal-title\" id=\"addUserModalLabel\">\n              <i class=\"fas fa-user-plus me-2\"></i>\n              Add New User\n            </h5>\n            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\n          </div>\n          <div class=\"modal-body\">\n            <form @submit.prevent=\"submitAddUser\">\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addUsername\" class=\"form-label\">Username *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"addUsername\"\n                    v-model=\"addUserForm.username\"\n                    required\n                  >\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addEmail\" class=\"form-label\">Email *</label>\n                  <input\n                    type=\"email\"\n                    class=\"form-control\"\n                    id=\"addEmail\"\n                    v-model=\"addUserForm.email\"\n                    required\n                  >\n                </div>\n              </div>\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addFirstName\" class=\"form-label\">First Name *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"addFirstName\"\n                    v-model=\"addUserForm.first_name\"\n                    required\n                  >\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addLastName\" class=\"form-label\">Last Name *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"addLastName\"\n                    v-model=\"addUserForm.last_name\"\n                    required\n                  >\n                </div>\n              </div>\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addRole\" class=\"form-label\">Role *</label>\n                  <select class=\"form-select\" id=\"addRole\" v-model=\"addUserForm.role\" required>\n                    <option value=\"\">Select Role</option>\n                    <option value=\"admin\">Administrator</option>\n                    <option value=\"client\">Client</option>\n                  </select>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addPassword\" class=\"form-label\">Password *</label>\n                  <input\n                    type=\"password\"\n                    class=\"form-control\"\n                    id=\"addPassword\"\n                    v-model=\"addUserForm.password\"\n                    required\n                    minlength=\"6\"\n                  >\n                </div>\n              </div>\n              <div class=\"mb-3\">\n                <label for=\"addPhone\" class=\"form-label\">Phone Number</label>\n                <input\n                  type=\"tel\"\n                  class=\"form-control\"\n                  id=\"addPhone\"\n                  v-model=\"addUserForm.phone_number\"\n                >\n              </div>\n            </form>\n          </div>\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>\n            <button type=\"button\" class=\"btn btn-primary\" @click=\"submitAddUser\" :disabled=\"addUserLoading\">\n              <span v-if=\"addUserLoading\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\n              <i v-else class=\"fas fa-plus me-2\"></i>\n              {{ addUserLoading ? 'Creating...' : 'Create User' }}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Edit User Modal -->\n    <div class=\"modal fade\" id=\"editUserModal\" tabindex=\"-1\" aria-labelledby=\"editUserModalLabel\" aria-hidden=\"true\">\n      <div class=\"modal-dialog modal-lg\">\n        <div class=\"modal-content\">\n          <div class=\"modal-header\">\n            <h5 class=\"modal-title\" id=\"editUserModalLabel\">\n              <i class=\"fas fa-user-edit me-2\"></i>\n              Edit User\n            </h5>\n            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\n          </div>\n          <div class=\"modal-body\">\n            <form @submit.prevent=\"submitEditUser\" v-if=\"editUserForm.id\">\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editUsername\" class=\"form-label\">Username *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"editUsername\"\n                    v-model=\"editUserForm.username\"\n                    required\n                  >\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editEmail\" class=\"form-label\">Email *</label>\n                  <input\n                    type=\"email\"\n                    class=\"form-control\"\n                    id=\"editEmail\"\n                    v-model=\"editUserForm.email\"\n                    required\n                  >\n                </div>\n              </div>\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editFirstName\" class=\"form-label\">First Name *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"editFirstName\"\n                    v-model=\"editUserForm.first_name\"\n                    required\n                  >\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editLastName\" class=\"form-label\">Last Name *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"editLastName\"\n                    v-model=\"editUserForm.last_name\"\n                    required\n                  >\n                </div>\n              </div>\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editRole\" class=\"form-label\">Role *</label>\n                  <select class=\"form-select\" id=\"editRole\" v-model=\"editUserForm.role\" required>\n                    <option value=\"admin\">Administrator</option>\n                    <option value=\"client\">Client</option>\n                  </select>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editStatus\" class=\"form-label\">Status *</label>\n                  <select class=\"form-select\" id=\"editStatus\" v-model=\"editUserForm.status\" required>\n                    <option value=\"active\">Active</option>\n                    <option value=\"inactive\">Inactive</option>\n                    <option value=\"suspended\">Suspended</option>\n                    <option value=\"pending\">Pending</option>\n                  </select>\n                </div>\n              </div>\n              <div class=\"mb-3\">\n                <label for=\"editPhone\" class=\"form-label\">Phone Number</label>\n                <input\n                  type=\"tel\"\n                  class=\"form-control\"\n                  id=\"editPhone\"\n                  v-model=\"editUserForm.phone_number\"\n                >\n              </div>\n            </form>\n          </div>\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>\n            <button type=\"button\" class=\"btn btn-primary\" @click=\"submitEditUser\" :disabled=\"editUserLoading\">\n              <span v-if=\"editUserLoading\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\n              <i v-else class=\"fas fa-save me-2\"></i>\n              {{ editUserLoading ? 'Updating...' : 'Update User' }}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- View User Modal -->\n    <div class=\"modal fade\" id=\"viewUserModal\" tabindex=\"-1\" aria-labelledby=\"viewUserModalLabel\" aria-hidden=\"true\">\n      <div class=\"modal-dialog modal-lg\">\n        <div class=\"modal-content\">\n          <div class=\"modal-header\">\n            <h5 class=\"modal-title\" id=\"viewUserModalLabel\">\n              <i class=\"fas fa-user me-2\"></i>\n              User Details\n            </h5>\n            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\n          </div>\n          <div class=\"modal-body\" v-if=\"viewUserData\">\n            <div class=\"row\">\n              <div class=\"col-md-4 text-center mb-4\">\n                <div class=\"user-avatar-large mx-auto mb-3\">\n                  <img v-if=\"viewUserData.profile_picture\" :src=\"viewUserData.profile_picture\" :alt=\"viewUserData.full_name\" class=\"rounded-circle\">\n                  <div v-else class=\"avatar-placeholder-large rounded-circle\">\n                    {{ getInitials(viewUserData.full_name) }}\n                  </div>\n                </div>\n                <h5>{{ viewUserData.full_name }}</h5>\n                <span class=\"badge\" :class=\"getStatusBadgeClass(viewUserData.status)\">\n                  {{ formatStatus(viewUserData.status) }}\n                </span>\n              </div>\n              <div class=\"col-md-8\">\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Username:</strong></div>\n                  <div class=\"col-sm-8\">{{ viewUserData.username }}</div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Email:</strong></div>\n                  <div class=\"col-sm-8\">{{ viewUserData.email }}</div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Type:</strong></div>\n                  <div class=\"col-sm-8\">\n                    <span class=\"badge\" :class=\"viewUserData.type === 'admin' ? 'bg-primary' : 'bg-info'\">\n                      {{ viewUserData.type === 'admin' ? 'Administrator' : 'Client' }}\n                    </span>\n                  </div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Phone:</strong></div>\n                  <div class=\"col-sm-8\">{{ viewUserData.phone_number || 'N/A' }}</div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Registered:</strong></div>\n                  <div class=\"col-sm-8\">{{ formatDate(viewUserData.created_at) }}</div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Last Login:</strong></div>\n                  <div class=\"col-sm-8\">{{ viewUserData.last_login ? formatDate(viewUserData.last_login) : 'Never' }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Close</button>\n            <button type=\"button\" class=\"btn btn-primary\" @click=\"editUser(viewUserData)\" data-bs-dismiss=\"modal\">\n              <i class=\"fas fa-edit me-2\"></i>\n              Edit User\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport AdminHeader from './AdminHeader.vue';\nimport AdminSidebar from './AdminSidebar.vue';\nimport adminAuthService from '@/services/adminAuthService';\nimport userManagementService from '@/services/userManagementService';\nimport { Modal } from 'bootstrap';\n\nexport default {\n  name: 'AdminUsers',\n  components: {\n    AdminHeader,\n    AdminSidebar\n  },\n\n  data() {\n    return {\n      // UI State\n      sidebarCollapsed: false,\n      showUserDropdown: false,\n      isMobile: false,\n      adminData: null,\n      // Component Data\n      users: [],\n      filteredUsers: [],\n      searchQuery: '',\n      filterStatus: '',\n      filterType: '',\n      currentPage: 1,\n      itemsPerPage: 10,\n      loading: false,\n      userStats: {\n        total: 0,\n        active: 0,\n        pending: 0,\n        admins: 0\n      },\n\n      // Modal data\n      viewUserData: null,\n      addUserLoading: false,\n      editUserLoading: false,\n\n      // Add user form\n      addUserForm: {\n        username: '',\n        email: '',\n        first_name: '',\n        last_name: '',\n        role: '',\n        password: '',\n        phone_number: ''\n      },\n\n      // Edit user form\n      editUserForm: {\n        id: null,\n        username: '',\n        email: '',\n        first_name: '',\n        last_name: '',\n        role: '',\n        status: '',\n        phone_number: ''\n      }\n    };\n  },\n\n  computed: {\n    activeMenu() {\n      const path = this.$route.path;\n      if (path.includes('/admin/users')) return 'users';\n      if (path.includes('/admin/requests')) return 'requests';\n      if (path.includes('/admin/reports')) return 'reports';\n      if (path.includes('/admin/settings')) return 'settings';\n      if (path.includes('/admin/activity-logs')) return 'activity';\n      if (path.includes('/admin/profile')) return 'profile';\n      return 'dashboard';\n    },\n\n    paginatedUsers() {\n      const start = (this.currentPage - 1) * this.itemsPerPage;\n      const end = start + this.itemsPerPage;\n      return this.filteredUsers.slice(start, end);\n    },\n\n    totalPages() {\n      return Math.ceil(this.filteredUsers.length / this.itemsPerPage);\n    },\n\n    visiblePages() {\n      const pages = [];\n      const start = Math.max(1, this.currentPage - 2);\n      const end = Math.min(this.totalPages, this.currentPage + 2);\n\n      for (let i = start; i <= end; i++) {\n        pages.push(i);\n      }\n      return pages;\n    }\n  },\n\n  async mounted() {\n    // Check authentication\n    if (!adminAuthService.isLoggedIn()) {\n      this.$router.push('/admin/login');\n      return;\n    }\n\n    // Initialize UI state\n    this.initializeUI();\n\n    // Make bootstrap available globally for this component\n    this.$bootstrap = { Modal };\n\n    // Load component data\n    await this.loadAdminProfile();\n    await this.loadUserStats();\n    await this.loadUsers();\n  },\n\n  beforeUnmount() {\n    if (this.handleResize) {\n      window.removeEventListener('resize', this.handleResize);\n    }\n  },\n\n  methods: {\n    // Initialize UI state\n    initializeUI() {\n      this.isMobile = window.innerWidth <= 768;\n\n      // Load saved sidebar state (only on desktop)\n      if (!this.isMobile) {\n        const saved = localStorage.getItem('adminSidebarCollapsed');\n        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n      } else {\n        this.sidebarCollapsed = true; // Always collapsed on mobile\n      }\n\n      // Setup resize listener\n      this.handleResize = () => {\n        const wasMobile = this.isMobile;\n        this.isMobile = window.innerWidth <= 768;\n\n        if (this.isMobile && !wasMobile) {\n          this.sidebarCollapsed = true; // Collapse when switching to mobile\n        } else if (!this.isMobile && wasMobile) {\n          // Restore saved state when switching to desktop\n          const saved = localStorage.getItem('adminSidebarCollapsed');\n          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n        }\n      };\n      window.addEventListener('resize', this.handleResize);\n    },\n\n    // Sidebar toggle\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));\n    },\n\n    // Menu navigation\n    handleMenuChange(menu) {\n      const routes = {\n        'dashboard': '/admin/dashboard',\n        'users': '/admin/users',\n        'requests': '/admin/requests',\n        'reports': '/admin/reports',\n        'settings': '/admin/settings',\n        'activity': '/admin/activity-logs',\n        'profile': '/admin/profile'\n      };\n\n      // Close sidebar on mobile after navigation\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n\n      if (routes[menu]) {\n        this.$router.push(routes[menu]);\n      }\n    },\n\n    // User dropdown toggle\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n\n    // Menu actions\n    handleMenuAction(action) {\n      if (action === 'profile') {\n        this.$router.push('/admin/profile');\n      } else if (action === 'settings') {\n        this.$router.push('/admin/settings');\n      }\n      this.showUserDropdown = false;\n    },\n\n    // Close mobile sidebar\n    closeMobileSidebar() {\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n    },\n\n    // Logout\n    handleLogout() {\n      adminAuthService.logout();\n      this.$router.push('/admin/login');\n    },\n\n    // Load admin profile data\n    async loadAdminProfile() {\n      try {\n        const response = await adminAuthService.getProfile();\n        if (response.success) {\n          this.adminData = response.data;\n        }\n      } catch (error) {\n        console.error('Failed to load admin data:', error);\n        this.adminData = adminAuthService.getAdminData();\n      }\n    },\n\n    // Load user statistics\n    async loadUserStats() {\n      try {\n        const response = await userManagementService.getUserStats();\n\n        if (response.success) {\n          this.userStats = response.data;\n        } else {\n          throw new Error(response.message || 'Failed to load user statistics');\n        }\n      } catch (error) {\n        console.error('Failed to load user statistics:', error);\n        // Keep default stats on error\n        this.userStats = {\n          total: 0,\n          active: 0,\n          pending: 0,\n          admins: 0\n        };\n      }\n    },\n\n    // Load users data\n    async loadUsers() {\n      this.loading = true;\n      try {\n        const params = {\n          page: this.currentPage,\n          limit: 50, // Load more for client-side filtering\n          search: this.searchQuery || undefined,\n          role: this.filterType || undefined,\n          is_active: this.filterStatus === 'active' ? true :\n                     this.filterStatus === 'inactive' ? false : undefined\n        };\n\n        const response = await userManagementService.getUsers(params);\n\n        if (response.success) {\n          // Format users for display\n          this.users = response.data.users.map(user =>\n            userManagementService.formatUserData(user)\n          );\n\n          this.filteredUsers = [...this.users];\n          this.calculateStats();\n        } else {\n          throw new Error(response.message || 'Failed to load users');\n        }\n      } catch (error) {\n        console.error('Failed to load users:', error);\n        this.$toast?.error?.(error.message || 'Failed to load users');\n\n        // Fallback to empty state\n        this.users = [];\n        this.filteredUsers = [];\n        this.calculateStats();\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // Calculate user statistics\n    calculateStats() {\n      this.userStats = {\n        total: this.users.length,\n        active: this.users.filter(u => u.status === 'active').length,\n        pending: this.users.filter(u => u.status === 'pending').length,\n        admins: this.users.filter(u => u.type === 'admin').length\n      };\n    },\n\n    // Search users\n    searchUsers() {\n      this.filterUsers();\n    },\n\n    // Filter users based on search and filters\n    filterUsers() {\n      let filtered = [...this.users];\n\n      // Apply search filter\n      if (this.searchQuery) {\n        const query = this.searchQuery.toLowerCase();\n        filtered = filtered.filter(user =>\n          user.full_name.toLowerCase().includes(query) ||\n          user.email.toLowerCase().includes(query) ||\n          user.username.toLowerCase().includes(query)\n        );\n      }\n\n      // Apply status filter\n      if (this.filterStatus) {\n        filtered = filtered.filter(user => user.status === this.filterStatus);\n      }\n\n      // Apply type filter\n      if (this.filterType) {\n        filtered = filtered.filter(user => user.type === this.filterType);\n      }\n\n      this.filteredUsers = filtered;\n      this.currentPage = 1; // Reset to first page\n    },\n\n    // Change page\n    changePage(page) {\n      if (page >= 1 && page <= this.totalPages) {\n        this.currentPage = page;\n      }\n    },\n\n    // Get user initials for avatar\n    getInitials(fullName) {\n      if (!fullName) return '?';\n      return fullName.split(' ').map(name => name.charAt(0)).join('').toUpperCase().slice(0, 2);\n    },\n\n    // Get status badge class\n    getStatusBadgeClass(status) {\n      const classes = {\n        'active': 'bg-success',\n        'inactive': 'bg-secondary',\n        'pending': 'bg-warning',\n        'suspended': 'bg-danger'\n      };\n      return classes[status] || 'bg-secondary';\n    },\n\n    // Format status text\n    formatStatus(status) {\n      return status.charAt(0).toUpperCase() + status.slice(1);\n    },\n\n    // Format date\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    },\n\n    // User actions\n\n\n    async toggleUserStatus(user) {\n      try {\n        const newStatus = user.status === 'active' ? 'suspended' : 'active';\n        const reason = `Status changed by admin: ${this.adminData?.first_name || 'Admin'}`;\n\n        const response = await userManagementService.updateUserStatus(user.id, newStatus, reason);\n\n        if (response.success) {\n          // Update local data\n          user.status = newStatus;\n          this.calculateStats();\n\n          this.$toast?.success?.(`User ${user.full_name} has been ${newStatus === 'active' ? 'activated' : 'suspended'}.`);\n        } else {\n          throw new Error(response.message || 'Failed to update user status');\n        }\n      } catch (error) {\n        console.error('Failed to update user status:', error);\n        this.$toast?.error?.(error.message || 'Failed to update user status. Please try again.');\n      }\n    },\n\n    async deleteUser(user) {\n      if (!confirm(`Are you sure you want to delete user \"${user.full_name}\"? This action cannot be undone.`)) {\n        return;\n      }\n\n      try {\n        const reason = `User deleted by admin: ${this.adminData?.first_name || 'Admin'}`;\n        const response = await userManagementService.deleteUser(user.id, reason);\n\n        if (response.success) {\n          // Remove from local data\n          const index = this.users.findIndex(u => u.id === user.id);\n          if (index > -1) {\n            this.users.splice(index, 1);\n            this.filterUsers();\n            this.calculateStats();\n          }\n\n          this.$toast?.success?.(`User ${user.full_name} has been deleted.`);\n        } else {\n          throw new Error(response.message || 'Failed to delete user');\n        }\n      } catch (error) {\n        console.error('Failed to delete user:', error);\n        this.$toast?.error?.(error.message || 'Failed to delete user. Please try again.');\n      }\n    },\n\n\n\n    // Modal methods\n    showAddUserModal() {\n      this.resetAddUserForm();\n      try {\n        const modalElement = document.getElementById('addUserModal');\n        if (modalElement) {\n          const modal = new Modal(modalElement);\n          modal.show();\n        }\n      } catch (error) {\n        console.error('Error showing add user modal:', error);\n        this.$toast?.error?.('Failed to open add user modal');\n      }\n    },\n\n    resetAddUserForm() {\n      this.addUserForm = {\n        username: '',\n        email: '',\n        first_name: '',\n        last_name: '',\n        role: '',\n        password: '',\n        phone_number: ''\n      };\n    },\n\n    async submitAddUser() {\n      try {\n        this.addUserLoading = true;\n\n        // Validate form\n        const validation = userManagementService.validateUserData(this.addUserForm);\n        if (!validation.isValid) {\n          this.$toast?.error?.(validation.errors.join(', '));\n          return;\n        }\n\n        const response = await userManagementService.createUser(this.addUserForm);\n\n        if (response.success) {\n          this.$toast?.success?.('User created successfully');\n\n          // Close modal\n          try {\n            const modal = Modal.getInstance(document.getElementById('addUserModal'));\n            if (modal) modal.hide();\n          } catch (error) {\n            console.error('Error closing modal:', error);\n          }\n\n          // Reload data\n          await this.loadUsers();\n          await this.loadUserStats();\n        } else {\n          throw new Error(response.message || 'Failed to create user');\n        }\n      } catch (error) {\n        console.error('Failed to create user:', error);\n        this.$toast?.error?.(error.message || 'Failed to create user');\n      } finally {\n        this.addUserLoading = false;\n      }\n    },\n\n    editUser(user) {\n      this.editUserForm = {\n        id: user.id,\n        username: user.username,\n        email: user.email,\n        first_name: user.first_name,\n        last_name: user.last_name,\n        role: user.type,\n        status: user.status,\n        phone_number: user.phone_number || ''\n      };\n\n      try {\n        const modalElement = document.getElementById('editUserModal');\n        if (modalElement) {\n          const modal = new Modal(modalElement);\n          modal.show();\n        }\n      } catch (error) {\n        console.error('Error showing edit user modal:', error);\n        this.$toast?.error?.('Failed to open edit user modal');\n      }\n    },\n\n    async submitEditUser() {\n      try {\n        this.editUserLoading = true;\n\n        // Validate form\n        const validation = userManagementService.validateUserData(this.editUserForm, true);\n        if (!validation.isValid) {\n          this.$toast?.error?.(validation.errors.join(', '));\n          return;\n        }\n\n        const response = await userManagementService.updateUser(this.editUserForm.id, this.editUserForm);\n\n        if (response.success) {\n          this.$toast?.success?.('User updated successfully');\n\n          // Close modal\n          try {\n            const modal = Modal.getInstance(document.getElementById('editUserModal'));\n            if (modal) modal.hide();\n          } catch (error) {\n            console.error('Error closing modal:', error);\n          }\n\n          // Update local data\n          const userIndex = this.users.findIndex(u => u.id === this.editUserForm.id);\n          if (userIndex > -1) {\n            this.users[userIndex] = { ...this.users[userIndex], ...this.editUserForm };\n            this.filterUsers();\n          }\n\n          // Reload stats\n          await this.loadUserStats();\n        } else {\n          throw new Error(response.message || 'Failed to update user');\n        }\n      } catch (error) {\n        console.error('Failed to update user:', error);\n        this.$toast?.error?.(error.message || 'Failed to update user');\n      } finally {\n        this.editUserLoading = false;\n      }\n    },\n\n    async viewUser(user) {\n      try {\n        const response = await userManagementService.getUser(user.id);\n\n        if (response.success) {\n          this.viewUserData = response.data;\n          try {\n            const modalElement = document.getElementById('viewUserModal');\n            if (modalElement) {\n              const modal = new Modal(modalElement);\n              modal.show();\n            }\n          } catch (error) {\n            console.error('Error showing view user modal:', error);\n            this.$toast?.error?.('Failed to open user details modal');\n          }\n        } else {\n          throw new Error(response.message || 'Failed to load user details');\n        }\n      } catch (error) {\n        console.error('Failed to load user details:', error);\n        this.$toast?.error?.(error.message || 'Failed to load user details');\n      }\n    },\n\n    // Additional user-specific methods can be added here\n    // Navigation handlers are now provided by the mixin\n  }\n};\n</script>\n\n<style scoped>\n@import './css/adminDashboard.css';\n\n/* User avatar styles */\n.user-avatar {\n  width: 40px;\n  height: 40px;\n}\n\n.user-avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.avatar-placeholder {\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: 600;\n  font-size: 0.875rem;\n  border-radius: 50%;\n}\n\n.user-avatar-large {\n  width: 80px;\n  height: 80px;\n}\n\n.avatar-placeholder-large {\n  width: 80px;\n  height: 80px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 24px;\n  border-radius: 50%;\n}\n\n/* Modal styles */\n.modal-lg {\n  max-width: 800px;\n}\n\n/* Form styles */\n.form-label {\n  font-weight: 600;\n  color: #495057;\n}\n\n.form-control:focus,\n.form-select:focus {\n  border-color: #80bdff;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n/* Loading states */\n.spinner-border-sm {\n  width: 1rem;\n  height: 1rem;\n}\n\n/* Table improvements */\n.table th {\n  border-top: none;\n  font-weight: 600;\n  color: #5a5c69;\n  font-size: 0.875rem;\n}\n\n.table td {\n  vertical-align: middle;\n  font-size: 0.875rem;\n}\n\n.table-hover tbody tr:hover {\n  background-color: rgba(0, 123, 255, 0.05);\n}\n\n/* Button group improvements */\n.btn-group-sm .btn {\n  padding: 0.375rem 0.5rem;\n  font-size: 0.75rem;\n}\n\n/* Search and filter improvements */\n.form-select-sm {\n  font-size: 0.875rem;\n  padding: 0.375rem 0.75rem;\n}\n\n/* Pagination improvements */\n.pagination-sm .page-link {\n  padding: 0.375rem 0.75rem;\n  font-size: 0.875rem;\n}\n\n/* Badge improvements */\n.badge {\n  font-size: 0.75rem;\n  padding: 0.375rem 0.75rem;\n  border-radius: 0.5rem;\n}\n\n/* Responsive improvements */\n@media (max-width: 768px) {\n  .table-responsive {\n    font-size: 0.8rem;\n  }\n\n  .btn-group-sm .btn {\n    padding: 0.25rem 0.375rem;\n    font-size: 0.7rem;\n  }\n\n  .user-avatar {\n    width: 32px;\n    height: 32px;\n  }\n\n  .avatar-placeholder {\n    width: 32px;\n    height: 32px;\n    font-size: 0.75rem;\n  }\n}\n\n@media (max-width: 576px) {\n  .d-flex.gap-2 {\n    flex-direction: column;\n    gap: 0.5rem !important;\n  }\n\n  .btn-group {\n    flex-direction: column;\n  }\n\n  .btn-group .btn {\n    border-radius: 0.375rem !important;\n    margin-bottom: 0.25rem;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAa;;EAmBjBA,KAAK,EAAC;AAAqB;;EAUvBA,KAAK,EAAC;AAAqB;;EAEzBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAQ;;EACZA,KAAK,EAAC;AAA6D;;EAEjEA,KAAK,EAAC;AAAc;;;EAe1BA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA4C;;EAChDA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAAU;;EAIdA,KAAK,EAAC;AAAwC;;EASxDA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA4C;;EAChDA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAAU;;EAIdA,KAAK,EAAC;AAAwC;;EASxDA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAA4C;;EAChDA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAAU;;EAIdA,KAAK,EAAC;AAAwC;;EASxDA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAyC;;EAC7CA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAmC;;EACvCA,KAAK,EAAC;AAAU;;EAIdA,KAAK,EAAC;AAAwC;;EAY1DA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAQ;;EACZA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAoE;;EAKxEA,KAAK,EAAC;AAAc;;EAetBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAa;;EAarBA,KAAK,EAAC;AAAmB;;EACtBA,KAAK,EAAC;AAAY;;;EAORA,KAAK,EAAC;;;EAQkBA,KAAK,EAAC;AAAkB;;EAG/DA,KAAK,EAAC;AAAY;;EAMXA,KAAK,EAAC;AAAkB;;EAC3BA,KAAK,EAAC;AAAmB;;EAcnBA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAAkB;;;;EAEfA,KAAK,EAAC;;;EAKbA,KAAK,EAAC;AAAS;;EACfA,KAAK,EAAC;AAAkB;;EAiB5BA,KAAK,EAAC;AAAwB;;;;;;;EA0BlBA,KAAK,EAAC;;;EAC1BA,KAAK,EAAC;AAAY;;EAIjBA,KAAK,EAAC;AAA+B;;;;;EA+BtDA,KAAK,EAAC,YAAY;EAACC,EAAE,EAAC,cAAc;EAACC,QAAQ,EAAC,IAAI;EAAC,iBAAe,EAAC,mBAAmB;EAAC,aAAW,EAAC;;;EACjGF,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAe;;EAQnBA,KAAK,EAAC;AAAY;;EAEdA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;EAUrBA,KAAK,EAAC;AAAe;;EAWvBA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;EAUrBA,KAAK,EAAC;AAAe;;EAWvBA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;EAQrBA,KAAK,EAAC;AAAe;;EAYvBA,KAAK,EAAC;AAAM;;EAWhBA,KAAK,EAAC;AAAc;;;;EAGOA,KAAK,EAAC,uCAAuC;EAACG,IAAI,EAAC;;;;EACrEH,KAAK,EAAC;;;EASrBA,KAAK,EAAC,YAAY;EAACC,EAAE,EAAC,eAAe;EAACC,QAAQ,EAAC,IAAI;EAAC,iBAAe,EAAC,oBAAoB;EAAC,aAAW,EAAC;;;EACnGF,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAe;;EAQnBA,KAAK,EAAC;AAAY;;EAEdA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;EAUrBA,KAAK,EAAC;AAAe;;EAWvBA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;EAUrBA,KAAK,EAAC;AAAe;;EAWvBA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAe;;EAOrBA,KAAK,EAAC;AAAe;;EAUvBA,KAAK,EAAC;AAAM;;EAWhBA,KAAK,EAAC;AAAc;;;;EAGQA,KAAK,EAAC,uCAAuC;EAACG,IAAI,EAAC;;;;EACtEH,KAAK,EAAC;;;EASrBA,KAAK,EAAC,YAAY;EAACC,EAAE,EAAC,eAAe;EAACC,QAAQ,EAAC,IAAI;EAAC,iBAAe,EAAC,oBAAoB;EAAC,aAAW,EAAC;;;EACnGF,KAAK,EAAC;AAAuB;;EAC3BA,KAAK,EAAC;AAAe;;;EAQnBA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAA2B;;EAC/BA,KAAK,EAAC;AAAgC;;;;EAE7BA,KAAK,EAAC;;;EASjBA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAU;;EAElBA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAU;;EAElBA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAU;;EAMlBA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAU;;EAElBA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAU;;EAElBA,KAAK,EAAC;AAAU;;EAEdA,KAAK,EAAC;AAAU;;EAKxBA,KAAK,EAAC;AAAc;;;;uBAjiBjCI,mBAAA,CA2iBM,OA3iBNC,UA2iBM,GA1iBJC,YAAA,CASEC,sBAAA;IARCC,QAAQ,EAAEC,KAAA,CAAAC,SAAS,EAAEC,UAAU;IAC/BC,gBAAgB,EAAEH,KAAA,CAAAG,gBAAgB;IAClCC,gBAAgB,EAAEJ,KAAA,CAAAI,gBAAgB;IAClCC,UAAU,EAAEC,QAAA,CAAAD,UAAU;IACtBE,eAAc,EAAED,QAAA,CAAAE,mBAAmB;IACnCC,oBAAoB,EAAEH,QAAA,CAAAI,wBAAwB;IAC9CC,YAAW,EAAEL,QAAA,CAAAM,gBAAgB;IAC7BC,QAAM,EAAEP,QAAA,CAAAQ;sKAGXC,mBAAA,oBAAuB,EACvBC,mBAAA,CAIO;IAHLzB,KAAK,EAAA0B,eAAA,EAAC,gBAAgB;MAAAC,MAAA,GACHlB,KAAA,CAAAI,gBAAgB,IAAIJ,KAAA,CAAAmB;IAAQ;IAC9CC,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAiB,kBAAA,IAAAjB,QAAA,CAAAiB,kBAAA,IAAAD,IAAA,CAAkB;2BAG5BN,mBAAA,CA4QM,OA5QNQ,UA4QM,GA3QJ3B,YAAA,CAME4B,uBAAA;IALCC,SAAS,EAAE1B,KAAA,CAAAI,gBAAgB;IAC3BC,UAAU,EAAEC,QAAA,CAAAD,UAAU;IACtBsB,YAAW,EAAErB,QAAA,CAAAsB,gBAAgB;IAC7Bf,QAAM,EAAEP,QAAA,CAAAQ,YAAY;IACpBe,eAAc,EAAEvB,QAAA,CAAAE;uGAGnBQ,mBAAA,CAkQO;IAlQDzB,KAAK,EAAA0B,eAAA,EAAC,cAAc;MAAA,qBAAgCjB,KAAA,CAAAI;IAAgB;MACxEY,mBAAA,CAgQM,OAhQNc,UAgQM,GA/PJf,mBAAA,iBAAoB,EACpBC,mBAAA,CAgBM,OAhBNe,UAgBM,GAfJf,mBAAA,CAcM,OAdNgB,UAcM,GAbJhB,mBAAA,CAYM,OAZNiB,UAYM,GAVJjB,mBAAA,CASM,OATNkB,UASM,GARJlB,mBAAA,CAGS;IAHDzB,KAAK,EAAC,gCAAgC;IAAE6B,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAA6B,SAAA,IAAA7B,QAAA,CAAA6B,SAAA,IAAAb,IAAA,CAAS;IAAGc,QAAQ,EAAEpC,KAAA,CAAAqC;MAC3ErB,mBAAA,CAAoE;IAAjEzB,KAAK,EAAA0B,eAAA,EAAC,sBAAsB;MAAA,WAAsBjB,KAAA,CAAAqC;IAAO;wEAAQ,WAEtE,G,8BACArB,mBAAA,CAGS;IAHDzB,KAAK,EAAC,wBAAwB;IAAE6B,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAgC,gBAAA,IAAAhC,QAAA,CAAAgC,gBAAA,IAAAhB,IAAA,CAAgB;kCAC7DN,mBAAA,CAAqC;IAAlCzB,KAAK,EAAC;EAAuB,4B,iBAAK,YAEvC,E,YAMRwB,mBAAA,qBAAwB,EACxBC,mBAAA,CAqEM,OArENuB,UAqEM,GApEJvB,mBAAA,CAgBM,OAhBNwB,WAgBM,GAfJxB,mBAAA,CAcM,OAdNyB,WAcM,GAbJzB,mBAAA,CAYM,OAZN0B,WAYM,GAXJ1B,mBAAA,CAUM,OAVN2B,WAUM,GATJ3B,mBAAA,CAKM,OALN4B,WAKM,G,4BAJJ5B,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAA2D,GAAC,eAEvE,sBACAyB,mBAAA,CAAoF,OAApF6B,WAAoF,EAAAC,gBAAA,CAA7B9C,KAAA,CAAA+C,SAAS,CAACC,KAAK,sB,+BAExEhC,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAU,IACnByB,mBAAA,CAAgD;IAA7CzB,KAAK,EAAC;EAAkC,G,8BAMrDyB,mBAAA,CAgBM,OAhBNiC,WAgBM,GAfJjC,mBAAA,CAcM,OAdNkC,WAcM,GAbJlC,mBAAA,CAYM,OAZNmC,WAYM,GAXJnC,mBAAA,CAUM,OAVNoC,WAUM,GATJpC,mBAAA,CAKM,OALNqC,WAKM,G,4BAJJrC,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAA2D,GAAC,gBAEvE,sBACAyB,mBAAA,CAAqF,OAArFsC,WAAqF,EAAAR,gBAAA,CAA9B9C,KAAA,CAAA+C,SAAS,CAAC7B,MAAM,sB,+BAEzEF,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAU,IACnByB,mBAAA,CAAqD;IAAlDzB,KAAK,EAAC;EAAuC,G,8BAM1DyB,mBAAA,CAgBM,OAhBNuC,WAgBM,GAfJvC,mBAAA,CAcM,OAdNwC,WAcM,GAbJxC,mBAAA,CAYM,OAZNyC,WAYM,GAXJzC,mBAAA,CAUM,OAVN0C,WAUM,GATJ1C,mBAAA,CAKM,OALN2C,WAKM,G,4BAJJ3C,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAA2D,GAAC,wBAEvE,sBACAyB,mBAAA,CAAsF,OAAtF4C,WAAsF,EAAAd,gBAAA,CAA/B9C,KAAA,CAAA+C,SAAS,CAACc,OAAO,sB,+BAE1E7C,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAU,IACnByB,mBAAA,CAAqD;IAAlDzB,KAAK,EAAC;EAAuC,G,8BAM1DyB,mBAAA,CAgBM,OAhBN8C,WAgBM,GAfJ9C,mBAAA,CAcM,OAdN+C,WAcM,GAbJ/C,mBAAA,CAYM,OAZNgD,WAYM,GAXJhD,mBAAA,CAUM,OAVNiD,WAUM,GATJjD,mBAAA,CAKM,OALNkD,WAKM,G,4BAJJlD,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAwD,GAAC,eAEpE,sBACAyB,mBAAA,CAAqF,OAArFmD,WAAqF,EAAArB,gBAAA,CAA9B9C,KAAA,CAAA+C,SAAS,CAACqB,MAAM,sB,+BAEzEpD,mBAAA,CAEM;IAFDzB,KAAK,EAAC;EAAU,IACnByB,mBAAA,CAAsD;IAAnDzB,KAAK,EAAC;EAAwC,G,gCAQ7DwB,mBAAA,iBAAoB,EACpBC,mBAAA,CAkKM,OAlKNqD,WAkKM,GAjKJrD,mBAAA,CAgKM,OAhKNsD,WAgKM,GA/JJtD,mBAAA,CA8JM,OA9JNuD,WA8JM,GA7JJvD,mBAAA,CAmBM,OAnBNwD,WAmBM,G,4BAlBJxD,mBAAA,CAGK;IAHDzB,KAAK,EAAC;EAAmC,IAC3CyB,mBAAA,CAAiC;IAA9BzB,KAAK,EAAC;EAAmB,I,iBAAK,aAEnC,E,sBACAyB,mBAAA,CAaM,OAbNyD,WAaM,G,gBAZJzD,mBAAA,CAMS;IANDzB,KAAK,EAAC,4BAA4B;+DAAUS,KAAA,CAAA0E,YAAY,GAAAC,MAAA;IAAGC,QAAM,EAAAvD,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAuE,WAAA,IAAAvE,QAAA,CAAAuE,WAAA,IAAAvD,IAAA,CAAW;yZAAlCtB,KAAA,CAAA0E,YAAY,E,mBAOhE1D,mBAAA,CAIS;IAJDzB,KAAK,EAAC,4BAA4B;+DAAUS,KAAA,CAAA8E,UAAU,GAAAH,MAAA;IAAGC,QAAM,EAAAvD,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAuE,WAAA,IAAAvE,QAAA,CAAAuE,WAAA,IAAAvD,IAAA,CAAW;kCAClFN,mBAAA,CAAmC;IAA3B+D,KAAK,EAAC;EAAE,GAAC,WAAS,qBAC1B/D,mBAAA,CAAuC;IAA/B+D,KAAK,EAAC;EAAQ,GAAC,SAAO,qBAC9B/D,mBAAA,CAAqC;IAA7B+D,KAAK,EAAC;EAAO,GAAC,QAAM,oB,2DAHsB/E,KAAA,CAAA8E,UAAU,E,OAOlE9D,mBAAA,CAwIM,OAxINgE,WAwIM,GAvIJjE,mBAAA,gBAAmB,EACnBC,mBAAA,CAoBM,OApBNiE,WAoBM,GAnBJjE,mBAAA,CAaM,OAbNkE,WAaM,GAZJlE,mBAAA,CAWM,OAXNmE,WAWM,G,4BAVJnE,mBAAA,CAEO;IAFDzB,KAAK,EAAC;EAAkB,IAC5ByB,mBAAA,CAA6B;IAA1BzB,KAAK,EAAC;EAAe,G,sCAE1ByB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpB8F,WAAW,EAAC,6CAA6C;+DAChDrF,KAAA,CAAAsF,WAAW,GAAAX,MAAA;IACnBY,OAAK,EAAAlE,MAAA,QAAAA,MAAA,UAAAC,IAAA,KAAEhB,QAAA,CAAAkF,WAAA,IAAAlF,QAAA,CAAAkF,WAAA,IAAAlE,IAAA,CAAW;iEADVtB,KAAA,CAAAsF,WAAW,E,OAK1BtE,mBAAA,CAIM,OAJNyE,WAIM,GAHJzE,mBAAA,CAEO,QAFP0E,WAEO,EAFkB,WACf,GAAA5C,gBAAA,CAAG9C,KAAA,CAAA2F,aAAa,CAACC,MAAM,IAAG,MAAI,GAAA9C,gBAAA,CAAG9C,KAAA,CAAA6F,KAAK,CAACD,MAAM,IAAG,SAC1D,gB,KAIJ7E,mBAAA,mBAAsB,EACXf,KAAA,CAAAqC,OAAO,I,cAAlB1C,mBAAA,CAKM,OALNmG,WAKM,EAAAzE,MAAA,SAAAA,MAAA,QAJJL,mBAAA,CAEM;IAFDzB,KAAK,EAAC,6BAA6B;IAACG,IAAI,EAAC;MAC5CsB,mBAAA,CAA+C;IAAzCzB,KAAK,EAAC;EAAiB,GAAC,YAAU,E,qBAE1CyB,mBAAA,CAA+C;IAA5CzB,KAAK,EAAC;EAAiB,GAAC,kBAAgB,oB,MAI7BS,KAAA,CAAA2F,aAAa,CAACC,MAAM,U,cAApCjG,mBAAA,CAMMoG,SAAA;IAAAC,GAAA;EAAA,IAPNjF,mBAAA,iBAAoB,EACpBC,mBAAA,CAMM,OANNiF,WAMM,G,4BALJjF,mBAAA,CAAqD;IAAlDzB,KAAK,EAAC;EAAuC,6B,4BAChDyB,mBAAA,CAA6C;IAAzCzB,KAAK,EAAC;EAAe,GAAC,gBAAc,sBACxCyB,mBAAA,CAEI,KAFJkF,WAEI,EAAApD,gBAAA,CADC9C,KAAA,CAAAsF,WAAW,gG,qEAKlB3F,mBAAA,CAgEMoG,SAAA;IAAAC,GAAA;EAAA,IAjENjF,mBAAA,iBAAoB,EACpBC,mBAAA,CAgEM,OAhENmF,WAgEM,GA/DJnF,mBAAA,CA8DQ,SA9DRoF,WA8DQ,G,4BA7DNpF,mBAAA,CASQ;IATDzB,KAAK,EAAC;EAAa,IACxByB,mBAAA,CAOK,aANHA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAc,YAAV,OAAK,GACTA,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAe,YAAX,QAAM,GACVA,mBAAA,CAAmB,YAAf,YAAU,GACdA,mBAAA,CAAgB,YAAZ,SAAO,E,wBAGfA,mBAAA,CAkDQ,iB,kBAjDNrB,mBAAA,CAgDKoG,SAAA,QAAAM,WAAA,CAhDc/F,QAAA,CAAAgG,cAAc,EAAtBC,IAAI;yBAAf5G,mBAAA,CAgDK;MAhD+BqG,GAAG,EAAEO,IAAI,CAAC/G;QAC5CwB,mBAAA,CAaK,aAZHA,mBAAA,CAWM,OAXNwF,WAWM,GAVJxF,mBAAA,CAKM,OALNyF,WAKM,GAJOF,IAAI,CAACG,eAAe,I,cAA/B/G,mBAAA,CAA0G;;MAAxEgH,GAAG,EAAEJ,IAAI,CAACG,eAAe;MAAGE,GAAG,EAAEL,IAAI,CAACM,SAAS;MAAEtH,KAAK,EAAC;2DACzFI,mBAAA,CAEM,OAFNmH,WAEM,EAAAhE,gBAAA,CADDxC,QAAA,CAAAyG,WAAW,CAACR,IAAI,CAACM,SAAS,mB,GAGjC7F,mBAAA,CAGM,cAFJA,mBAAA,CAA+C,OAA/CgG,WAA+C,EAAAlE,gBAAA,CAAvByD,IAAI,CAACM,SAAS,kBACtC7F,mBAAA,CAAwD,OAAxDiG,WAAwD,EAA1B,GAAC,GAAAnE,gBAAA,CAAGyD,IAAI,CAACW,QAAQ,iB,OAIrDlG,mBAAA,CAAyB,YAAA8B,gBAAA,CAAlByD,IAAI,CAACY,KAAK,kBACjBnG,mBAAA,CAIK,aAHHA,mBAAA,CAEO;MAFDzB,KAAK,EAAA0B,eAAA,EAAC,OAAO,EAASsF,IAAI,CAACnB,IAAI;wBAChCmB,IAAI,CAACnB,IAAI,yD,GAGhBpE,mBAAA,CAIK,aAHHA,mBAAA,CAEO;MAFDzB,KAAK,EAAA0B,eAAA,EAAC,OAAO,EAASX,QAAA,CAAA8G,mBAAmB,CAACb,IAAI,CAACc,MAAM;wBACtD/G,QAAA,CAAAgH,YAAY,CAACf,IAAI,CAACc,MAAM,yB,GAG/BrG,mBAAA,CAA0C,YAAA8B,gBAAA,CAAnCxC,QAAA,CAAAiH,UAAU,CAAChB,IAAI,CAACiB,UAAU,mBACjCxG,mBAAA,CAoBK,aAnBHA,mBAAA,CAkBM,OAlBNyG,WAkBM,GAjBJzG,mBAAA,CAES;MAFDzB,KAAK,EAAC,yBAAyB;MAAE6B,OAAK,EAAAuD,MAAA,IAAErE,QAAA,CAAAoH,QAAQ,CAACnB,IAAI;MAAGoB,KAAK,EAAC;yCACpE3G,mBAAA,CAA0B;MAAvBzB,KAAK,EAAC;IAAY,2B,kCAEvByB,mBAAA,CAES;MAFDzB,KAAK,EAAC,yBAAyB;MAAE6B,OAAK,EAAAuD,MAAA,IAAErE,QAAA,CAAAsH,QAAQ,CAACrB,IAAI;MAAGoB,KAAK,EAAC;yCACpE3G,mBAAA,CAA2B;MAAxBzB,KAAK,EAAC;IAAa,2B,kCAExByB,mBAAA,CAOS;MANPzB,KAAK,EAAA0B,eAAA,EAAC,KAAK,EACHsF,IAAI,CAACc,MAAM;MAClBjG,OAAK,EAAAuD,MAAA,IAAErE,QAAA,CAAAuH,gBAAgB,CAACtB,IAAI;MAC5BoB,KAAK,EAAEpB,IAAI,CAACc,MAAM;QAEnBrG,mBAAA,CAA0E;MAAtEzB,KAAK,EAAA0B,eAAA,CAAEsF,IAAI,CAACc,MAAM;mEAExBrG,mBAAA,CAES;MAFDzB,KAAK,EAAC,wBAAwB;MAAE6B,OAAK,EAAAuD,MAAA,IAAErE,QAAA,CAAAwH,UAAU,CAACvB,IAAI;MAAGoB,KAAK,EAAC;yCACrE3G,mBAAA,CAA4B;MAAzBzB,KAAK,EAAC;IAAc,2B;0FASrCwB,mBAAA,gBAAmB,EACRT,QAAA,CAAAyH,UAAU,Q,cAArBpI,mBAAA,CA0BM,OA1BNqI,WA0BM,GAzBJhH,mBAAA,CAEM,OAFNiH,WAEM,EAFkB,QACjB,GAAAnF,gBAAA,CAAG9C,KAAA,CAAAkI,WAAW,IAAG,MAAI,GAAApF,gBAAA,CAAGxC,QAAA,CAAAyH,UAAU,kBAEzC/G,mBAAA,CAqBM,cApBJA,mBAAA,CAmBK,MAnBLmH,WAmBK,GAlBHnH,mBAAA,CAIK;IAJDzB,KAAK,EAAA0B,eAAA,EAAC,WAAW;MAAAmB,QAAA,EAAqBpC,KAAA,CAAAkI,WAAW;IAAA;MACnDlH,mBAAA,CAES;IAFDzB,KAAK,EAAC,WAAW;IAAE6B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAsD,MAAA,IAAErE,QAAA,CAAA8H,UAAU,CAACpI,KAAA,CAAAkI,WAAW;IAAQ9F,QAAQ,EAAEpC,KAAA,CAAAkI,WAAW;KAAQ,YAE7F,iBAAAG,WAAA,E,qCAEF1I,mBAAA,CAOKoG,SAAA,QAAAM,WAAA,CANY/F,QAAA,CAAAgI,YAAY,EAApBC,IAAI;yBADb5I,mBAAA,CAOK;MALFqG,GAAG,EAAEuC,IAAI;MACVhJ,KAAK,EAAA0B,eAAA,EAAC,WAAW;QAAAC,MAAA,EACCqH,IAAI,KAAKvI,KAAA,CAAAkI;MAAW;QAEtClH,mBAAA,CAAuE;MAA/DzB,KAAK,EAAC,WAAW;MAAE6B,OAAK,EAAAuD,MAAA,IAAErE,QAAA,CAAA8H,UAAU,CAACG,IAAI;wBAAMA,IAAI,wBAAAC,WAAA,E;kCAE7DxH,mBAAA,CAIK;IAJDzB,KAAK,EAAA0B,eAAA,EAAC,WAAW;MAAAmB,QAAA,EAAqBpC,KAAA,CAAAkI,WAAW,KAAK5H,QAAA,CAAAyH;IAAU;MAClE/G,mBAAA,CAES;IAFDzB,KAAK,EAAC,WAAW;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAsD,MAAA,IAAErE,QAAA,CAAA8H,UAAU,CAACpI,KAAA,CAAAkI,WAAW;IAAQ9F,QAAQ,EAAEpC,KAAA,CAAAkI,WAAW,KAAK5H,QAAA,CAAAyH;KAAY,QAEtG,iBAAAU,WAAA,E,0FAatB1H,mBAAA,oBAAuB,EACvBC,mBAAA,CAkGM,OAlGN0H,WAkGM,GAjGJ1H,mBAAA,CAgGM,OAhGN2H,WAgGM,GA/FJ3H,mBAAA,CA8FM,OA9FN4H,WA8FM,G,4BA7FJ5H,mBAAA,CAMM;IANDzB,KAAK,EAAC;EAAc,IACvByB,mBAAA,CAGK;IAHDzB,KAAK,EAAC,aAAa;IAACC,EAAE,EAAC;MACzBwB,mBAAA,CAAqC;IAAlCzB,KAAK,EAAC;EAAuB,I,iBAAK,gBAEvC,E,GACAyB,mBAAA,CAA4F;IAApFoE,IAAI,EAAC,QAAQ;IAAC7F,KAAK,EAAC,WAAW;IAAC,iBAAe,EAAC,OAAO;IAAC,YAAU,EAAC;2BAE7EyB,mBAAA,CA6EM,OA7EN6H,WA6EM,GA5EJ7H,mBAAA,CA2EO;IA3EA8H,QAAM,EAAAzH,MAAA,SAAAA,MAAA,OAAA0H,cAAA,KAAAzH,IAAA,KAAUhB,QAAA,CAAA0I,aAAA,IAAA1I,QAAA,CAAA0I,aAAA,IAAA1H,IAAA,CAAa;MAClCN,mBAAA,CAqBM,OArBNiI,WAqBM,GApBJjI,mBAAA,CASM,OATNkI,WASM,G,4BARJlI,mBAAA,CAA8D;IAAvDmI,GAAG,EAAC,aAAa;IAAC5J,KAAK,EAAC;KAAa,YAAU,sB,gBACtDyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,aAAa;iEACPQ,KAAA,CAAAoJ,WAAW,CAAClC,QAAQ,GAAAvC,MAAA;IAC7B0E,QAAQ,EAAR;iDADSrJ,KAAA,CAAAoJ,WAAW,CAAClC,QAAQ,E,KAIjClG,mBAAA,CASM,OATNsI,WASM,G,4BARJtI,mBAAA,CAAwD;IAAjDmI,GAAG,EAAC,UAAU;IAAC5J,KAAK,EAAC;KAAa,SAAO,sB,gBAChDyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,OAAO;IACZ7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,UAAU;iEACJQ,KAAA,CAAAoJ,WAAW,CAACjC,KAAK,GAAAxC,MAAA;IAC1B0E,QAAQ,EAAR;iDADSrJ,KAAA,CAAAoJ,WAAW,CAACjC,KAAK,E,OAKhCnG,mBAAA,CAqBM,OArBNuI,WAqBM,GApBJvI,mBAAA,CASM,OATNwI,WASM,G,4BARJxI,mBAAA,CAAiE;IAA1DmI,GAAG,EAAC,cAAc;IAAC5J,KAAK,EAAC;KAAa,cAAY,sB,gBACzDyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,cAAc;iEACRQ,KAAA,CAAAoJ,WAAW,CAAClJ,UAAU,GAAAyE,MAAA;IAC/B0E,QAAQ,EAAR;iDADSrJ,KAAA,CAAAoJ,WAAW,CAAClJ,UAAU,E,KAInCc,mBAAA,CASM,OATNyI,WASM,G,4BARJzI,mBAAA,CAA+D;IAAxDmI,GAAG,EAAC,aAAa;IAAC5J,KAAK,EAAC;KAAa,aAAW,sB,gBACvDyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,aAAa;iEACPQ,KAAA,CAAAoJ,WAAW,CAACM,SAAS,GAAA/E,MAAA;IAC9B0E,QAAQ,EAAR;iDADSrJ,KAAA,CAAAoJ,WAAW,CAACM,SAAS,E,OAKpC1I,mBAAA,CAoBM,OApBN2I,WAoBM,GAnBJ3I,mBAAA,CAOM,OAPN4I,WAOM,G,4BANJ5I,mBAAA,CAAsD;IAA/CmI,GAAG,EAAC,SAAS;IAAC5J,KAAK,EAAC;KAAa,QAAM,sB,gBAC9CyB,mBAAA,CAIS;IAJDzB,KAAK,EAAC,aAAa;IAACC,EAAE,EAAC,SAAS;iEAAUQ,KAAA,CAAAoJ,WAAW,CAAC1J,IAAI,GAAAiF,MAAA;IAAE0E,QAAQ,EAAR;kCAClErI,mBAAA,CAAqC;IAA7B+D,KAAK,EAAC;EAAE,GAAC,aAAW,qBAC5B/D,mBAAA,CAA4C;IAApC+D,KAAK,EAAC;EAAO,GAAC,eAAa,qBACnC/D,mBAAA,CAAsC;IAA9B+D,KAAK,EAAC;EAAQ,GAAC,QAAM,oB,2CAHmB/E,KAAA,CAAAoJ,WAAW,CAAC1J,IAAI,E,KAMpEsB,mBAAA,CAUM,OAVN6I,WAUM,G,4BATJ7I,mBAAA,CAA8D;IAAvDmI,GAAG,EAAC,aAAa;IAAC5J,KAAK,EAAC;KAAa,YAAU,sB,gBACtDyB,mBAAA,CAOC;IANCoE,IAAI,EAAC,UAAU;IACf7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,aAAa;iEACPQ,KAAA,CAAAoJ,WAAW,CAACU,QAAQ,GAAAnF,MAAA;IAC7B0E,QAAQ,EAAR,EAAQ;IACRU,SAAS,EAAC;iDAFD/J,KAAA,CAAAoJ,WAAW,CAACU,QAAQ,E,OAMnC9I,mBAAA,CAQM,OARNgJ,WAQM,G,4BAPJhJ,mBAAA,CAA6D;IAAtDmI,GAAG,EAAC,UAAU;IAAC5J,KAAK,EAAC;KAAa,cAAY,sB,gBACrDyB,mBAAA,CAKC;IAJCoE,IAAI,EAAC,KAAK;IACV7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,UAAU;iEACJQ,KAAA,CAAAoJ,WAAW,CAACa,YAAY,GAAAtF,MAAA;iDAAxB3E,KAAA,CAAAoJ,WAAW,CAACa,YAAY,E,kCAKzCjJ,mBAAA,CAOM,OAPNkJ,WAOM,G,4BANJlJ,mBAAA,CAAuF;IAA/EoE,IAAI,EAAC,QAAQ;IAAC7F,KAAK,EAAC,mBAAmB;IAAC,iBAAe,EAAC;KAAQ,QAAM,sBAC9EyB,mBAAA,CAIS;IAJDoE,IAAI,EAAC,QAAQ;IAAC7F,KAAK,EAAC,iBAAiB;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAA0I,aAAA,IAAA1I,QAAA,CAAA0I,aAAA,IAAA1H,IAAA,CAAa;IAAGc,QAAQ,EAAEpC,KAAA,CAAAmK;MAClEnK,KAAA,CAAAmK,cAAc,I,cAA1BxK,mBAAA,CAA+F,QAA/FyK,WAA+F,M,cAC/FzK,mBAAA,CAAuC,KAAvC0K,WAAuC,I,iBAAA,GACvC,GAAAvH,gBAAA,CAAG9C,KAAA,CAAAmK,cAAc,iD,uCAO3BpJ,mBAAA,qBAAwB,EACxBC,mBAAA,CA+FM,OA/FNsJ,WA+FM,GA9FJtJ,mBAAA,CA6FM,OA7FNuJ,WA6FM,GA5FJvJ,mBAAA,CA2FM,OA3FNwJ,WA2FM,G,4BA1FJxJ,mBAAA,CAMM;IANDzB,KAAK,EAAC;EAAc,IACvByB,mBAAA,CAGK;IAHDzB,KAAK,EAAC,aAAa;IAACC,EAAE,EAAC;MACzBwB,mBAAA,CAAqC;IAAlCzB,KAAK,EAAC;EAAuB,I,iBAAK,aAEvC,E,GACAyB,mBAAA,CAA4F;IAApFoE,IAAI,EAAC,QAAQ;IAAC7F,KAAK,EAAC,WAAW;IAAC,iBAAe,EAAC,OAAO;IAAC,YAAU,EAAC;2BAE7EyB,mBAAA,CA0EM,OA1ENyJ,WA0EM,GAzEyCzK,KAAA,CAAA0K,YAAY,CAAClL,EAAE,I,cAA5DG,mBAAA,CAwEO;;IAxEAmJ,QAAM,EAAAzH,MAAA,SAAAA,MAAA,OAAA0H,cAAA,KAAAzH,IAAA,KAAUhB,QAAA,CAAAqK,cAAA,IAAArK,QAAA,CAAAqK,cAAA,IAAArJ,IAAA,CAAc;MACnCN,mBAAA,CAqBM,OArBN4J,WAqBM,GApBJ5J,mBAAA,CASM,OATN6J,WASM,G,4BARJ7J,mBAAA,CAA+D;IAAxDmI,GAAG,EAAC,cAAc;IAAC5J,KAAK,EAAC;KAAa,YAAU,sB,gBACvDyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,cAAc;iEACRQ,KAAA,CAAA0K,YAAY,CAACxD,QAAQ,GAAAvC,MAAA;IAC9B0E,QAAQ,EAAR;iDADSrJ,KAAA,CAAA0K,YAAY,CAACxD,QAAQ,E,KAIlClG,mBAAA,CASM,OATN8J,WASM,G,4BARJ9J,mBAAA,CAAyD;IAAlDmI,GAAG,EAAC,WAAW;IAAC5J,KAAK,EAAC;KAAa,SAAO,sB,gBACjDyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,OAAO;IACZ7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,WAAW;iEACLQ,KAAA,CAAA0K,YAAY,CAACvD,KAAK,GAAAxC,MAAA;IAC3B0E,QAAQ,EAAR;iDADSrJ,KAAA,CAAA0K,YAAY,CAACvD,KAAK,E,OAKjCnG,mBAAA,CAqBM,OArBN+J,WAqBM,GApBJ/J,mBAAA,CASM,OATNgK,WASM,G,4BARJhK,mBAAA,CAAkE;IAA3DmI,GAAG,EAAC,eAAe;IAAC5J,KAAK,EAAC;KAAa,cAAY,sB,gBAC1DyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,eAAe;iEACTQ,KAAA,CAAA0K,YAAY,CAACxK,UAAU,GAAAyE,MAAA;IAChC0E,QAAQ,EAAR;iDADSrJ,KAAA,CAAA0K,YAAY,CAACxK,UAAU,E,KAIpCc,mBAAA,CASM,OATNiK,WASM,G,4BARJjK,mBAAA,CAAgE;IAAzDmI,GAAG,EAAC,cAAc;IAAC5J,KAAK,EAAC;KAAa,aAAW,sB,gBACxDyB,mBAAA,CAMC;IALCoE,IAAI,EAAC,MAAM;IACX7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,cAAc;iEACRQ,KAAA,CAAA0K,YAAY,CAAChB,SAAS,GAAA/E,MAAA;IAC/B0E,QAAQ,EAAR;iDADSrJ,KAAA,CAAA0K,YAAY,CAAChB,SAAS,E,OAKrC1I,mBAAA,CAiBM,OAjBNkK,WAiBM,GAhBJlK,mBAAA,CAMM,OANNmK,WAMM,G,4BALJnK,mBAAA,CAAuD;IAAhDmI,GAAG,EAAC,UAAU;IAAC5J,KAAK,EAAC;KAAa,QAAM,sB,gBAC/CyB,mBAAA,CAGS;IAHDzB,KAAK,EAAC,aAAa;IAACC,EAAE,EAAC,UAAU;iEAAUQ,KAAA,CAAA0K,YAAY,CAAChL,IAAI,GAAAiF,MAAA;IAAE0E,QAAQ,EAAR;kCACpErI,mBAAA,CAA4C;IAApC+D,KAAK,EAAC;EAAO,GAAC,eAAa,qBACnC/D,mBAAA,CAAsC;IAA9B+D,KAAK,EAAC;EAAQ,GAAC,QAAM,oB,2CAFoB/E,KAAA,CAAA0K,YAAY,CAAChL,IAAI,E,KAKtEsB,mBAAA,CAQM,OARNoK,WAQM,G,4BAPJpK,mBAAA,CAA2D;IAApDmI,GAAG,EAAC,YAAY;IAAC5J,KAAK,EAAC;KAAa,UAAQ,sB,gBACnDyB,mBAAA,CAKS;IALDzB,KAAK,EAAC,aAAa;IAACC,EAAE,EAAC,YAAY;iEAAUQ,KAAA,CAAA0K,YAAY,CAACrD,MAAM,GAAA1C,MAAA;IAAE0E,QAAQ,EAAR;kCACxErI,mBAAA,CAAsC;IAA9B+D,KAAK,EAAC;EAAQ,GAAC,QAAM,qBAC7B/D,mBAAA,CAA0C;IAAlC+D,KAAK,EAAC;EAAU,GAAC,UAAQ,qBACjC/D,mBAAA,CAA4C;IAApC+D,KAAK,EAAC;EAAW,GAAC,WAAS,qBACnC/D,mBAAA,CAAwC;IAAhC+D,KAAK,EAAC;EAAS,GAAC,SAAO,oB,2CAJoB/E,KAAA,CAAA0K,YAAY,CAACrD,MAAM,E,OAQ5ErG,mBAAA,CAQM,OARNqK,WAQM,G,4BAPJrK,mBAAA,CAA8D;IAAvDmI,GAAG,EAAC,WAAW;IAAC5J,KAAK,EAAC;KAAa,cAAY,sB,gBACtDyB,mBAAA,CAKC;IAJCoE,IAAI,EAAC,KAAK;IACV7F,KAAK,EAAC,cAAc;IACpBC,EAAE,EAAC,WAAW;iEACLQ,KAAA,CAAA0K,YAAY,CAACT,YAAY,GAAAtF,MAAA;iDAAzB3E,KAAA,CAAA0K,YAAY,CAACT,YAAY,E,uEAK1CjJ,mBAAA,CAOM,OAPNsK,WAOM,G,4BANJtK,mBAAA,CAAuF;IAA/EoE,IAAI,EAAC,QAAQ;IAAC7F,KAAK,EAAC,mBAAmB;IAAC,iBAAe,EAAC;KAAQ,QAAM,sBAC9EyB,mBAAA,CAIS;IAJDoE,IAAI,EAAC,QAAQ;IAAC7F,KAAK,EAAC,iBAAiB;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,WAAAC,IAAA,KAAEhB,QAAA,CAAAqK,cAAA,IAAArK,QAAA,CAAAqK,cAAA,IAAArJ,IAAA,CAAc;IAAGc,QAAQ,EAAEpC,KAAA,CAAAuL;MACnEvL,KAAA,CAAAuL,eAAe,I,cAA3B5L,mBAAA,CAAgG,QAAhG6L,YAAgG,M,cAChG7L,mBAAA,CAAuC,KAAvC8L,YAAuC,I,iBAAA,GACvC,GAAA3I,gBAAA,CAAG9C,KAAA,CAAAuL,eAAe,iD,wCAO5BxK,mBAAA,qBAAwB,EACxBC,mBAAA,CAiEM,OAjEN0K,YAiEM,GAhEJ1K,mBAAA,CA+DM,OA/DN2K,YA+DM,GA9DJ3K,mBAAA,CA6DM,OA7DN4K,YA6DM,G,4BA5DJ5K,mBAAA,CAMM;IANDzB,KAAK,EAAC;EAAc,IACvByB,mBAAA,CAGK;IAHDzB,KAAK,EAAC,aAAa;IAACC,EAAE,EAAC;MACzBwB,mBAAA,CAAgC;IAA7BzB,KAAK,EAAC;EAAkB,I,iBAAK,gBAElC,E,GACAyB,mBAAA,CAA4F;IAApFoE,IAAI,EAAC,QAAQ;IAAC7F,KAAK,EAAC,WAAW;IAAC,iBAAe,EAAC,OAAO;IAAC,YAAU,EAAC;2BAE/CS,KAAA,CAAA6L,YAAY,I,cAA1ClM,mBAAA,CA6CM,OA7CNmM,YA6CM,GA5CJ9K,mBAAA,CA2CM,OA3CN+K,YA2CM,GA1CJ/K,mBAAA,CAWM,OAXNgL,YAWM,GAVJhL,mBAAA,CAKM,OALNiL,YAKM,GAJOjM,KAAA,CAAA6L,YAAY,CAACnF,eAAe,I,cAAvC/G,mBAAA,CAAkI;;IAAxFgH,GAAG,EAAE3G,KAAA,CAAA6L,YAAY,CAACnF,eAAe;IAAGE,GAAG,EAAE5G,KAAA,CAAA6L,YAAY,CAAChF,SAAS;IAAEtH,KAAK,EAAC;0DACjHI,mBAAA,CAEM,OAFNuM,YAEM,EAAApJ,gBAAA,CADDxC,QAAA,CAAAyG,WAAW,CAAC/G,KAAA,CAAA6L,YAAY,CAAChF,SAAS,mB,GAGzC7F,mBAAA,CAAqC,YAAA8B,gBAAA,CAA9B9C,KAAA,CAAA6L,YAAY,CAAChF,SAAS,kBAC7B7F,mBAAA,CAEO;IAFDzB,KAAK,EAAA0B,eAAA,EAAC,OAAO,EAASX,QAAA,CAAA8G,mBAAmB,CAACpH,KAAA,CAAA6L,YAAY,CAACxE,MAAM;sBAC9D/G,QAAA,CAAAgH,YAAY,CAACtH,KAAA,CAAA6L,YAAY,CAACxE,MAAM,yB,GAGvCrG,mBAAA,CA6BM,OA7BNmL,YA6BM,GA5BJnL,mBAAA,CAGM,OAHNoL,YAGM,G,4BAFJpL,mBAAA,CAAsD;IAAjDzB,KAAK,EAAC;EAAU,IAACyB,mBAAA,CAA0B,gBAAlB,WAAS,E,sBACvCA,mBAAA,CAAuD,OAAvDqL,YAAuD,EAAAvJ,gBAAA,CAA9B9C,KAAA,CAAA6L,YAAY,CAAC3E,QAAQ,iB,GAEhDlG,mBAAA,CAGM,OAHNsL,YAGM,G,4BAFJtL,mBAAA,CAAmD;IAA9CzB,KAAK,EAAC;EAAU,IAACyB,mBAAA,CAAuB,gBAAf,QAAM,E,sBACpCA,mBAAA,CAAoD,OAApDuL,YAAoD,EAAAzJ,gBAAA,CAA3B9C,KAAA,CAAA6L,YAAY,CAAC1E,KAAK,iB,GAE7CnG,mBAAA,CAOM,OAPNwL,YAOM,G,4BANJxL,mBAAA,CAAkD;IAA7CzB,KAAK,EAAC;EAAU,IAACyB,mBAAA,CAAsB,gBAAd,OAAK,E,sBACnCA,mBAAA,CAIM,OAJNyL,YAIM,GAHJzL,mBAAA,CAEO;IAFDzB,KAAK,EAAA0B,eAAA,EAAC,OAAO,EAASjB,KAAA,CAAA6L,YAAY,CAACzG,IAAI;sBACxCpF,KAAA,CAAA6L,YAAY,CAACzG,IAAI,iE,KAI1BpE,mBAAA,CAGM,OAHN0L,YAGM,G,4BAFJ1L,mBAAA,CAAmD;IAA9CzB,KAAK,EAAC;EAAU,IAACyB,mBAAA,CAAuB,gBAAf,QAAM,E,sBACpCA,mBAAA,CAAoE,OAApE2L,YAAoE,EAAA7J,gBAAA,CAA3C9C,KAAA,CAAA6L,YAAY,CAAC5B,YAAY,0B,GAEpDjJ,mBAAA,CAGM,OAHN4L,YAGM,G,4BAFJ5L,mBAAA,CAAwD;IAAnDzB,KAAK,EAAC;EAAU,IAACyB,mBAAA,CAA4B,gBAApB,aAAW,E,sBACzCA,mBAAA,CAAqE,OAArE6L,YAAqE,EAAA/J,gBAAA,CAA5CxC,QAAA,CAAAiH,UAAU,CAACvH,KAAA,CAAA6L,YAAY,CAACrE,UAAU,kB,GAE7DxG,mBAAA,CAGM,OAHN8L,YAGM,G,4BAFJ9L,mBAAA,CAAwD;IAAnDzB,KAAK,EAAC;EAAU,IAACyB,mBAAA,CAA4B,gBAApB,aAAW,E,sBACzCA,mBAAA,CAAyG,OAAzG+L,YAAyG,EAAAjK,gBAAA,CAAhF9C,KAAA,CAAA6L,YAAY,CAACmB,UAAU,GAAG1M,QAAA,CAAAiH,UAAU,CAACvH,KAAA,CAAA6L,YAAY,CAACmB,UAAU,4B,8CAK7FhM,mBAAA,CAMM,OANNiM,YAMM,G,4BALJjM,mBAAA,CAAsF;IAA9EoE,IAAI,EAAC,QAAQ;IAAC7F,KAAK,EAAC,mBAAmB;IAAC,iBAAe,EAAC;KAAQ,OAAK,sBAC7EyB,mBAAA,CAGS;IAHDoE,IAAI,EAAC,QAAQ;IAAC7F,KAAK,EAAC,iBAAiB;IAAE6B,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAsD,MAAA,IAAErE,QAAA,CAAAsH,QAAQ,CAAC5H,KAAA,CAAA6L,YAAY;IAAG,iBAAe,EAAC;kCAC5F7K,mBAAA,CAAgC;IAA7BzB,KAAK,EAAC;EAAkB,4B,iBAAK,aAElC,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}