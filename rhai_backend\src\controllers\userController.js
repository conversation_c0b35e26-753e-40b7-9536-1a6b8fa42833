const UserService = require('../services/userServiceNew');

class UserController {
  // @desc    Get all users
  // @route   GET /api/users
  // @access  Private/Admin
  static async getAllUsers(req, res, next) {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const role = req.query.role;
      const search = req.query.search;
      const is_active = req.query.is_active;

      const filters = {};
      if (role) filters.role = role;
      if (search) filters.search = search;
      if (is_active !== undefined) filters.is_active = is_active === 'true';

      const result = await UserService.getAllUsers(page, limit, filters);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  // @desc    Get single user
  // @route   GET /api/users/:id
  // @access  Private/Admin
  static async getUser(req, res, next) {
    try {
      const userId = req.params.id;
      const result = await UserService.getUserById(userId);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  // @desc    Create user
  // @route   POST /api/users
  // @access  Private/Admin
  static async createUser(req, res, next) {
    try {
      const { email, password, first_name, last_name, role } = req.body;
      
      const result = await UserService.createUser({
        email,
        password,
        first_name,
        last_name,
        role
      });

      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }

  // @desc    Update user
  // @route   PUT /api/users/:id
  // @access  Private/Admin
  static async updateUser(req, res, next) {
    try {
      const userId = req.params.id;
      const { email, first_name, last_name, role, is_active } = req.body;
      
      const result = await UserService.updateUser(userId, {
        email,
        first_name,
        last_name,
        role,
        is_active
      });

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  // @desc    Delete user
  // @route   DELETE /api/users/:id
  // @access  Private/Admin
  static async deleteUser(req, res, next) {
    try {
      const userId = req.params.id;
      
      // Prevent admin from deleting themselves
      if (userId == req.user.id) {
        return res.status(400).json({
          success: false,
          error: 'You cannot delete your own account'
        });
      }

      const result = await UserService.deleteUser(userId);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  // @desc    Toggle user status
  // @route   PATCH /api/users/:id/toggle-status
  // @access  Private/Admin
  static async toggleUserStatus(req, res, next) {
    try {
      const userId = req.params.id;
      
      // Prevent admin from deactivating themselves
      if (userId == req.user.id) {
        return res.status(400).json({
          success: false,
          error: 'You cannot deactivate your own account'
        });
      }

      const result = await UserService.toggleUserStatus(userId);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  // @desc    Get users by role
  // @route   GET /api/users/role/:role
  // @access  Private/Admin
  static async getUsersByRole(req, res, next) {
    try {
      const role = req.params.role;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      const result = await UserService.getUsersByRole(role, page, limit);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  // @desc    Search users
  // @route   GET /api/users/search
  // @access  Private/Admin
  static async searchUsers(req, res, next) {
    try {
      const searchTerm = req.query.q;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      if (!searchTerm) {
        return res.status(400).json({
          success: false,
          error: 'Search term is required'
        });
      }

      const result = await UserService.searchUsers(searchTerm, page, limit);
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  // @desc    Get user statistics
  // @route   GET /api/users/stats
  // @access  Private/Admin
  static async getUserStats(req, res, next) {
    try {
      const result = await UserService.getUserStats();
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }
}

module.exports = UserController;
