const UserController = require('./src/controllers/userController');

// Mock request and response objects
const mockReq = {
  query: {},
  params: {},
  body: {},
  user: { id: 32, role: 'admin' } // Mock admin user
};

const mockRes = {
  status: function(code) {
    this.statusCode = code;
    return this;
  },
  json: function(data) {
    this.data = data;
    console.log(`Response ${this.statusCode}:`, JSON.stringify(data, null, 2));
    return this;
  }
};

const mockNext = (error) => {
  if (error) {
    console.error('Controller error:', error);
  }
};

async function testControllerDirect() {
  console.log('🔍 Testing UserController directly...');
  
  try {
    console.log('\n1. Testing getAllUsers...');
    await UserController.getAllUsers(mockReq, mockRes, mockNext);
    
    console.log('\n2. Testing getUserStats...');
    await UserController.getUserStats(mockReq, mockRes, mockNext);
    
  } catch (error) {
    console.error('❌ Error testing controller:', error);
  }
  
  process.exit(0);
}

testControllerDirect();
