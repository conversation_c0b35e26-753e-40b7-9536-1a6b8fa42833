{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport documentRequestService from '@/services/documentRequestService';\nimport notificationService from '@/services/notificationService';\nexport default {\n  name: 'MyRequests',\n  data() {\n    return {\n      requests: [],\n      loading: true,\n      error: null,\n      // Filters\n      searchQuery: '',\n      statusFilter: '',\n      typeFilter: '',\n      sortBy: 'created_at_desc',\n      // Pagination\n      currentPage: 1,\n      itemsPerPage: 12,\n      // Search debounce\n      searchTimeout: null\n    };\n  },\n  computed: {\n    filteredRequests() {\n      // Ensure requests is always an array\n      if (!Array.isArray(this.requests)) {\n        return [];\n      }\n      let filtered = [...this.requests];\n\n      // Apply search filter\n      if (this.searchQuery) {\n        const query = this.searchQuery.toLowerCase();\n        filtered = filtered.filter(request => request.document_type.toLowerCase().includes(query) || request.purpose_category?.toLowerCase().includes(query) || request.id.toString().includes(query));\n      }\n\n      // Apply status filter\n      if (this.statusFilter) {\n        filtered = filtered.filter(request => request.status === this.statusFilter);\n      }\n\n      // Apply type filter\n      if (this.typeFilter) {\n        filtered = filtered.filter(request => request.document_type === this.typeFilter);\n      }\n\n      // Apply sorting\n      filtered.sort((a, b) => {\n        switch (this.sortBy) {\n          case 'created_at_desc':\n            return new Date(b.created_at) - new Date(a.created_at);\n          case 'created_at_asc':\n            return new Date(a.created_at) - new Date(b.created_at);\n          case 'status':\n            return a.status.localeCompare(b.status);\n          case 'type':\n            return a.document_type.localeCompare(b.document_type);\n          default:\n            return 0;\n        }\n      });\n      return filtered;\n    },\n    paginatedRequests() {\n      const start = (this.currentPage - 1) * this.itemsPerPage;\n      const end = start + this.itemsPerPage;\n      return this.filteredRequests.slice(start, end);\n    },\n    totalPages() {\n      return Math.ceil(this.filteredRequests.length / this.itemsPerPage);\n    },\n    visiblePages() {\n      const pages = [];\n      const start = Math.max(1, this.currentPage - 2);\n      const end = Math.min(this.totalPages, this.currentPage + 2);\n      for (let i = start; i <= end; i++) {\n        pages.push(i);\n      }\n      return pages;\n    },\n    totalRequests() {\n      return this.requests.length;\n    },\n    pendingCount() {\n      return this.requests.filter(r => ['pending', 'under_review'].includes(r.status)).length;\n    },\n    completedCount() {\n      return this.requests.filter(r => r.status === 'completed').length;\n    },\n    totalSpent() {\n      return this.requests.filter(r => r.status === 'completed').reduce((sum, r) => sum + parseFloat(r.total_fee || 0), 0);\n    }\n  },\n  async mounted() {\n    await this.loadRequests();\n    await this.initializeRealTimeFeatures();\n  },\n  beforeUnmount() {\n    this.cleanupRealTimeFeatures();\n  },\n  methods: {\n    async loadRequests() {\n      try {\n        this.loading = true;\n        this.error = null;\n        const response = await documentRequestService.getMyRequests();\n\n        // Handle the nested response structure: response.data.requests\n        if (response && response.data && response.data.requests && Array.isArray(response.data.requests)) {\n          this.requests = response.data.requests;\n        } else if (response && response.data && Array.isArray(response.data)) {\n          this.requests = response.data;\n        } else if (response && Array.isArray(response)) {\n          this.requests = response;\n        } else {\n          this.requests = [];\n          console.warn('API response does not contain array data:', response);\n        }\n      } catch (error) {\n        console.error('Error loading requests:', error);\n        this.error = error.response?.data?.message || 'Failed to load requests';\n        this.requests = []; // Ensure requests is always an array even on error\n      } finally {\n        this.loading = false;\n      }\n    },\n    handleSearch() {\n      // Debounce search\n      if (this.searchTimeout) {\n        clearTimeout(this.searchTimeout);\n      }\n      this.searchTimeout = setTimeout(() => {\n        this.currentPage = 1; // Reset to first page when searching\n      }, 300);\n    },\n    applyFilters() {\n      this.currentPage = 1; // Reset to first page when filtering\n    },\n    changePage(page) {\n      if (page >= 1 && page <= this.totalPages) {\n        this.currentPage = page;\n      }\n    },\n    getDocumentIcon(type) {\n      const icons = {\n        'Barangay Clearance': 'fas fa-certificate',\n        'Cedula': 'fas fa-id-card'\n      };\n      return icons[type] || 'fas fa-file-alt';\n    },\n    getStatusClass(status) {\n      const classes = {\n        'pending': 'status-pending',\n        'under_review': 'status-review',\n        'approved': 'status-approved',\n        'processing': 'status-processing',\n        'ready_for_pickup': 'status-ready',\n        'completed': 'status-completed',\n        'rejected': 'status-rejected',\n        'cancelled': 'status-cancelled'\n      };\n      return classes[status] || 'status-unknown';\n    },\n    formatStatus(status) {\n      const statusMap = {\n        'pending': 'Pending',\n        'under_review': 'Under Review',\n        'approved': 'Approved',\n        'processing': 'Processing',\n        'ready_for_pickup': 'Ready for Pickup',\n        'completed': 'Completed',\n        'rejected': 'Rejected',\n        'cancelled': 'Cancelled'\n      };\n      return statusMap[status] || status;\n    },\n    getProgressPercentage(status) {\n      const percentages = {\n        'pending': 10,\n        'under_review': 25,\n        'approved': 50,\n        'processing': 75,\n        'ready_for_pickup': 90,\n        'completed': 100,\n        'rejected': 0,\n        'cancelled': 0\n      };\n      return percentages[status] || 0;\n    },\n    getProgressText(status) {\n      const texts = {\n        'pending': 'Waiting for review',\n        'under_review': 'Being reviewed',\n        'approved': 'Approved, processing payment',\n        'processing': 'Document being prepared',\n        'ready_for_pickup': 'Ready for pickup',\n        'completed': 'Request completed',\n        'rejected': 'Request rejected',\n        'cancelled': 'Request cancelled'\n      };\n      return texts[status] || 'Unknown status';\n    },\n    canCancelRequest(status) {\n      return ['pending', 'under_review'].includes(status);\n    },\n    needsPayment(request) {\n      return request.status === 'approved' && !request.payment_status;\n    },\n    formatDate(dateString) {\n      if (!dateString) return 'N/A';\n      return new Date(dateString).toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    },\n    formatCurrency(amount) {\n      return parseFloat(amount || 0).toFixed(2);\n    },\n    viewRequestDetails(requestId) {\n      this.$router.push({\n        name: 'RequestDetails',\n        params: {\n          id: requestId\n        }\n      });\n    },\n    async cancelRequest(requestId) {\n      if (!confirm('Are you sure you want to cancel this request?')) return;\n      try {\n        await documentRequestService.cancelRequest(requestId, 'Cancelled by user');\n        this.$toast?.success('Request cancelled successfully');\n        await this.loadRequests(); // Reload requests\n      } catch (error) {\n        console.error('Error cancelling request:', error);\n        this.$toast?.error('Failed to cancel request');\n      }\n    },\n    processPayment(requestId) {\n      // TODO: Implement payment processing\n      console.log('Process payment for request:', requestId);\n    },\n    createNewRequest() {\n      this.$router.push({\n        name: 'NewDocumentRequest'\n      });\n    },\n    goBack() {\n      this.$router.push({\n        name: 'ClientDashboard'\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["documentRequestService", "notificationService", "name", "data", "requests", "loading", "error", "searchQuery", "statusFilter", "typeFilter", "sortBy", "currentPage", "itemsPerPage", "searchTimeout", "computed", "filteredRequests", "Array", "isArray", "filtered", "query", "toLowerCase", "filter", "request", "document_type", "includes", "purpose_category", "id", "toString", "status", "sort", "a", "b", "Date", "created_at", "localeCompare", "paginatedRequests", "start", "end", "slice", "totalPages", "Math", "ceil", "length", "visiblePages", "pages", "max", "min", "i", "push", "totalRequests", "pendingCount", "r", "completedCount", "totalSpent", "reduce", "sum", "parseFloat", "total_fee", "mounted", "loadRequests", "initializeRealTimeFeatures", "beforeUnmount", "cleanupRealTimeFeatures", "methods", "response", "getMyRequests", "console", "warn", "message", "handleSearch", "clearTimeout", "setTimeout", "applyFilters", "changePage", "page", "getDocumentIcon", "type", "icons", "getStatusClass", "classes", "formatStatus", "statusMap", "getProgressPercentage", "percentages", "getProgressText", "texts", "canCancelRequest", "needsPayment", "payment_status", "formatDate", "dateString", "toLocaleDateString", "year", "month", "day", "formatCurrency", "amount", "toFixed", "viewRequestDetails", "requestId", "$router", "params", "cancelRequest", "confirm", "$toast", "success", "processPayment", "log", "createNewRequest", "goBack"], "sources": ["D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\MyRequests.vue"], "sourcesContent": ["<template>\n  <div class=\"my-requests-page\">\n    <!-- Header -->\n    <div class=\"page-header\">\n      <div class=\"header-content\">\n        <div class=\"header-main\">\n          <h1 class=\"page-title\">\n            <i class=\"fas fa-file-alt\"></i>\n            My Document Requests\n          </h1>\n          <p class=\"page-description\">\n            Track and manage your document requests\n          </p>\n        </div>\n        <div class=\"header-actions\">\n          <button class=\"new-request-btn\" @click=\"createNewRequest\">\n            <i class=\"fas fa-plus\"></i>\n            New Request\n          </button>\n          <button class=\"back-btn\" @click=\"goBack\">\n            <i class=\"fas fa-arrow-left\"></i>\n            Back to Dashboard\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Filters and Search -->\n    <div class=\"filters-section\">\n      <div class=\"filters-container\">\n        <div class=\"search-box\">\n          <i class=\"fas fa-search\"></i>\n          <input\n            v-model=\"searchQuery\"\n            type=\"text\"\n            placeholder=\"Search requests...\"\n            @input=\"handleSearch\"\n          />\n        </div>\n        \n        <div class=\"filter-group\">\n          <select v-model=\"statusFilter\" @change=\"applyFilters\">\n            <option value=\"\">All Status</option>\n            <option value=\"pending\">Pending</option>\n            <option value=\"under_review\">Under Review</option>\n            <option value=\"approved\">Approved</option>\n            <option value=\"processing\">Processing</option>\n            <option value=\"ready_for_pickup\">Ready for Pickup</option>\n            <option value=\"completed\">Completed</option>\n            <option value=\"rejected\">Rejected</option>\n          </select>\n        </div>\n\n        <div class=\"filter-group\">\n          <select v-model=\"typeFilter\" @change=\"applyFilters\">\n            <option value=\"\">All Types</option>\n            <option value=\"Barangay Clearance\">Barangay Clearance</option>\n            <option value=\"Cedula\">Cedula</option>\n          </select>\n        </div>\n\n        <div class=\"filter-group\">\n          <select v-model=\"sortBy\" @change=\"applyFilters\">\n            <option value=\"created_at_desc\">Newest First</option>\n            <option value=\"created_at_asc\">Oldest First</option>\n            <option value=\"status\">By Status</option>\n            <option value=\"type\">By Type</option>\n          </select>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div v-if=\"loading\" class=\"loading-container\">\n      <div class=\"loading-spinner\">\n        <i class=\"fas fa-spinner fa-spin\"></i>\n      </div>\n      <p>Loading your requests...</p>\n    </div>\n\n    <!-- Error State -->\n    <div v-else-if=\"error\" class=\"error-container\">\n      <div class=\"error-content\">\n        <i class=\"fas fa-exclamation-triangle\"></i>\n        <h3>Unable to Load Requests</h3>\n        <p>{{ error }}</p>\n        <button class=\"retry-btn\" @click=\"loadRequests\">\n          <i class=\"fas fa-redo\"></i>\n          Try Again\n        </button>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div v-else-if=\"filteredRequests.length === 0 && !loading\" class=\"empty-state\">\n      <div class=\"empty-content\">\n        <i class=\"fas fa-inbox\"></i>\n        <h3>{{ searchQuery || statusFilter || typeFilter ? 'No Matching Requests' : 'No Requests Yet' }}</h3>\n        <p>\n          {{ searchQuery || statusFilter || typeFilter \n            ? 'Try adjusting your search or filters' \n            : 'Start by creating your first document request' }}\n        </p>\n        <button v-if=\"!searchQuery && !statusFilter && !typeFilter\" class=\"create-first-btn\" @click=\"createNewRequest\">\n          <i class=\"fas fa-plus\"></i>\n          Create Your First Request\n        </button>\n      </div>\n    </div>\n\n    <!-- Requests List -->\n    <div v-else class=\"requests-container\">\n      <div class=\"requests-grid\">\n        <div\n          v-for=\"request in paginatedRequests\"\n          :key=\"request.id\"\n          class=\"request-card\"\n          @click=\"viewRequestDetails(request.id)\"\n        >\n          <div class=\"request-header\">\n            <div class=\"request-type\">\n              <i :class=\"getDocumentIcon(request.document_type)\"></i>\n              <span>{{ request.document_type }}</span>\n            </div>\n            <div class=\"request-status\">\n              <span class=\"status-badge\" :class=\"getStatusClass(request.status)\">\n                {{ formatStatus(request.status) }}\n              </span>\n            </div>\n          </div>\n\n          <div class=\"request-body\">\n            <div class=\"request-info\">\n              <div class=\"info-item\">\n                <label>Request ID:</label>\n                <span class=\"request-id\">#{{ request.id.toString().padStart(6, '0') }}</span>\n              </div>\n              <div class=\"info-item\">\n                <label>Purpose:</label>\n                <span>{{ request.purpose_category || 'Not specified' }}</span>\n              </div>\n              <div class=\"info-item\">\n                <label>Submitted:</label>\n                <span>{{ formatDate(request.created_at) }}</span>\n              </div>\n              <div class=\"info-item\">\n                <label>Amount:</label>\n                <span class=\"amount\">₱{{ formatCurrency(request.total_fee) }}</span>\n              </div>\n            </div>\n\n            <div class=\"request-progress\">\n              <div class=\"progress-bar\">\n                <div \n                  class=\"progress-fill\" \n                  :style=\"{ width: getProgressPercentage(request.status) + '%' }\"\n                ></div>\n              </div>\n              <div class=\"progress-text\">\n                {{ getProgressText(request.status) }}\n              </div>\n            </div>\n          </div>\n\n          <div class=\"request-actions\">\n            <button class=\"action-btn view-btn\" @click.stop=\"viewRequestDetails(request.id)\">\n              <i class=\"fas fa-eye\"></i>\n              View Details\n            </button>\n            \n            <button \n              v-if=\"canCancelRequest(request.status)\"\n              class=\"action-btn cancel-btn\"\n              @click.stop=\"cancelRequest(request.id)\"\n            >\n              <i class=\"fas fa-times\"></i>\n              Cancel\n            </button>\n\n            <button \n              v-if=\"needsPayment(request)\"\n              class=\"action-btn pay-btn\"\n              @click.stop=\"processPayment(request.id)\"\n            >\n              <i class=\"fas fa-credit-card\"></i>\n              Pay Now\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Pagination -->\n      <div v-if=\"totalPages > 1\" class=\"pagination\">\n        <button \n          class=\"page-btn\"\n          :disabled=\"currentPage === 1\"\n          @click=\"changePage(currentPage - 1)\"\n        >\n          <i class=\"fas fa-chevron-left\"></i>\n          Previous\n        </button>\n        \n        <div class=\"page-numbers\">\n          <button\n            v-for=\"page in visiblePages\"\n            :key=\"page\"\n            class=\"page-number\"\n            :class=\"{ active: page === currentPage }\"\n            @click=\"changePage(page)\"\n          >\n            {{ page }}\n          </button>\n        </div>\n        \n        <button \n          class=\"page-btn\"\n          :disabled=\"currentPage === totalPages\"\n          @click=\"changePage(currentPage + 1)\"\n        >\n          Next\n          <i class=\"fas fa-chevron-right\"></i>\n        </button>\n      </div>\n    </div>\n\n    <!-- Summary Stats -->\n    <div class=\"summary-stats\">\n      <div class=\"stats-grid\">\n        <div class=\"stat-card\">\n          <div class=\"stat-icon\">\n            <i class=\"fas fa-file-alt\"></i>\n          </div>\n          <div class=\"stat-content\">\n            <h3>{{ totalRequests }}</h3>\n            <p>Total Requests</p>\n          </div>\n        </div>\n        \n        <div class=\"stat-card\">\n          <div class=\"stat-icon pending\">\n            <i class=\"fas fa-clock\"></i>\n          </div>\n          <div class=\"stat-content\">\n            <h3>{{ pendingCount }}</h3>\n            <p>Pending</p>\n          </div>\n        </div>\n        \n        <div class=\"stat-card\">\n          <div class=\"stat-icon completed\">\n            <i class=\"fas fa-check-circle\"></i>\n          </div>\n          <div class=\"stat-content\">\n            <h3>{{ completedCount }}</h3>\n            <p>Completed</p>\n          </div>\n        </div>\n        \n        <div class=\"stat-card\">\n          <div class=\"stat-icon\">\n            <i class=\"fas fa-peso-sign\"></i>\n          </div>\n          <div class=\"stat-content\">\n            <h3>₱{{ formatCurrency(totalSpent) }}</h3>\n            <p>Total Spent</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport documentRequestService from '@/services/documentRequestService';\nimport notificationService from '@/services/notificationService';\n\nexport default {\n  name: 'MyRequests',\n  data() {\n    return {\n      requests: [],\n      loading: true,\n      error: null,\n      \n      // Filters\n      searchQuery: '',\n      statusFilter: '',\n      typeFilter: '',\n      sortBy: 'created_at_desc',\n      \n      // Pagination\n      currentPage: 1,\n      itemsPerPage: 12,\n      \n      // Search debounce\n      searchTimeout: null\n    };\n  },\n  computed: {\n    filteredRequests() {\n      // Ensure requests is always an array\n      if (!Array.isArray(this.requests)) {\n        return [];\n      }\n      let filtered = [...this.requests];\n      \n      // Apply search filter\n      if (this.searchQuery) {\n        const query = this.searchQuery.toLowerCase();\n        filtered = filtered.filter(request => \n          request.document_type.toLowerCase().includes(query) ||\n          request.purpose_category?.toLowerCase().includes(query) ||\n          request.id.toString().includes(query)\n        );\n      }\n      \n      // Apply status filter\n      if (this.statusFilter) {\n        filtered = filtered.filter(request => request.status === this.statusFilter);\n      }\n      \n      // Apply type filter\n      if (this.typeFilter) {\n        filtered = filtered.filter(request => request.document_type === this.typeFilter);\n      }\n      \n      // Apply sorting\n      filtered.sort((a, b) => {\n        switch (this.sortBy) {\n          case 'created_at_desc':\n            return new Date(b.created_at) - new Date(a.created_at);\n          case 'created_at_asc':\n            return new Date(a.created_at) - new Date(b.created_at);\n          case 'status':\n            return a.status.localeCompare(b.status);\n          case 'type':\n            return a.document_type.localeCompare(b.document_type);\n          default:\n            return 0;\n        }\n      });\n      \n      return filtered;\n    },\n    \n    paginatedRequests() {\n      const start = (this.currentPage - 1) * this.itemsPerPage;\n      const end = start + this.itemsPerPage;\n      return this.filteredRequests.slice(start, end);\n    },\n    \n    totalPages() {\n      return Math.ceil(this.filteredRequests.length / this.itemsPerPage);\n    },\n    \n    visiblePages() {\n      const pages = [];\n      const start = Math.max(1, this.currentPage - 2);\n      const end = Math.min(this.totalPages, this.currentPage + 2);\n      \n      for (let i = start; i <= end; i++) {\n        pages.push(i);\n      }\n      \n      return pages;\n    },\n    \n    totalRequests() {\n      return this.requests.length;\n    },\n    \n    pendingCount() {\n      return this.requests.filter(r => ['pending', 'under_review'].includes(r.status)).length;\n    },\n    \n    completedCount() {\n      return this.requests.filter(r => r.status === 'completed').length;\n    },\n    \n    totalSpent() {\n      return this.requests\n        .filter(r => r.status === 'completed')\n        .reduce((sum, r) => sum + parseFloat(r.total_fee || 0), 0);\n    }\n  },\n  async mounted() {\n    await this.loadRequests();\n    await this.initializeRealTimeFeatures();\n  },\n\n  beforeUnmount() {\n    this.cleanupRealTimeFeatures();\n  },\n  methods: {\n    async loadRequests() {\n      try {\n        this.loading = true;\n        this.error = null;\n\n        const response = await documentRequestService.getMyRequests();\n\n        // Handle the nested response structure: response.data.requests\n        if (response && response.data && response.data.requests && Array.isArray(response.data.requests)) {\n          this.requests = response.data.requests;\n        } else if (response && response.data && Array.isArray(response.data)) {\n          this.requests = response.data;\n        } else if (response && Array.isArray(response)) {\n          this.requests = response;\n        } else {\n          this.requests = [];\n          console.warn('API response does not contain array data:', response);\n        }\n\n      } catch (error) {\n        console.error('Error loading requests:', error);\n        this.error = error.response?.data?.message || 'Failed to load requests';\n        this.requests = []; // Ensure requests is always an array even on error\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    handleSearch() {\n      // Debounce search\n      if (this.searchTimeout) {\n        clearTimeout(this.searchTimeout);\n      }\n      \n      this.searchTimeout = setTimeout(() => {\n        this.currentPage = 1; // Reset to first page when searching\n      }, 300);\n    },\n\n    applyFilters() {\n      this.currentPage = 1; // Reset to first page when filtering\n    },\n\n    changePage(page) {\n      if (page >= 1 && page <= this.totalPages) {\n        this.currentPage = page;\n      }\n    },\n\n    getDocumentIcon(type) {\n      const icons = {\n        'Barangay Clearance': 'fas fa-certificate',\n        'Cedula': 'fas fa-id-card'\n      };\n      return icons[type] || 'fas fa-file-alt';\n    },\n\n    getStatusClass(status) {\n      const classes = {\n        'pending': 'status-pending',\n        'under_review': 'status-review',\n        'approved': 'status-approved',\n        'processing': 'status-processing',\n        'ready_for_pickup': 'status-ready',\n        'completed': 'status-completed',\n        'rejected': 'status-rejected',\n        'cancelled': 'status-cancelled'\n      };\n      return classes[status] || 'status-unknown';\n    },\n\n    formatStatus(status) {\n      const statusMap = {\n        'pending': 'Pending',\n        'under_review': 'Under Review',\n        'approved': 'Approved',\n        'processing': 'Processing',\n        'ready_for_pickup': 'Ready for Pickup',\n        'completed': 'Completed',\n        'rejected': 'Rejected',\n        'cancelled': 'Cancelled'\n      };\n      return statusMap[status] || status;\n    },\n\n    getProgressPercentage(status) {\n      const percentages = {\n        'pending': 10,\n        'under_review': 25,\n        'approved': 50,\n        'processing': 75,\n        'ready_for_pickup': 90,\n        'completed': 100,\n        'rejected': 0,\n        'cancelled': 0\n      };\n      return percentages[status] || 0;\n    },\n\n    getProgressText(status) {\n      const texts = {\n        'pending': 'Waiting for review',\n        'under_review': 'Being reviewed',\n        'approved': 'Approved, processing payment',\n        'processing': 'Document being prepared',\n        'ready_for_pickup': 'Ready for pickup',\n        'completed': 'Request completed',\n        'rejected': 'Request rejected',\n        'cancelled': 'Request cancelled'\n      };\n      return texts[status] || 'Unknown status';\n    },\n\n    canCancelRequest(status) {\n      return ['pending', 'under_review'].includes(status);\n    },\n\n    needsPayment(request) {\n      return request.status === 'approved' && !request.payment_status;\n    },\n\n    formatDate(dateString) {\n      if (!dateString) return 'N/A';\n      return new Date(dateString).toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    },\n\n    formatCurrency(amount) {\n      return parseFloat(amount || 0).toFixed(2);\n    },\n\n    viewRequestDetails(requestId) {\n      this.$router.push({ name: 'RequestDetails', params: { id: requestId } });\n    },\n\n    async cancelRequest(requestId) {\n      if (!confirm('Are you sure you want to cancel this request?')) return;\n      \n      try {\n        await documentRequestService.cancelRequest(requestId, 'Cancelled by user');\n        this.$toast?.success('Request cancelled successfully');\n        await this.loadRequests(); // Reload requests\n      } catch (error) {\n        console.error('Error cancelling request:', error);\n        this.$toast?.error('Failed to cancel request');\n      }\n    },\n\n    processPayment(requestId) {\n      // TODO: Implement payment processing\n      console.log('Process payment for request:', requestId);\n    },\n\n    createNewRequest() {\n      this.$router.push({ name: 'NewDocumentRequest' });\n    },\n\n    goBack() {\n      this.$router.push({ name: 'ClientDashboard' });\n    }\n  }\n};\n</script>\n\n<style scoped>\n.my-requests-page {\n  padding: 2rem;\n  max-width: 1400px;\n  margin: 0 auto;\n}\n\n.page-header {\n  margin-bottom: 2rem;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  gap: 2rem;\n}\n\n.page-title {\n  font-size: 1.75rem;\n  font-weight: 700;\n  color: #1a365d;\n  margin-bottom: 0.5rem;\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.page-title i {\n  color: #3182ce;\n}\n\n.page-description {\n  font-size: 1rem;\n  color: #4a5568;\n  margin: 0;\n}\n\n.header-actions {\n  display: flex;\n  gap: 1rem;\n}\n\n.new-request-btn,\n.back-btn {\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.5rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  border: none;\n}\n\n.new-request-btn {\n  background: #38a169;\n  color: white;\n}\n\n.new-request-btn:hover {\n  background: #2f855a;\n}\n\n.back-btn {\n  background: #e2e8f0;\n  color: #4a5568;\n}\n\n.back-btn:hover {\n  background: #cbd5e0;\n}\n\n.filters-section {\n  background: white;\n  border-radius: 1rem;\n  padding: 1.5rem;\n  margin-bottom: 2rem;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n}\n\n.filters-container {\n  display: grid;\n  grid-template-columns: 2fr 1fr 1fr 1fr;\n  gap: 1rem;\n  align-items: center;\n}\n\n.search-box {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.search-box i {\n  position: absolute;\n  left: 1rem;\n  color: #a0aec0;\n  z-index: 1;\n}\n\n.search-box input {\n  width: 100%;\n  padding: 0.75rem 1rem 0.75rem 2.5rem;\n  border: 1px solid #e2e8f0;\n  border-radius: 0.5rem;\n  font-size: 0.875rem;\n  transition: all 0.2s;\n}\n\n.search-box input:focus {\n  outline: none;\n  border-color: #3182ce;\n  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);\n}\n\n.filter-group select {\n  width: 100%;\n  padding: 0.75rem;\n  border: 1px solid #e2e8f0;\n  border-radius: 0.5rem;\n  font-size: 0.875rem;\n  background: white;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.filter-group select:focus {\n  outline: none;\n  border-color: #3182ce;\n  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);\n}\n\n.loading-container,\n.error-container,\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4rem 2rem;\n  text-align: center;\n}\n\n.loading-spinner i {\n  font-size: 2rem;\n  color: #3182ce;\n  margin-bottom: 1rem;\n}\n\n.error-content,\n.empty-content {\n  max-width: 400px;\n}\n\n.error-content i,\n.empty-content i {\n  font-size: 3rem;\n  color: #a0aec0;\n  margin-bottom: 1rem;\n}\n\n.error-content h3,\n.empty-content h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: #2d3748;\n  margin-bottom: 0.5rem;\n}\n\n.error-content p,\n.empty-content p {\n  color: #718096;\n  margin-bottom: 1.5rem;\n}\n\n.retry-btn,\n.create-first-btn {\n  background: #3182ce;\n  color: white;\n  border: none;\n  padding: 0.75rem 1.5rem;\n  border-radius: 0.5rem;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  transition: all 0.2s;\n}\n\n.retry-btn:hover,\n.create-first-btn:hover {\n  background: #2c5aa0;\n}\n\n.requests-container {\n  margin-bottom: 3rem;\n}\n\n.requests-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\n  gap: 1.5rem;\n  margin-bottom: 2rem;\n}\n\n.request-card {\n  background: white;\n  border-radius: 1rem;\n  padding: 1.5rem;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\n  border: 1px solid #e2e8f0;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.request-card:hover {\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n  transform: translateY(-2px);\n}\n\n.request-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.request-type {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-weight: 600;\n  color: #2d3748;\n}\n\n.request-type i {\n  color: #3182ce;\n}\n\n.status-badge {\n  padding: 0.25rem 0.75rem;\n  border-radius: 1rem;\n  font-size: 0.75rem;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n}\n\n.status-pending { background: #fef5e7; color: #d69e2e; }\n.status-review { background: #ebf8ff; color: #3182ce; }\n.status-approved { background: #f0fff4; color: #38a169; }\n.status-processing { background: #e6fffa; color: #319795; }\n.status-ready { background: #f0f9ff; color: #0ea5e9; }\n.status-completed { background: #f0fff4; color: #22c55e; }\n.status-rejected { background: #fef2f2; color: #ef4444; }\n.status-cancelled { background: #f8fafc; color: #64748b; }\n\n.request-body {\n  margin-bottom: 1rem;\n}\n\n.request-info {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 0.75rem;\n  margin-bottom: 1rem;\n}\n\n.info-item {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.info-item label {\n  font-size: 0.75rem;\n  font-weight: 500;\n  color: #718096;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n}\n\n.info-item span {\n  color: #2d3748;\n  font-weight: 500;\n}\n\n.request-id {\n  font-family: 'Courier New', monospace;\n  color: #3182ce !important;\n}\n\n.amount {\n  color: #38a169 !important;\n  font-weight: 600 !important;\n}\n\n.request-progress {\n  margin-bottom: 1rem;\n}\n\n.progress-bar {\n  width: 100%;\n  height: 0.5rem;\n  background: #e2e8f0;\n  border-radius: 0.25rem;\n  overflow: hidden;\n  margin-bottom: 0.5rem;\n}\n\n.progress-fill {\n  height: 100%;\n  background: linear-gradient(90deg, #3182ce, #38a169);\n  border-radius: 0.25rem;\n  transition: width 0.3s ease;\n}\n\n.progress-text {\n  font-size: 0.875rem;\n  color: #4a5568;\n  text-align: center;\n}\n\n.request-actions {\n  display: flex;\n  gap: 0.5rem;\n  flex-wrap: wrap;\n}\n\n.action-btn {\n  padding: 0.5rem 1rem;\n  border-radius: 0.375rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.375rem;\n  border: none;\n  flex: 1;\n  justify-content: center;\n  min-width: 100px;\n}\n\n.view-btn {\n  background: #3182ce;\n  color: white;\n}\n\n.view-btn:hover {\n  background: #2c5aa0;\n}\n\n.cancel-btn {\n  background: #e53e3e;\n  color: white;\n}\n\n.cancel-btn:hover {\n  background: #c53030;\n}\n\n.pay-btn {\n  background: #38a169;\n  color: white;\n}\n\n.pay-btn:hover {\n  background: #2f855a;\n}\n\n.pagination {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 1rem;\n  margin-top: 2rem;\n}\n\n.page-btn {\n  padding: 0.5rem 1rem;\n  border: 1px solid #e2e8f0;\n  background: white;\n  color: #4a5568;\n  border-radius: 0.375rem;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.page-btn:hover:not(:disabled) {\n  background: #f7fafc;\n  border-color: #cbd5e0;\n}\n\n.page-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.page-numbers {\n  display: flex;\n  gap: 0.25rem;\n}\n\n.page-number {\n  width: 2.5rem;\n  height: 2.5rem;\n  border: 1px solid #e2e8f0;\n  background: white;\n  color: #4a5568;\n  border-radius: 0.375rem;\n  cursor: pointer;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.page-number:hover {\n  background: #f7fafc;\n  border-color: #cbd5e0;\n}\n\n.page-number.active {\n  background: #3182ce;\n  color: white;\n  border-color: #3182ce;\n}\n\n.summary-stats {\n  background: white;\n  border-radius: 1rem;\n  padding: 2rem;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1.5rem;\n}\n\n.stat-card {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1rem;\n  background: #f7fafc;\n  border-radius: 0.75rem;\n}\n\n.stat-icon {\n  width: 3rem;\n  height: 3rem;\n  background: #3182ce;\n  color: white;\n  border-radius: 0.75rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 1.25rem;\n}\n\n.stat-icon.pending {\n  background: #d69e2e;\n}\n\n.stat-icon.completed {\n  background: #38a169;\n}\n\n.stat-content h3 {\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: #1a365d;\n  margin-bottom: 0.25rem;\n}\n\n.stat-content p {\n  color: #718096;\n  font-size: 0.875rem;\n  margin: 0;\n}\n\n@media (max-width: 1024px) {\n  .filters-container {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n  }\n\n  .requests-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .stats-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (max-width: 768px) {\n  .my-requests-page {\n    padding: 1rem;\n  }\n\n  .header-content {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .header-actions {\n    justify-content: space-between;\n  }\n\n  .request-info {\n    grid-template-columns: 1fr;\n  }\n\n  .request-actions {\n    flex-direction: column;\n  }\n\n  .action-btn {\n    flex: none;\n  }\n\n  .stats-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .pagination {\n    flex-wrap: wrap;\n    gap: 0.5rem;\n  }\n\n  .page-numbers {\n    order: -1;\n    width: 100%;\n    justify-content: center;\n  }\n}\n</style>\n"], "mappings": ";;;;AAiRA,OAAOA,sBAAqB,MAAO,mCAAmC;AACtE,OAAOC,mBAAkB,MAAO,gCAAgC;AAEhE,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,IAAI;MAEX;MACAC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,UAAU,EAAE,EAAE;MACdC,MAAM,EAAE,iBAAiB;MAEzB;MACAC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE,EAAE;MAEhB;MACAC,aAAa,EAAE;IACjB,CAAC;EACH,CAAC;EACDC,QAAQ,EAAE;IACRC,gBAAgBA,CAAA,EAAG;MACjB;MACA,IAAI,CAACC,KAAK,CAACC,OAAO,CAAC,IAAI,CAACb,QAAQ,CAAC,EAAE;QACjC,OAAO,EAAE;MACX;MACA,IAAIc,QAAO,GAAI,CAAC,GAAG,IAAI,CAACd,QAAQ,CAAC;;MAEjC;MACA,IAAI,IAAI,CAACG,WAAW,EAAE;QACpB,MAAMY,KAAI,GAAI,IAAI,CAACZ,WAAW,CAACa,WAAW,CAAC,CAAC;QAC5CF,QAAO,GAAIA,QAAQ,CAACG,MAAM,CAACC,OAAM,IAC/BA,OAAO,CAACC,aAAa,CAACH,WAAW,CAAC,CAAC,CAACI,QAAQ,CAACL,KAAK,KAClDG,OAAO,CAACG,gBAAgB,EAAEL,WAAW,CAAC,CAAC,CAACI,QAAQ,CAACL,KAAK,KACtDG,OAAO,CAACI,EAAE,CAACC,QAAQ,CAAC,CAAC,CAACH,QAAQ,CAACL,KAAK,CACtC,CAAC;MACH;;MAEA;MACA,IAAI,IAAI,CAACX,YAAY,EAAE;QACrBU,QAAO,GAAIA,QAAQ,CAACG,MAAM,CAACC,OAAM,IAAKA,OAAO,CAACM,MAAK,KAAM,IAAI,CAACpB,YAAY,CAAC;MAC7E;;MAEA;MACA,IAAI,IAAI,CAACC,UAAU,EAAE;QACnBS,QAAO,GAAIA,QAAQ,CAACG,MAAM,CAACC,OAAM,IAAKA,OAAO,CAACC,aAAY,KAAM,IAAI,CAACd,UAAU,CAAC;MAClF;;MAEA;MACAS,QAAQ,CAACW,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACtB,QAAQ,IAAI,CAACrB,MAAM;UACjB,KAAK,iBAAiB;YACpB,OAAO,IAAIsB,IAAI,CAACD,CAAC,CAACE,UAAU,IAAI,IAAID,IAAI,CAACF,CAAC,CAACG,UAAU,CAAC;UACxD,KAAK,gBAAgB;YACnB,OAAO,IAAID,IAAI,CAACF,CAAC,CAACG,UAAU,IAAI,IAAID,IAAI,CAACD,CAAC,CAACE,UAAU,CAAC;UACxD,KAAK,QAAQ;YACX,OAAOH,CAAC,CAACF,MAAM,CAACM,aAAa,CAACH,CAAC,CAACH,MAAM,CAAC;UACzC,KAAK,MAAM;YACT,OAAOE,CAAC,CAACP,aAAa,CAACW,aAAa,CAACH,CAAC,CAACR,aAAa,CAAC;UACvD;YACE,OAAO,CAAC;QACZ;MACF,CAAC,CAAC;MAEF,OAAOL,QAAQ;IACjB,CAAC;IAEDiB,iBAAiBA,CAAA,EAAG;MAClB,MAAMC,KAAI,GAAI,CAAC,IAAI,CAACzB,WAAU,GAAI,CAAC,IAAI,IAAI,CAACC,YAAY;MACxD,MAAMyB,GAAE,GAAID,KAAI,GAAI,IAAI,CAACxB,YAAY;MACrC,OAAO,IAAI,CAACG,gBAAgB,CAACuB,KAAK,CAACF,KAAK,EAAEC,GAAG,CAAC;IAChD,CAAC;IAEDE,UAAUA,CAAA,EAAG;MACX,OAAOC,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC1B,gBAAgB,CAAC2B,MAAK,GAAI,IAAI,CAAC9B,YAAY,CAAC;IACpE,CAAC;IAED+B,YAAYA,CAAA,EAAG;MACb,MAAMC,KAAI,GAAI,EAAE;MAChB,MAAMR,KAAI,GAAII,IAAI,CAACK,GAAG,CAAC,CAAC,EAAE,IAAI,CAAClC,WAAU,GAAI,CAAC,CAAC;MAC/C,MAAM0B,GAAE,GAAIG,IAAI,CAACM,GAAG,CAAC,IAAI,CAACP,UAAU,EAAE,IAAI,CAAC5B,WAAU,GAAI,CAAC,CAAC;MAE3D,KAAK,IAAIoC,CAAA,GAAIX,KAAK,EAAEW,CAAA,IAAKV,GAAG,EAAEU,CAAC,EAAE,EAAE;QACjCH,KAAK,CAACI,IAAI,CAACD,CAAC,CAAC;MACf;MAEA,OAAOH,KAAK;IACd,CAAC;IAEDK,aAAaA,CAAA,EAAG;MACd,OAAO,IAAI,CAAC7C,QAAQ,CAACsC,MAAM;IAC7B,CAAC;IAEDQ,YAAYA,CAAA,EAAG;MACb,OAAO,IAAI,CAAC9C,QAAQ,CAACiB,MAAM,CAAC8B,CAAA,IAAK,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC3B,QAAQ,CAAC2B,CAAC,CAACvB,MAAM,CAAC,CAAC,CAACc,MAAM;IACzF,CAAC;IAEDU,cAAcA,CAAA,EAAG;MACf,OAAO,IAAI,CAAChD,QAAQ,CAACiB,MAAM,CAAC8B,CAAA,IAAKA,CAAC,CAACvB,MAAK,KAAM,WAAW,CAAC,CAACc,MAAM;IACnE,CAAC;IAEDW,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAACjD,QAAO,CAChBiB,MAAM,CAAC8B,CAAA,IAAKA,CAAC,CAACvB,MAAK,KAAM,WAAW,EACpC0B,MAAM,CAAC,CAACC,GAAG,EAAEJ,CAAC,KAAKI,GAAE,GAAIC,UAAU,CAACL,CAAC,CAACM,SAAQ,IAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IAC9D;EACF,CAAC;EACD,MAAMC,OAAOA,CAAA,EAAG;IACd,MAAM,IAAI,CAACC,YAAY,CAAC,CAAC;IACzB,MAAM,IAAI,CAACC,0BAA0B,CAAC,CAAC;EACzC,CAAC;EAEDC,aAAaA,CAAA,EAAG;IACd,IAAI,CAACC,uBAAuB,CAAC,CAAC;EAChC,CAAC;EACDC,OAAO,EAAE;IACP,MAAMJ,YAAYA,CAAA,EAAG;MACnB,IAAI;QACF,IAAI,CAACtD,OAAM,GAAI,IAAI;QACnB,IAAI,CAACC,KAAI,GAAI,IAAI;QAEjB,MAAM0D,QAAO,GAAI,MAAMhE,sBAAsB,CAACiE,aAAa,CAAC,CAAC;;QAE7D;QACA,IAAID,QAAO,IAAKA,QAAQ,CAAC7D,IAAG,IAAK6D,QAAQ,CAAC7D,IAAI,CAACC,QAAO,IAAKY,KAAK,CAACC,OAAO,CAAC+C,QAAQ,CAAC7D,IAAI,CAACC,QAAQ,CAAC,EAAE;UAChG,IAAI,CAACA,QAAO,GAAI4D,QAAQ,CAAC7D,IAAI,CAACC,QAAQ;QACxC,OAAO,IAAI4D,QAAO,IAAKA,QAAQ,CAAC7D,IAAG,IAAKa,KAAK,CAACC,OAAO,CAAC+C,QAAQ,CAAC7D,IAAI,CAAC,EAAE;UACpE,IAAI,CAACC,QAAO,GAAI4D,QAAQ,CAAC7D,IAAI;QAC/B,OAAO,IAAI6D,QAAO,IAAKhD,KAAK,CAACC,OAAO,CAAC+C,QAAQ,CAAC,EAAE;UAC9C,IAAI,CAAC5D,QAAO,GAAI4D,QAAQ;QAC1B,OAAO;UACL,IAAI,CAAC5D,QAAO,GAAI,EAAE;UAClB8D,OAAO,CAACC,IAAI,CAAC,2CAA2C,EAAEH,QAAQ,CAAC;QACrE;MAEF,EAAE,OAAO1D,KAAK,EAAE;QACd4D,OAAO,CAAC5D,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACA,KAAI,GAAIA,KAAK,CAAC0D,QAAQ,EAAE7D,IAAI,EAAEiE,OAAM,IAAK,yBAAyB;QACvE,IAAI,CAAChE,QAAO,GAAI,EAAE,EAAE;MACtB,UAAU;QACR,IAAI,CAACC,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAEDgE,YAAYA,CAAA,EAAG;MACb;MACA,IAAI,IAAI,CAACxD,aAAa,EAAE;QACtByD,YAAY,CAAC,IAAI,CAACzD,aAAa,CAAC;MAClC;MAEA,IAAI,CAACA,aAAY,GAAI0D,UAAU,CAAC,MAAM;QACpC,IAAI,CAAC5D,WAAU,GAAI,CAAC,EAAE;MACxB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC;IAED6D,YAAYA,CAAA,EAAG;MACb,IAAI,CAAC7D,WAAU,GAAI,CAAC,EAAE;IACxB,CAAC;IAED8D,UAAUA,CAACC,IAAI,EAAE;MACf,IAAIA,IAAG,IAAK,KAAKA,IAAG,IAAK,IAAI,CAACnC,UAAU,EAAE;QACxC,IAAI,CAAC5B,WAAU,GAAI+D,IAAI;MACzB;IACF,CAAC;IAEDC,eAAeA,CAACC,IAAI,EAAE;MACpB,MAAMC,KAAI,GAAI;QACZ,oBAAoB,EAAE,oBAAoB;QAC1C,QAAQ,EAAE;MACZ,CAAC;MACD,OAAOA,KAAK,CAACD,IAAI,KAAK,iBAAiB;IACzC,CAAC;IAEDE,cAAcA,CAAClD,MAAM,EAAE;MACrB,MAAMmD,OAAM,GAAI;QACd,SAAS,EAAE,gBAAgB;QAC3B,cAAc,EAAE,eAAe;QAC/B,UAAU,EAAE,iBAAiB;QAC7B,YAAY,EAAE,mBAAmB;QACjC,kBAAkB,EAAE,cAAc;QAClC,WAAW,EAAE,kBAAkB;QAC/B,UAAU,EAAE,iBAAiB;QAC7B,WAAW,EAAE;MACf,CAAC;MACD,OAAOA,OAAO,CAACnD,MAAM,KAAK,gBAAgB;IAC5C,CAAC;IAEDoD,YAAYA,CAACpD,MAAM,EAAE;MACnB,MAAMqD,SAAQ,GAAI;QAChB,SAAS,EAAE,SAAS;QACpB,cAAc,EAAE,cAAc;QAC9B,UAAU,EAAE,UAAU;QACtB,YAAY,EAAE,YAAY;QAC1B,kBAAkB,EAAE,kBAAkB;QACtC,WAAW,EAAE,WAAW;QACxB,UAAU,EAAE,UAAU;QACtB,WAAW,EAAE;MACf,CAAC;MACD,OAAOA,SAAS,CAACrD,MAAM,KAAKA,MAAM;IACpC,CAAC;IAEDsD,qBAAqBA,CAACtD,MAAM,EAAE;MAC5B,MAAMuD,WAAU,GAAI;QAClB,SAAS,EAAE,EAAE;QACb,cAAc,EAAE,EAAE;QAClB,UAAU,EAAE,EAAE;QACd,YAAY,EAAE,EAAE;QAChB,kBAAkB,EAAE,EAAE;QACtB,WAAW,EAAE,GAAG;QAChB,UAAU,EAAE,CAAC;QACb,WAAW,EAAE;MACf,CAAC;MACD,OAAOA,WAAW,CAACvD,MAAM,KAAK,CAAC;IACjC,CAAC;IAEDwD,eAAeA,CAACxD,MAAM,EAAE;MACtB,MAAMyD,KAAI,GAAI;QACZ,SAAS,EAAE,oBAAoB;QAC/B,cAAc,EAAE,gBAAgB;QAChC,UAAU,EAAE,8BAA8B;QAC1C,YAAY,EAAE,yBAAyB;QACvC,kBAAkB,EAAE,kBAAkB;QACtC,WAAW,EAAE,mBAAmB;QAChC,UAAU,EAAE,kBAAkB;QAC9B,WAAW,EAAE;MACf,CAAC;MACD,OAAOA,KAAK,CAACzD,MAAM,KAAK,gBAAgB;IAC1C,CAAC;IAED0D,gBAAgBA,CAAC1D,MAAM,EAAE;MACvB,OAAO,CAAC,SAAS,EAAE,cAAc,CAAC,CAACJ,QAAQ,CAACI,MAAM,CAAC;IACrD,CAAC;IAED2D,YAAYA,CAACjE,OAAO,EAAE;MACpB,OAAOA,OAAO,CAACM,MAAK,KAAM,UAAS,IAAK,CAACN,OAAO,CAACkE,cAAc;IACjE,CAAC;IAEDC,UAAUA,CAACC,UAAU,EAAE;MACrB,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;MAC7B,OAAO,IAAI1D,IAAI,CAAC0D,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;QACtDC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC;IAEDC,cAAcA,CAACC,MAAM,EAAE;MACrB,OAAOxC,UAAU,CAACwC,MAAK,IAAK,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;IAC3C,CAAC;IAEDC,kBAAkBA,CAACC,SAAS,EAAE;MAC5B,IAAI,CAACC,OAAO,CAACpD,IAAI,CAAC;QAAE9C,IAAI,EAAE,gBAAgB;QAAEmG,MAAM,EAAE;UAAE3E,EAAE,EAAEyE;QAAU;MAAE,CAAC,CAAC;IAC1E,CAAC;IAED,MAAMG,aAAaA,CAACH,SAAS,EAAE;MAC7B,IAAI,CAACI,OAAO,CAAC,+CAA+C,CAAC,EAAE;MAE/D,IAAI;QACF,MAAMvG,sBAAsB,CAACsG,aAAa,CAACH,SAAS,EAAE,mBAAmB,CAAC;QAC1E,IAAI,CAACK,MAAM,EAAEC,OAAO,CAAC,gCAAgC,CAAC;QACtD,MAAM,IAAI,CAAC9C,YAAY,CAAC,CAAC,EAAE;MAC7B,EAAE,OAAOrD,KAAK,EAAE;QACd4D,OAAO,CAAC5D,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,IAAI,CAACkG,MAAM,EAAElG,KAAK,CAAC,0BAA0B,CAAC;MAChD;IACF,CAAC;IAEDoG,cAAcA,CAACP,SAAS,EAAE;MACxB;MACAjC,OAAO,CAACyC,GAAG,CAAC,8BAA8B,EAAER,SAAS,CAAC;IACxD,CAAC;IAEDS,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACR,OAAO,CAACpD,IAAI,CAAC;QAAE9C,IAAI,EAAE;MAAqB,CAAC,CAAC;IACnD,CAAC;IAED2G,MAAMA,CAAA,EAAG;MACP,IAAI,CAACT,OAAO,CAACpD,IAAI,CAAC;QAAE9C,IAAI,EAAE;MAAkB,CAAC,CAAC;IAChD;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}