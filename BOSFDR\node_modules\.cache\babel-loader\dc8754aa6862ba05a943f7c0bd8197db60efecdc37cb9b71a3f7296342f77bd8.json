{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport AdminHeader from './AdminHeader.vue';\nimport AdminSidebar from './AdminSidebar.vue';\nimport adminAuthService from '@/services/adminAuthService';\nimport userManagementService from '@/services/userManagementService';\nimport { Modal } from 'bootstrap';\nexport default {\n  name: 'AdminUsers',\n  components: {\n    AdminHeader,\n    AdminSidebar\n  },\n  data() {\n    return {\n      // UI State\n      sidebarCollapsed: false,\n      showUserDropdown: false,\n      isMobile: false,\n      adminData: null,\n      // Component Data\n      users: [],\n      filteredUsers: [],\n      searchQuery: '',\n      filterStatus: '',\n      filterType: '',\n      currentPage: 1,\n      itemsPerPage: 10,\n      loading: false,\n      userStats: {\n        total: 0,\n        active: 0,\n        pending: 0,\n        admins: 0\n      },\n      // Modal data\n      viewUserData: null,\n      addUserLoading: false,\n      editUserLoading: false,\n      // Add user form\n      addUserForm: {\n        username: '',\n        email: '',\n        first_name: '',\n        last_name: '',\n        role: '',\n        password: '',\n        phone_number: ''\n      },\n      // Edit user form\n      editUserForm: {\n        id: null,\n        username: '',\n        email: '',\n        first_name: '',\n        last_name: '',\n        role: '',\n        status: '',\n        phone_number: ''\n      }\n    };\n  },\n  computed: {\n    activeMenu() {\n      const path = this.$route.path;\n      if (path.includes('/admin/users')) return 'users';\n      if (path.includes('/admin/requests')) return 'requests';\n      if (path.includes('/admin/reports')) return 'reports';\n      if (path.includes('/admin/settings')) return 'settings';\n      if (path.includes('/admin/activity-logs')) return 'activity';\n      if (path.includes('/admin/profile')) return 'profile';\n      return 'dashboard';\n    },\n    paginatedUsers() {\n      const start = (this.currentPage - 1) * this.itemsPerPage;\n      const end = start + this.itemsPerPage;\n      return this.filteredUsers.slice(start, end);\n    },\n    totalPages() {\n      return Math.ceil(this.filteredUsers.length / this.itemsPerPage);\n    },\n    visiblePages() {\n      const pages = [];\n      const start = Math.max(1, this.currentPage - 2);\n      const end = Math.min(this.totalPages, this.currentPage + 2);\n      for (let i = start; i <= end; i++) {\n        pages.push(i);\n      }\n      return pages;\n    }\n  },\n  async mounted() {\n    // Check authentication\n    if (!adminAuthService.isLoggedIn()) {\n      this.$router.push('/admin/login');\n      return;\n    }\n\n    // Initialize UI state\n    this.initializeUI();\n\n    // Make bootstrap available globally for this component\n    this.$bootstrap = {\n      Modal\n    };\n\n    // Load component data\n    await this.loadAdminProfile();\n    await this.loadUserStats();\n    await this.loadUsers();\n  },\n  beforeUnmount() {\n    if (this.handleResize) {\n      window.removeEventListener('resize', this.handleResize);\n    }\n  },\n  methods: {\n    // Initialize UI state\n    initializeUI() {\n      this.isMobile = window.innerWidth <= 768;\n\n      // Load saved sidebar state (only on desktop)\n      if (!this.isMobile) {\n        const saved = localStorage.getItem('adminSidebarCollapsed');\n        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n      } else {\n        this.sidebarCollapsed = true; // Always collapsed on mobile\n      }\n\n      // Setup resize listener\n      this.handleResize = () => {\n        const wasMobile = this.isMobile;\n        this.isMobile = window.innerWidth <= 768;\n        if (this.isMobile && !wasMobile) {\n          this.sidebarCollapsed = true; // Collapse when switching to mobile\n        } else if (!this.isMobile && wasMobile) {\n          // Restore saved state when switching to desktop\n          const saved = localStorage.getItem('adminSidebarCollapsed');\n          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n        }\n      };\n      window.addEventListener('resize', this.handleResize);\n    },\n    // Sidebar toggle\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));\n    },\n    // Menu navigation\n    handleMenuChange(menu) {\n      const routes = {\n        'dashboard': '/admin/dashboard',\n        'users': '/admin/users',\n        'requests': '/admin/requests',\n        'reports': '/admin/reports',\n        'settings': '/admin/settings',\n        'activity': '/admin/activity-logs',\n        'profile': '/admin/profile'\n      };\n\n      // Close sidebar on mobile after navigation\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n      if (routes[menu]) {\n        this.$router.push(routes[menu]);\n      }\n    },\n    // User dropdown toggle\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n    // Menu actions\n    handleMenuAction(action) {\n      if (action === 'profile') {\n        this.$router.push('/admin/profile');\n      } else if (action === 'settings') {\n        this.$router.push('/admin/settings');\n      }\n      this.showUserDropdown = false;\n    },\n    // Close mobile sidebar\n    closeMobileSidebar() {\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n    },\n    // Logout\n    handleLogout() {\n      adminAuthService.logout();\n      this.$router.push('/admin/login');\n    },\n    // Load admin profile data\n    async loadAdminProfile() {\n      try {\n        const response = await adminAuthService.getProfile();\n        if (response.success) {\n          this.adminData = response.data;\n        }\n      } catch (error) {\n        console.error('Failed to load admin data:', error);\n        this.adminData = adminAuthService.getAdminData();\n      }\n    },\n    // Load user statistics\n    async loadUserStats() {\n      try {\n        const response = await userManagementService.getUserStats();\n        if (response.success) {\n          this.userStats = response.data;\n        } else {\n          throw new Error(response.message || 'Failed to load user statistics');\n        }\n      } catch (error) {\n        console.error('Failed to load user statistics:', error);\n        // Keep default stats on error\n        this.userStats = {\n          total: 0,\n          active: 0,\n          pending: 0,\n          admins: 0\n        };\n      }\n    },\n    // Load users data\n    async loadUsers() {\n      this.loading = true;\n      try {\n        const params = {\n          page: this.currentPage,\n          limit: 50,\n          // Load more for client-side filtering\n          search: this.searchQuery || undefined,\n          role: this.filterType || undefined,\n          is_active: this.filterStatus === 'active' ? true : this.filterStatus === 'inactive' ? false : undefined\n        };\n        const response = await userManagementService.getUsers(params);\n        if (response.success) {\n          // Format users for display\n          this.users = response.data.users.map(user => userManagementService.formatUserData(user));\n          this.filteredUsers = [...this.users];\n          this.calculateStats();\n        } else {\n          throw new Error(response.message || 'Failed to load users');\n        }\n      } catch (error) {\n        console.error('Failed to load users:', error);\n        this.$toast?.error?.(error.message || 'Failed to load users');\n\n        // Fallback to empty state\n        this.users = [];\n        this.filteredUsers = [];\n        this.calculateStats();\n      } finally {\n        this.loading = false;\n      }\n    },\n    // Calculate user statistics\n    calculateStats() {\n      this.userStats = {\n        total: this.users.length,\n        active: this.users.filter(u => u.status === 'active').length,\n        pending: this.users.filter(u => u.status === 'pending').length,\n        admins: this.users.filter(u => u.type === 'admin').length\n      };\n    },\n    // Search users\n    searchUsers() {\n      this.filterUsers();\n    },\n    // Filter users based on search and filters\n    filterUsers() {\n      let filtered = [...this.users];\n\n      // Apply search filter\n      if (this.searchQuery) {\n        const query = this.searchQuery.toLowerCase();\n        filtered = filtered.filter(user => user.full_name.toLowerCase().includes(query) || user.email.toLowerCase().includes(query) || user.username.toLowerCase().includes(query));\n      }\n\n      // Apply status filter\n      if (this.filterStatus) {\n        filtered = filtered.filter(user => user.status === this.filterStatus);\n      }\n\n      // Apply type filter\n      if (this.filterType) {\n        filtered = filtered.filter(user => user.type === this.filterType);\n      }\n      this.filteredUsers = filtered;\n      this.currentPage = 1; // Reset to first page\n    },\n    // Change page\n    changePage(page) {\n      if (page >= 1 && page <= this.totalPages) {\n        this.currentPage = page;\n      }\n    },\n    // Get user initials for avatar\n    getInitials(fullName) {\n      if (!fullName) return '?';\n      return fullName.split(' ').map(name => name.charAt(0)).join('').toUpperCase().slice(0, 2);\n    },\n    // Get status badge class\n    getStatusBadgeClass(status) {\n      const classes = {\n        'active': 'bg-success',\n        'inactive': 'bg-secondary',\n        'pending': 'bg-warning',\n        'suspended': 'bg-danger'\n      };\n      return classes[status] || 'bg-secondary';\n    },\n    // Format status text\n    formatStatus(status) {\n      return status.charAt(0).toUpperCase() + status.slice(1);\n    },\n    // Format date\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    },\n    // User actions\n\n    async toggleUserStatus(user) {\n      try {\n        const newStatus = user.status === 'active' ? 'suspended' : 'active';\n        const reason = `Status changed by admin: ${this.adminData?.first_name || 'Admin'}`;\n        const response = await userManagementService.updateUserStatus(user.id, newStatus, reason);\n        if (response.success) {\n          // Update local data\n          user.status = newStatus;\n          this.calculateStats();\n          this.$toast?.success?.(`User ${user.full_name} has been ${newStatus === 'active' ? 'activated' : 'suspended'}.`);\n        } else {\n          throw new Error(response.message || 'Failed to update user status');\n        }\n      } catch (error) {\n        console.error('Failed to update user status:', error);\n        this.$toast?.error?.(error.message || 'Failed to update user status. Please try again.');\n      }\n    },\n    async deleteUser(user) {\n      if (!confirm(`Are you sure you want to delete user \"${user.full_name}\"? This action cannot be undone.`)) {\n        return;\n      }\n      try {\n        const reason = `User deleted by admin: ${this.adminData?.first_name || 'Admin'}`;\n        const response = await userManagementService.deleteUser(user.id, reason);\n        if (response.success) {\n          // Remove from local data\n          const index = this.users.findIndex(u => u.id === user.id);\n          if (index > -1) {\n            this.users.splice(index, 1);\n            this.filterUsers();\n            this.calculateStats();\n          }\n          this.$toast?.success?.(`User ${user.full_name} has been deleted.`);\n        } else {\n          throw new Error(response.message || 'Failed to delete user');\n        }\n      } catch (error) {\n        console.error('Failed to delete user:', error);\n        this.$toast?.error?.(error.message || 'Failed to delete user. Please try again.');\n      }\n    },\n    // Modal methods\n    showAddUserModal() {\n      this.resetAddUserForm();\n      try {\n        const modalElement = document.getElementById('addUserModal');\n        if (modalElement) {\n          const modal = new Modal(modalElement);\n          modal.show();\n        }\n      } catch (error) {\n        console.error('Error showing add user modal:', error);\n        this.$toast?.error?.('Failed to open add user modal');\n      }\n    },\n    resetAddUserForm() {\n      this.addUserForm = {\n        username: '',\n        email: '',\n        first_name: '',\n        last_name: '',\n        role: '',\n        password: '',\n        phone_number: ''\n      };\n    },\n    async submitAddUser() {\n      try {\n        this.addUserLoading = true;\n\n        // Validate form\n        const validation = userManagementService.validateUserData(this.addUserForm);\n        if (!validation.isValid) {\n          this.$toast?.error?.(validation.errors.join(', '));\n          return;\n        }\n        const response = await userManagementService.createUser(this.addUserForm);\n        if (response.success) {\n          this.$toast?.success?.('User created successfully');\n\n          // Close modal\n          try {\n            const modal = Modal.getInstance(document.getElementById('addUserModal'));\n            if (modal) modal.hide();\n          } catch (error) {\n            console.error('Error closing modal:', error);\n          }\n\n          // Reload data\n          await this.loadUsers();\n          await this.loadUserStats();\n        } else {\n          throw new Error(response.message || 'Failed to create user');\n        }\n      } catch (error) {\n        console.error('Failed to create user:', error);\n        this.$toast?.error?.(error.message || 'Failed to create user');\n      } finally {\n        this.addUserLoading = false;\n      }\n    },\n    editUser(user) {\n      this.editUserForm = {\n        id: user.id,\n        username: user.username,\n        email: user.email,\n        first_name: user.first_name,\n        last_name: user.last_name,\n        role: user.type,\n        status: user.status,\n        phone_number: user.phone_number || ''\n      };\n      try {\n        const modalElement = document.getElementById('editUserModal');\n        if (modalElement) {\n          const modal = new Modal(modalElement);\n          modal.show();\n        }\n      } catch (error) {\n        console.error('Error showing edit user modal:', error);\n        this.$toast?.error?.('Failed to open edit user modal');\n      }\n    },\n    async submitEditUser() {\n      try {\n        this.editUserLoading = true;\n\n        // Validate form\n        const validation = userManagementService.validateUserData(this.editUserForm, true);\n        if (!validation.isValid) {\n          this.$toast?.error?.(validation.errors.join(', '));\n          return;\n        }\n        const response = await userManagementService.updateUser(this.editUserForm.id, this.editUserForm);\n        if (response.success) {\n          this.$toast?.success?.('User updated successfully');\n\n          // Close modal\n          try {\n            const modal = Modal.getInstance(document.getElementById('editUserModal'));\n            if (modal) modal.hide();\n          } catch (error) {\n            console.error('Error closing modal:', error);\n          }\n\n          // Update local data\n          const userIndex = this.users.findIndex(u => u.id === this.editUserForm.id);\n          if (userIndex > -1) {\n            this.users[userIndex] = {\n              ...this.users[userIndex],\n              ...this.editUserForm\n            };\n            this.filterUsers();\n          }\n\n          // Reload stats\n          await this.loadUserStats();\n        } else {\n          throw new Error(response.message || 'Failed to update user');\n        }\n      } catch (error) {\n        console.error('Failed to update user:', error);\n        this.$toast?.error?.(error.message || 'Failed to update user');\n      } finally {\n        this.editUserLoading = false;\n      }\n    },\n    async viewUser(user) {\n      try {\n        const response = await userManagementService.getUser(user.id);\n        if (response.success) {\n          this.viewUserData = response.data;\n          try {\n            const modalElement = document.getElementById('viewUserModal');\n            if (modalElement) {\n              const modal = new Modal(modalElement);\n              modal.show();\n            }\n          } catch (error) {\n            console.error('Error showing view user modal:', error);\n            this.$toast?.error?.('Failed to open user details modal');\n          }\n        } else {\n          throw new Error(response.message || 'Failed to load user details');\n        }\n      } catch (error) {\n        console.error('Failed to load user details:', error);\n        this.$toast?.error?.(error.message || 'Failed to load user details');\n      }\n    }\n\n    // Additional user-specific methods can be added here\n    // Navigation handlers are now provided by the mixin\n  }\n};", "map": {"version": 3, "names": ["Ad<PERSON><PERSON><PERSON><PERSON>", "AdminSidebar", "adminAuthService", "userManagementService", "Modal", "name", "components", "data", "sidebarCollapsed", "showUserDropdown", "isMobile", "adminData", "users", "filteredUsers", "searchQuery", "filterStatus", "filterType", "currentPage", "itemsPerPage", "loading", "userStats", "total", "active", "pending", "admins", "viewUserData", "addUserLoading", "editUserLoading", "addUserForm", "username", "email", "first_name", "last_name", "role", "password", "phone_number", "editUserForm", "id", "status", "computed", "activeMenu", "path", "$route", "includes", "paginatedUsers", "start", "end", "slice", "totalPages", "Math", "ceil", "length", "visiblePages", "pages", "max", "min", "i", "push", "mounted", "isLoggedIn", "$router", "initializeUI", "$bootstrap", "loadAdminProfile", "loadUserStats", "loadUsers", "beforeUnmount", "handleResize", "window", "removeEventListener", "methods", "innerWidth", "saved", "localStorage", "getItem", "JSON", "parse", "was<PERSON><PERSON><PERSON>", "addEventListener", "handleSidebarToggle", "setItem", "stringify", "handleMenuChange", "menu", "routes", "handleUserDropdownToggle", "handleMenuAction", "action", "closeMobileSidebar", "handleLogout", "logout", "response", "getProfile", "success", "error", "console", "getAdminData", "getUserStats", "Error", "message", "params", "page", "limit", "search", "undefined", "is_active", "getUsers", "map", "user", "formatUserData", "calculateStats", "$toast", "filter", "u", "type", "searchUsers", "filterUsers", "filtered", "query", "toLowerCase", "full_name", "changePage", "getInitials", "fullName", "split", "char<PERSON>t", "join", "toUpperCase", "getStatusBadgeClass", "classes", "formatStatus", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "toggleUserStatus", "newStatus", "reason", "updateUserStatus", "deleteUser", "confirm", "index", "findIndex", "splice", "showAddUserModal", "resetAddUserForm", "modalElement", "document", "getElementById", "modal", "show", "submitAddUser", "validation", "validateUserData", "<PERSON><PERSON><PERSON><PERSON>", "errors", "createUser", "getInstance", "hide", "editUser", "submitEditUser", "updateUser", "userIndex", "viewUser", "getUser"], "sources": ["D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminUsers.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-users\">\n    <AdminHeader\n      :userName=\"adminData?.first_name || 'Admin'\"\n      :showUserDropdown=\"showUserDropdown\"\n      :sidebarCollapsed=\"sidebarCollapsed\"\n      :activeMenu=\"activeMenu\"\n      @sidebar-toggle=\"handleSidebarToggle\"\n      @user-dropdown-toggle=\"handleUserDropdownToggle\"\n      @menu-action=\"handleMenuAction\"\n      @logout=\"handleLogout\"\n    />\n\n    <!-- Mobile Overlay -->\n    <div\n      class=\"mobile-overlay\"\n      :class=\"{ active: !sidebarCollapsed && isMobile }\"\n      @click=\"closeMobileSidebar\"\n    ></div>\n\n    <div class=\"dashboard-container\">\n      <AdminSidebar\n        :collapsed=\"sidebarCollapsed\"\n        :activeMenu=\"activeMenu\"\n        @menu-change=\"handleMenuChange\"\n        @logout=\"handleLogout\"\n        @toggle-sidebar=\"handleSidebarToggle\"\n      />\n\n      <main class=\"main-content\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n        <div class=\"container-fluid p-4\">\n          <!-- Page Header -->\n          <div class=\"row mb-4\">\n            <div class=\"col-12\">\n              <div class=\"d-flex justify-content-between align-items-center flex-wrap\">\n\n                <div class=\"d-flex gap-2\">\n                  <button class=\"btn btn-outline-success btn-sm\" @click=\"loadUsers\" :disabled=\"loading\">\n                    <i class=\"fas fa-sync-alt me-1\" :class=\"{ 'fa-spin': loading }\"></i>\n                    Refresh\n                  </button>\n                  <button class=\"btn btn-success btn-sm\" @click=\"showAddUserModal\">\n                    <i class=\"fas fa-user-plus me-1\"></i>\n                    Add User\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- User Statistics -->\n          <div class=\"row mb-4\">\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-primary shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-primary text-uppercase mb-1\">\n                        Total Users\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.total || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-users fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-success shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-success text-uppercase mb-1\">\n                        Active Users\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.active || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-user-check fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-warning shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-warning text-uppercase mb-1\">\n                        Pending Verification\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.pending || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-user-clock fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3 mb-3\">\n              <div class=\"card border-left-info shadow h-100 py-2\">\n                <div class=\"card-body\">\n                  <div class=\"row no-gutters align-items-center\">\n                    <div class=\"col mr-2\">\n                      <div class=\"text-xs font-weight-bold text-info text-uppercase mb-1\">\n                        Admin Users\n                      </div>\n                      <div class=\"h5 mb-0 font-weight-bold text-gray-800\">{{ userStats.admins || 0 }}</div>\n                    </div>\n                    <div class=\"col-auto\">\n                      <i class=\"fas fa-user-shield fa-2x text-gray-300\"></i>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Users Table -->\n          <div class=\"row\">\n            <div class=\"col-12\">\n              <div class=\"card shadow\">\n                <div class=\"card-header py-3 d-flex justify-content-between align-items-center\">\n                  <h6 class=\"m-0 font-weight-bold text-primary\">\n                    <i class=\"fas fa-users me-2\"></i>\n                    User List\n                  </h6>\n                  <div class=\"d-flex gap-2\">\n                    <select class=\"form-select form-select-sm\" v-model=\"filterStatus\" @change=\"filterUsers\">\n                      <option value=\"\">All Status</option>\n                      <option value=\"active\">Active</option>\n                      <option value=\"inactive\">Inactive</option>\n                      <option value=\"pending\">Pending</option>\n                      <option value=\"suspended\">Suspended</option>\n                    </select>\n                    <select class=\"form-select form-select-sm\" v-model=\"filterType\" @change=\"filterUsers\">\n                      <option value=\"\">All Types</option>\n                      <option value=\"client\">Clients</option>\n                      <option value=\"admin\">Admins</option>\n                    </select>\n                  </div>\n                </div>\n                <div class=\"card-body\">\n                  <!-- Search Bar -->\n                  <div class=\"row mb-3\">\n                    <div class=\"col-md-6\">\n                      <div class=\"input-group\">\n                        <span class=\"input-group-text\">\n                          <i class=\"fas fa-search\"></i>\n                        </span>\n                        <input\n                          type=\"text\"\n                          class=\"form-control\"\n                          placeholder=\"Search users by name, email, or username...\"\n                          v-model=\"searchQuery\"\n                          @input=\"searchUsers\"\n                        >\n                      </div>\n                    </div>\n                    <div class=\"col-md-6 text-end\">\n                      <span class=\"text-muted\">\n                        Showing {{ filteredUsers.length }} of {{ users.length }} users\n                      </span>\n                    </div>\n                  </div>\n\n                  <!-- Loading State -->\n                  <div v-if=\"loading\" class=\"text-center py-4\">\n                    <div class=\"spinner-border text-primary\" role=\"status\">\n                      <span class=\"visually-hidden\">Loading...</span>\n                    </div>\n                    <p class=\"text-muted mt-2\">Loading users...</p>\n                  </div>\n\n                  <!-- Empty State -->\n                  <div v-else-if=\"filteredUsers.length === 0\" class=\"text-center py-5\">\n                    <i class=\"fas fa-users fa-3x text-gray-300 mb-3\"></i>\n                    <h5 class=\"text-gray-600\">No users found</h5>\n                    <p class=\"text-muted\">\n                      {{ searchQuery ? 'Try adjusting your search criteria.' : 'No users have been registered yet.' }}\n                    </p>\n                  </div>\n\n                  <!-- Users Table -->\n                  <div v-else class=\"table-responsive\">\n                    <table class=\"table table-hover\">\n                      <thead class=\"table-light\">\n                        <tr>\n                          <th>User</th>\n                          <th>Email</th>\n                          <th>Type</th>\n                          <th>Status</th>\n                          <th>Registered</th>\n                          <th>Actions</th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        <tr v-for=\"user in paginatedUsers\" :key=\"user.id\">\n                          <td>\n                            <div class=\"d-flex align-items-center\">\n                              <div class=\"user-avatar me-3\">\n                                <img v-if=\"user.profile_picture\" :src=\"user.profile_picture\" :alt=\"user.full_name\" class=\"rounded-circle\">\n                                <div v-else class=\"avatar-placeholder rounded-circle\">\n                                  {{ getInitials(user.full_name) }}\n                                </div>\n                              </div>\n                              <div>\n                                <div class=\"fw-bold\">{{ user.full_name }}</div>\n                                <div class=\"text-muted small\">@{{ user.username }}</div>\n                              </div>\n                            </div>\n                          </td>\n                          <td>{{ user.email }}</td>\n                          <td>\n                            <span class=\"badge\" :class=\"user.type === 'admin' ? 'bg-primary' : 'bg-info'\">\n                              {{ user.type === 'admin' ? 'Admin' : 'Client' }}\n                            </span>\n                          </td>\n                          <td>\n                            <span class=\"badge\" :class=\"getStatusBadgeClass(user.status)\">\n                              {{ formatStatus(user.status) }}\n                            </span>\n                          </td>\n                          <td>{{ formatDate(user.created_at) }}</td>\n                          <td>\n                            <div class=\"btn-group btn-group-sm\">\n                              <button class=\"btn btn-outline-primary\" @click=\"viewUser(user)\" title=\"View Details\">\n                                <i class=\"fas fa-eye\"></i>\n                              </button>\n                              <button class=\"btn btn-outline-warning\" @click=\"editUser(user)\" title=\"Edit User\">\n                                <i class=\"fas fa-edit\"></i>\n                              </button>\n                              <button\n                                class=\"btn\"\n                                :class=\"user.status === 'active' ? 'btn-outline-warning' : 'btn-outline-success'\"\n                                @click=\"toggleUserStatus(user)\"\n                                :title=\"user.status === 'active' ? 'Suspend User' : 'Activate User'\"\n                              >\n                                <i :class=\"user.status === 'active' ? 'fas fa-pause' : 'fas fa-play'\"></i>\n                              </button>\n                              <button class=\"btn btn-outline-danger\" @click=\"deleteUser(user)\" title=\"Delete User\">\n                                <i class=\"fas fa-trash\"></i>\n                              </button>\n                            </div>\n                          </td>\n                        </tr>\n                      </tbody>\n                    </table>\n                  </div>\n\n                  <!-- Pagination -->\n                  <div v-if=\"totalPages > 1\" class=\"d-flex justify-content-between align-items-center mt-3\">\n                    <div class=\"text-muted\">\n                      Page {{ currentPage }} of {{ totalPages }}\n                    </div>\n                    <nav>\n                      <ul class=\"pagination pagination-sm mb-0\">\n                        <li class=\"page-item\" :class=\"{ disabled: currentPage === 1 }\">\n                          <button class=\"page-link\" @click=\"changePage(currentPage - 1)\" :disabled=\"currentPage === 1\">\n                            Previous\n                          </button>\n                        </li>\n                        <li\n                          v-for=\"page in visiblePages\"\n                          :key=\"page\"\n                          class=\"page-item\"\n                          :class=\"{ active: page === currentPage }\"\n                        >\n                          <button class=\"page-link\" @click=\"changePage(page)\">{{ page }}</button>\n                        </li>\n                        <li class=\"page-item\" :class=\"{ disabled: currentPage === totalPages }\">\n                          <button class=\"page-link\" @click=\"changePage(currentPage + 1)\" :disabled=\"currentPage === totalPages\">\n                            Next\n                          </button>\n                        </li>\n                      </ul>\n                    </nav>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n\n    <!-- Add User Modal -->\n    <div class=\"modal fade\" id=\"addUserModal\" tabindex=\"-1\" aria-labelledby=\"addUserModalLabel\" aria-hidden=\"true\">\n      <div class=\"modal-dialog modal-lg\">\n        <div class=\"modal-content\">\n          <div class=\"modal-header\">\n            <h5 class=\"modal-title\" id=\"addUserModalLabel\">\n              <i class=\"fas fa-user-plus me-2\"></i>\n              Add New User\n            </h5>\n            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\n          </div>\n          <div class=\"modal-body\">\n            <form @submit.prevent=\"submitAddUser\">\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addUsername\" class=\"form-label\">Username *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"addUsername\"\n                    v-model=\"addUserForm.username\"\n                    required\n                  >\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addEmail\" class=\"form-label\">Email *</label>\n                  <input\n                    type=\"email\"\n                    class=\"form-control\"\n                    id=\"addEmail\"\n                    v-model=\"addUserForm.email\"\n                    required\n                  >\n                </div>\n              </div>\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addFirstName\" class=\"form-label\">First Name *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"addFirstName\"\n                    v-model=\"addUserForm.first_name\"\n                    required\n                  >\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addLastName\" class=\"form-label\">Last Name *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"addLastName\"\n                    v-model=\"addUserForm.last_name\"\n                    required\n                  >\n                </div>\n              </div>\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addRole\" class=\"form-label\">Role *</label>\n                  <select class=\"form-select\" id=\"addRole\" v-model=\"addUserForm.role\" required>\n                    <option value=\"\">Select Role</option>\n                    <option value=\"admin\">Administrator</option>\n                    <option value=\"client\">Client</option>\n                  </select>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"addPassword\" class=\"form-label\">Password *</label>\n                  <input\n                    type=\"password\"\n                    class=\"form-control\"\n                    id=\"addPassword\"\n                    v-model=\"addUserForm.password\"\n                    required\n                    minlength=\"6\"\n                  >\n                </div>\n              </div>\n              <div class=\"mb-3\">\n                <label for=\"addPhone\" class=\"form-label\">Phone Number</label>\n                <input\n                  type=\"tel\"\n                  class=\"form-control\"\n                  id=\"addPhone\"\n                  v-model=\"addUserForm.phone_number\"\n                >\n              </div>\n            </form>\n          </div>\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>\n            <button type=\"button\" class=\"btn btn-primary\" @click=\"submitAddUser\" :disabled=\"addUserLoading\">\n              <span v-if=\"addUserLoading\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\n              <i v-else class=\"fas fa-plus me-2\"></i>\n              {{ addUserLoading ? 'Creating...' : 'Create User' }}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Edit User Modal -->\n    <div class=\"modal fade\" id=\"editUserModal\" tabindex=\"-1\" aria-labelledby=\"editUserModalLabel\" aria-hidden=\"true\">\n      <div class=\"modal-dialog modal-lg\">\n        <div class=\"modal-content\">\n          <div class=\"modal-header\">\n            <h5 class=\"modal-title\" id=\"editUserModalLabel\">\n              <i class=\"fas fa-user-edit me-2\"></i>\n              Edit User\n            </h5>\n            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\n          </div>\n          <div class=\"modal-body\">\n            <form @submit.prevent=\"submitEditUser\" v-if=\"editUserForm.id\">\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editUsername\" class=\"form-label\">Username *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"editUsername\"\n                    v-model=\"editUserForm.username\"\n                    required\n                  >\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editEmail\" class=\"form-label\">Email *</label>\n                  <input\n                    type=\"email\"\n                    class=\"form-control\"\n                    id=\"editEmail\"\n                    v-model=\"editUserForm.email\"\n                    required\n                  >\n                </div>\n              </div>\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editFirstName\" class=\"form-label\">First Name *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"editFirstName\"\n                    v-model=\"editUserForm.first_name\"\n                    required\n                  >\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editLastName\" class=\"form-label\">Last Name *</label>\n                  <input\n                    type=\"text\"\n                    class=\"form-control\"\n                    id=\"editLastName\"\n                    v-model=\"editUserForm.last_name\"\n                    required\n                  >\n                </div>\n              </div>\n              <div class=\"row\">\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editRole\" class=\"form-label\">Role *</label>\n                  <select class=\"form-select\" id=\"editRole\" v-model=\"editUserForm.role\" required>\n                    <option value=\"admin\">Administrator</option>\n                    <option value=\"client\">Client</option>\n                  </select>\n                </div>\n                <div class=\"col-md-6 mb-3\">\n                  <label for=\"editStatus\" class=\"form-label\">Status *</label>\n                  <select class=\"form-select\" id=\"editStatus\" v-model=\"editUserForm.status\" required>\n                    <option value=\"active\">Active</option>\n                    <option value=\"inactive\">Inactive</option>\n                    <option value=\"suspended\">Suspended</option>\n                    <option value=\"pending\">Pending</option>\n                  </select>\n                </div>\n              </div>\n              <div class=\"mb-3\">\n                <label for=\"editPhone\" class=\"form-label\">Phone Number</label>\n                <input\n                  type=\"tel\"\n                  class=\"form-control\"\n                  id=\"editPhone\"\n                  v-model=\"editUserForm.phone_number\"\n                >\n              </div>\n            </form>\n          </div>\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Cancel</button>\n            <button type=\"button\" class=\"btn btn-primary\" @click=\"submitEditUser\" :disabled=\"editUserLoading\">\n              <span v-if=\"editUserLoading\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span>\n              <i v-else class=\"fas fa-save me-2\"></i>\n              {{ editUserLoading ? 'Updating...' : 'Update User' }}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- View User Modal -->\n    <div class=\"modal fade\" id=\"viewUserModal\" tabindex=\"-1\" aria-labelledby=\"viewUserModalLabel\" aria-hidden=\"true\">\n      <div class=\"modal-dialog modal-lg\">\n        <div class=\"modal-content\">\n          <div class=\"modal-header\">\n            <h5 class=\"modal-title\" id=\"viewUserModalLabel\">\n              <i class=\"fas fa-user me-2\"></i>\n              User Details\n            </h5>\n            <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Close\"></button>\n          </div>\n          <div class=\"modal-body\" v-if=\"viewUserData\">\n            <div class=\"row\">\n              <div class=\"col-md-4 text-center mb-4\">\n                <div class=\"user-avatar-large mx-auto mb-3\">\n                  <img v-if=\"viewUserData.profile_picture\" :src=\"viewUserData.profile_picture\" :alt=\"viewUserData.full_name\" class=\"rounded-circle\">\n                  <div v-else class=\"avatar-placeholder-large rounded-circle\">\n                    {{ getInitials(viewUserData.full_name) }}\n                  </div>\n                </div>\n                <h5>{{ viewUserData.full_name }}</h5>\n                <span class=\"badge\" :class=\"getStatusBadgeClass(viewUserData.status)\">\n                  {{ formatStatus(viewUserData.status) }}\n                </span>\n              </div>\n              <div class=\"col-md-8\">\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Username:</strong></div>\n                  <div class=\"col-sm-8\">{{ viewUserData.username }}</div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Email:</strong></div>\n                  <div class=\"col-sm-8\">{{ viewUserData.email }}</div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Type:</strong></div>\n                  <div class=\"col-sm-8\">\n                    <span class=\"badge\" :class=\"viewUserData.type === 'admin' ? 'bg-primary' : 'bg-info'\">\n                      {{ viewUserData.type === 'admin' ? 'Administrator' : 'Client' }}\n                    </span>\n                  </div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Phone:</strong></div>\n                  <div class=\"col-sm-8\">{{ viewUserData.phone_number || 'N/A' }}</div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Registered:</strong></div>\n                  <div class=\"col-sm-8\">{{ formatDate(viewUserData.created_at) }}</div>\n                </div>\n                <div class=\"row mb-3\">\n                  <div class=\"col-sm-4\"><strong>Last Login:</strong></div>\n                  <div class=\"col-sm-8\">{{ viewUserData.last_login ? formatDate(viewUserData.last_login) : 'Never' }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Close</button>\n            <button type=\"button\" class=\"btn btn-primary\" @click=\"editUser(viewUserData)\" data-bs-dismiss=\"modal\">\n              <i class=\"fas fa-edit me-2\"></i>\n              Edit User\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport AdminHeader from './AdminHeader.vue';\nimport AdminSidebar from './AdminSidebar.vue';\nimport adminAuthService from '@/services/adminAuthService';\nimport userManagementService from '@/services/userManagementService';\nimport { Modal } from 'bootstrap';\n\nexport default {\n  name: 'AdminUsers',\n  components: {\n    AdminHeader,\n    AdminSidebar\n  },\n\n  data() {\n    return {\n      // UI State\n      sidebarCollapsed: false,\n      showUserDropdown: false,\n      isMobile: false,\n      adminData: null,\n      // Component Data\n      users: [],\n      filteredUsers: [],\n      searchQuery: '',\n      filterStatus: '',\n      filterType: '',\n      currentPage: 1,\n      itemsPerPage: 10,\n      loading: false,\n      userStats: {\n        total: 0,\n        active: 0,\n        pending: 0,\n        admins: 0\n      },\n\n      // Modal data\n      viewUserData: null,\n      addUserLoading: false,\n      editUserLoading: false,\n\n      // Add user form\n      addUserForm: {\n        username: '',\n        email: '',\n        first_name: '',\n        last_name: '',\n        role: '',\n        password: '',\n        phone_number: ''\n      },\n\n      // Edit user form\n      editUserForm: {\n        id: null,\n        username: '',\n        email: '',\n        first_name: '',\n        last_name: '',\n        role: '',\n        status: '',\n        phone_number: ''\n      }\n    };\n  },\n\n  computed: {\n    activeMenu() {\n      const path = this.$route.path;\n      if (path.includes('/admin/users')) return 'users';\n      if (path.includes('/admin/requests')) return 'requests';\n      if (path.includes('/admin/reports')) return 'reports';\n      if (path.includes('/admin/settings')) return 'settings';\n      if (path.includes('/admin/activity-logs')) return 'activity';\n      if (path.includes('/admin/profile')) return 'profile';\n      return 'dashboard';\n    },\n\n    paginatedUsers() {\n      const start = (this.currentPage - 1) * this.itemsPerPage;\n      const end = start + this.itemsPerPage;\n      return this.filteredUsers.slice(start, end);\n    },\n\n    totalPages() {\n      return Math.ceil(this.filteredUsers.length / this.itemsPerPage);\n    },\n\n    visiblePages() {\n      const pages = [];\n      const start = Math.max(1, this.currentPage - 2);\n      const end = Math.min(this.totalPages, this.currentPage + 2);\n\n      for (let i = start; i <= end; i++) {\n        pages.push(i);\n      }\n      return pages;\n    }\n  },\n\n  async mounted() {\n    // Check authentication\n    if (!adminAuthService.isLoggedIn()) {\n      this.$router.push('/admin/login');\n      return;\n    }\n\n    // Initialize UI state\n    this.initializeUI();\n\n    // Make bootstrap available globally for this component\n    this.$bootstrap = { Modal };\n\n    // Load component data\n    await this.loadAdminProfile();\n    await this.loadUserStats();\n    await this.loadUsers();\n  },\n\n  beforeUnmount() {\n    if (this.handleResize) {\n      window.removeEventListener('resize', this.handleResize);\n    }\n  },\n\n  methods: {\n    // Initialize UI state\n    initializeUI() {\n      this.isMobile = window.innerWidth <= 768;\n\n      // Load saved sidebar state (only on desktop)\n      if (!this.isMobile) {\n        const saved = localStorage.getItem('adminSidebarCollapsed');\n        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n      } else {\n        this.sidebarCollapsed = true; // Always collapsed on mobile\n      }\n\n      // Setup resize listener\n      this.handleResize = () => {\n        const wasMobile = this.isMobile;\n        this.isMobile = window.innerWidth <= 768;\n\n        if (this.isMobile && !wasMobile) {\n          this.sidebarCollapsed = true; // Collapse when switching to mobile\n        } else if (!this.isMobile && wasMobile) {\n          // Restore saved state when switching to desktop\n          const saved = localStorage.getItem('adminSidebarCollapsed');\n          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;\n        }\n      };\n      window.addEventListener('resize', this.handleResize);\n    },\n\n    // Sidebar toggle\n    handleSidebarToggle() {\n      this.sidebarCollapsed = !this.sidebarCollapsed;\n      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));\n    },\n\n    // Menu navigation\n    handleMenuChange(menu) {\n      const routes = {\n        'dashboard': '/admin/dashboard',\n        'users': '/admin/users',\n        'requests': '/admin/requests',\n        'reports': '/admin/reports',\n        'settings': '/admin/settings',\n        'activity': '/admin/activity-logs',\n        'profile': '/admin/profile'\n      };\n\n      // Close sidebar on mobile after navigation\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n\n      if (routes[menu]) {\n        this.$router.push(routes[menu]);\n      }\n    },\n\n    // User dropdown toggle\n    handleUserDropdownToggle() {\n      this.showUserDropdown = !this.showUserDropdown;\n    },\n\n    // Menu actions\n    handleMenuAction(action) {\n      if (action === 'profile') {\n        this.$router.push('/admin/profile');\n      } else if (action === 'settings') {\n        this.$router.push('/admin/settings');\n      }\n      this.showUserDropdown = false;\n    },\n\n    // Close mobile sidebar\n    closeMobileSidebar() {\n      if (this.isMobile) {\n        this.sidebarCollapsed = true;\n      }\n    },\n\n    // Logout\n    handleLogout() {\n      adminAuthService.logout();\n      this.$router.push('/admin/login');\n    },\n\n    // Load admin profile data\n    async loadAdminProfile() {\n      try {\n        const response = await adminAuthService.getProfile();\n        if (response.success) {\n          this.adminData = response.data;\n        }\n      } catch (error) {\n        console.error('Failed to load admin data:', error);\n        this.adminData = adminAuthService.getAdminData();\n      }\n    },\n\n    // Load user statistics\n    async loadUserStats() {\n      try {\n        const response = await userManagementService.getUserStats();\n\n        if (response.success) {\n          this.userStats = response.data;\n        } else {\n          throw new Error(response.message || 'Failed to load user statistics');\n        }\n      } catch (error) {\n        console.error('Failed to load user statistics:', error);\n        // Keep default stats on error\n        this.userStats = {\n          total: 0,\n          active: 0,\n          pending: 0,\n          admins: 0\n        };\n      }\n    },\n\n    // Load users data\n    async loadUsers() {\n      this.loading = true;\n      try {\n        const params = {\n          page: this.currentPage,\n          limit: 50, // Load more for client-side filtering\n          search: this.searchQuery || undefined,\n          role: this.filterType || undefined,\n          is_active: this.filterStatus === 'active' ? true :\n                     this.filterStatus === 'inactive' ? false : undefined\n        };\n\n        const response = await userManagementService.getUsers(params);\n\n        if (response.success) {\n          // Format users for display\n          this.users = response.data.users.map(user =>\n            userManagementService.formatUserData(user)\n          );\n\n          this.filteredUsers = [...this.users];\n          this.calculateStats();\n        } else {\n          throw new Error(response.message || 'Failed to load users');\n        }\n      } catch (error) {\n        console.error('Failed to load users:', error);\n        this.$toast?.error?.(error.message || 'Failed to load users');\n\n        // Fallback to empty state\n        this.users = [];\n        this.filteredUsers = [];\n        this.calculateStats();\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // Calculate user statistics\n    calculateStats() {\n      this.userStats = {\n        total: this.users.length,\n        active: this.users.filter(u => u.status === 'active').length,\n        pending: this.users.filter(u => u.status === 'pending').length,\n        admins: this.users.filter(u => u.type === 'admin').length\n      };\n    },\n\n    // Search users\n    searchUsers() {\n      this.filterUsers();\n    },\n\n    // Filter users based on search and filters\n    filterUsers() {\n      let filtered = [...this.users];\n\n      // Apply search filter\n      if (this.searchQuery) {\n        const query = this.searchQuery.toLowerCase();\n        filtered = filtered.filter(user =>\n          user.full_name.toLowerCase().includes(query) ||\n          user.email.toLowerCase().includes(query) ||\n          user.username.toLowerCase().includes(query)\n        );\n      }\n\n      // Apply status filter\n      if (this.filterStatus) {\n        filtered = filtered.filter(user => user.status === this.filterStatus);\n      }\n\n      // Apply type filter\n      if (this.filterType) {\n        filtered = filtered.filter(user => user.type === this.filterType);\n      }\n\n      this.filteredUsers = filtered;\n      this.currentPage = 1; // Reset to first page\n    },\n\n    // Change page\n    changePage(page) {\n      if (page >= 1 && page <= this.totalPages) {\n        this.currentPage = page;\n      }\n    },\n\n    // Get user initials for avatar\n    getInitials(fullName) {\n      if (!fullName) return '?';\n      return fullName.split(' ').map(name => name.charAt(0)).join('').toUpperCase().slice(0, 2);\n    },\n\n    // Get status badge class\n    getStatusBadgeClass(status) {\n      const classes = {\n        'active': 'bg-success',\n        'inactive': 'bg-secondary',\n        'pending': 'bg-warning',\n        'suspended': 'bg-danger'\n      };\n      return classes[status] || 'bg-secondary';\n    },\n\n    // Format status text\n    formatStatus(status) {\n      return status.charAt(0).toUpperCase() + status.slice(1);\n    },\n\n    // Format date\n    formatDate(dateString) {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n      });\n    },\n\n    // User actions\n\n\n    async toggleUserStatus(user) {\n      try {\n        const newStatus = user.status === 'active' ? 'suspended' : 'active';\n        const reason = `Status changed by admin: ${this.adminData?.first_name || 'Admin'}`;\n\n        const response = await userManagementService.updateUserStatus(user.id, newStatus, reason);\n\n        if (response.success) {\n          // Update local data\n          user.status = newStatus;\n          this.calculateStats();\n\n          this.$toast?.success?.(`User ${user.full_name} has been ${newStatus === 'active' ? 'activated' : 'suspended'}.`);\n        } else {\n          throw new Error(response.message || 'Failed to update user status');\n        }\n      } catch (error) {\n        console.error('Failed to update user status:', error);\n        this.$toast?.error?.(error.message || 'Failed to update user status. Please try again.');\n      }\n    },\n\n    async deleteUser(user) {\n      if (!confirm(`Are you sure you want to delete user \"${user.full_name}\"? This action cannot be undone.`)) {\n        return;\n      }\n\n      try {\n        const reason = `User deleted by admin: ${this.adminData?.first_name || 'Admin'}`;\n        const response = await userManagementService.deleteUser(user.id, reason);\n\n        if (response.success) {\n          // Remove from local data\n          const index = this.users.findIndex(u => u.id === user.id);\n          if (index > -1) {\n            this.users.splice(index, 1);\n            this.filterUsers();\n            this.calculateStats();\n          }\n\n          this.$toast?.success?.(`User ${user.full_name} has been deleted.`);\n        } else {\n          throw new Error(response.message || 'Failed to delete user');\n        }\n      } catch (error) {\n        console.error('Failed to delete user:', error);\n        this.$toast?.error?.(error.message || 'Failed to delete user. Please try again.');\n      }\n    },\n\n\n\n    // Modal methods\n    showAddUserModal() {\n      this.resetAddUserForm();\n      try {\n        const modalElement = document.getElementById('addUserModal');\n        if (modalElement) {\n          const modal = new Modal(modalElement);\n          modal.show();\n        }\n      } catch (error) {\n        console.error('Error showing add user modal:', error);\n        this.$toast?.error?.('Failed to open add user modal');\n      }\n    },\n\n    resetAddUserForm() {\n      this.addUserForm = {\n        username: '',\n        email: '',\n        first_name: '',\n        last_name: '',\n        role: '',\n        password: '',\n        phone_number: ''\n      };\n    },\n\n    async submitAddUser() {\n      try {\n        this.addUserLoading = true;\n\n        // Validate form\n        const validation = userManagementService.validateUserData(this.addUserForm);\n        if (!validation.isValid) {\n          this.$toast?.error?.(validation.errors.join(', '));\n          return;\n        }\n\n        const response = await userManagementService.createUser(this.addUserForm);\n\n        if (response.success) {\n          this.$toast?.success?.('User created successfully');\n\n          // Close modal\n          try {\n            const modal = Modal.getInstance(document.getElementById('addUserModal'));\n            if (modal) modal.hide();\n          } catch (error) {\n            console.error('Error closing modal:', error);\n          }\n\n          // Reload data\n          await this.loadUsers();\n          await this.loadUserStats();\n        } else {\n          throw new Error(response.message || 'Failed to create user');\n        }\n      } catch (error) {\n        console.error('Failed to create user:', error);\n        this.$toast?.error?.(error.message || 'Failed to create user');\n      } finally {\n        this.addUserLoading = false;\n      }\n    },\n\n    editUser(user) {\n      this.editUserForm = {\n        id: user.id,\n        username: user.username,\n        email: user.email,\n        first_name: user.first_name,\n        last_name: user.last_name,\n        role: user.type,\n        status: user.status,\n        phone_number: user.phone_number || ''\n      };\n\n      try {\n        const modalElement = document.getElementById('editUserModal');\n        if (modalElement) {\n          const modal = new Modal(modalElement);\n          modal.show();\n        }\n      } catch (error) {\n        console.error('Error showing edit user modal:', error);\n        this.$toast?.error?.('Failed to open edit user modal');\n      }\n    },\n\n    async submitEditUser() {\n      try {\n        this.editUserLoading = true;\n\n        // Validate form\n        const validation = userManagementService.validateUserData(this.editUserForm, true);\n        if (!validation.isValid) {\n          this.$toast?.error?.(validation.errors.join(', '));\n          return;\n        }\n\n        const response = await userManagementService.updateUser(this.editUserForm.id, this.editUserForm);\n\n        if (response.success) {\n          this.$toast?.success?.('User updated successfully');\n\n          // Close modal\n          try {\n            const modal = Modal.getInstance(document.getElementById('editUserModal'));\n            if (modal) modal.hide();\n          } catch (error) {\n            console.error('Error closing modal:', error);\n          }\n\n          // Update local data\n          const userIndex = this.users.findIndex(u => u.id === this.editUserForm.id);\n          if (userIndex > -1) {\n            this.users[userIndex] = { ...this.users[userIndex], ...this.editUserForm };\n            this.filterUsers();\n          }\n\n          // Reload stats\n          await this.loadUserStats();\n        } else {\n          throw new Error(response.message || 'Failed to update user');\n        }\n      } catch (error) {\n        console.error('Failed to update user:', error);\n        this.$toast?.error?.(error.message || 'Failed to update user');\n      } finally {\n        this.editUserLoading = false;\n      }\n    },\n\n    async viewUser(user) {\n      try {\n        const response = await userManagementService.getUser(user.id);\n\n        if (response.success) {\n          this.viewUserData = response.data;\n          try {\n            const modalElement = document.getElementById('viewUserModal');\n            if (modalElement) {\n              const modal = new Modal(modalElement);\n              modal.show();\n            }\n          } catch (error) {\n            console.error('Error showing view user modal:', error);\n            this.$toast?.error?.('Failed to open user details modal');\n          }\n        } else {\n          throw new Error(response.message || 'Failed to load user details');\n        }\n      } catch (error) {\n        console.error('Failed to load user details:', error);\n        this.$toast?.error?.(error.message || 'Failed to load user details');\n      }\n    },\n\n    // Additional user-specific methods can be added here\n    // Navigation handlers are now provided by the mixin\n  }\n};\n</script>\n\n<style scoped>\n@import './css/adminDashboard.css';\n\n/* User avatar styles */\n.user-avatar {\n  width: 40px;\n  height: 40px;\n}\n\n.user-avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.avatar-placeholder {\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: 600;\n  font-size: 0.875rem;\n  border-radius: 50%;\n}\n\n.user-avatar-large {\n  width: 80px;\n  height: 80px;\n}\n\n.avatar-placeholder-large {\n  width: 80px;\n  height: 80px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  font-size: 24px;\n  border-radius: 50%;\n}\n\n/* Modal styles */\n.modal-lg {\n  max-width: 800px;\n}\n\n/* Form styles */\n.form-label {\n  font-weight: 600;\n  color: #495057;\n}\n\n.form-control:focus,\n.form-select:focus {\n  border-color: #80bdff;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n/* Loading states */\n.spinner-border-sm {\n  width: 1rem;\n  height: 1rem;\n}\n\n/* Table improvements */\n.table th {\n  border-top: none;\n  font-weight: 600;\n  color: #5a5c69;\n  font-size: 0.875rem;\n}\n\n.table td {\n  vertical-align: middle;\n  font-size: 0.875rem;\n}\n\n.table-hover tbody tr:hover {\n  background-color: rgba(0, 123, 255, 0.05);\n}\n\n/* Button group improvements */\n.btn-group-sm .btn {\n  padding: 0.375rem 0.5rem;\n  font-size: 0.75rem;\n}\n\n/* Search and filter improvements */\n.form-select-sm {\n  font-size: 0.875rem;\n  padding: 0.375rem 0.75rem;\n}\n\n/* Pagination improvements */\n.pagination-sm .page-link {\n  padding: 0.375rem 0.75rem;\n  font-size: 0.875rem;\n}\n\n/* Badge improvements */\n.badge {\n  font-size: 0.75rem;\n  padding: 0.375rem 0.75rem;\n  border-radius: 0.5rem;\n}\n\n/* Responsive improvements */\n@media (max-width: 768px) {\n  .table-responsive {\n    font-size: 0.8rem;\n  }\n\n  .btn-group-sm .btn {\n    padding: 0.25rem 0.375rem;\n    font-size: 0.7rem;\n  }\n\n  .user-avatar {\n    width: 32px;\n    height: 32px;\n  }\n\n  .avatar-placeholder {\n    width: 32px;\n    height: 32px;\n    font-size: 0.75rem;\n  }\n}\n\n@media (max-width: 576px) {\n  .d-flex.gap-2 {\n    flex-direction: column;\n    gap: 0.5rem !important;\n  }\n\n  .btn-group {\n    flex-direction: column;\n  }\n\n  .btn-group .btn {\n    border-radius: 0.375rem !important;\n    margin-bottom: 0.25rem;\n  }\n}\n</style>\n"], "mappings": ";;;;AAgjBA,OAAOA,WAAU,MAAO,mBAAmB;AAC3C,OAAOC,YAAW,MAAO,oBAAoB;AAC7C,OAAOC,gBAAe,MAAO,6BAA6B;AAC1D,OAAOC,qBAAoB,MAAO,kCAAkC;AACpE,SAASC,KAAI,QAAS,WAAW;AAEjC,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,UAAU,EAAE;IACVN,WAAW;IACXC;EACF,CAAC;EAEDM,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,gBAAgB,EAAE,KAAK;MACvBC,gBAAgB,EAAE,KAAK;MACvBC,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE,IAAI;MACf;MACAC,KAAK,EAAE,EAAE;MACTC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE,EAAE;MAChBC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE;QACTC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE,CAAC;QACVC,MAAM,EAAE;MACV,CAAC;MAED;MACAC,YAAY,EAAE,IAAI;MAClBC,cAAc,EAAE,KAAK;MACrBC,eAAe,EAAE,KAAK;MAEtB;MACAC,WAAW,EAAE;QACXC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,EAAE;QACTC,UAAU,EAAE,EAAE;QACdC,SAAS,EAAE,EAAE;QACbC,IAAI,EAAE,EAAE;QACRC,QAAQ,EAAE,EAAE;QACZC,YAAY,EAAE;MAChB,CAAC;MAED;MACAC,YAAY,EAAE;QACZC,EAAE,EAAE,IAAI;QACRR,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,EAAE;QACTC,UAAU,EAAE,EAAE;QACdC,SAAS,EAAE,EAAE;QACbC,IAAI,EAAE,EAAE;QACRK,MAAM,EAAE,EAAE;QACVH,YAAY,EAAE;MAChB;IACF,CAAC;EACH,CAAC;EAEDI,QAAQ,EAAE;IACRC,UAAUA,CAAA,EAAG;MACX,MAAMC,IAAG,GAAI,IAAI,CAACC,MAAM,CAACD,IAAI;MAC7B,IAAIA,IAAI,CAACE,QAAQ,CAAC,cAAc,CAAC,EAAE,OAAO,OAAO;MACjD,IAAIF,IAAI,CAACE,QAAQ,CAAC,iBAAiB,CAAC,EAAE,OAAO,UAAU;MACvD,IAAIF,IAAI,CAACE,QAAQ,CAAC,gBAAgB,CAAC,EAAE,OAAO,SAAS;MACrD,IAAIF,IAAI,CAACE,QAAQ,CAAC,iBAAiB,CAAC,EAAE,OAAO,UAAU;MACvD,IAAIF,IAAI,CAACE,QAAQ,CAAC,sBAAsB,CAAC,EAAE,OAAO,UAAU;MAC5D,IAAIF,IAAI,CAACE,QAAQ,CAAC,gBAAgB,CAAC,EAAE,OAAO,SAAS;MACrD,OAAO,WAAW;IACpB,CAAC;IAEDC,cAAcA,CAAA,EAAG;MACf,MAAMC,KAAI,GAAI,CAAC,IAAI,CAAC5B,WAAU,GAAI,CAAC,IAAI,IAAI,CAACC,YAAY;MACxD,MAAM4B,GAAE,GAAID,KAAI,GAAI,IAAI,CAAC3B,YAAY;MACrC,OAAO,IAAI,CAACL,aAAa,CAACkC,KAAK,CAACF,KAAK,EAAEC,GAAG,CAAC;IAC7C,CAAC;IAEDE,UAAUA,CAAA,EAAG;MACX,OAAOC,IAAI,CAACC,IAAI,CAAC,IAAI,CAACrC,aAAa,CAACsC,MAAK,GAAI,IAAI,CAACjC,YAAY,CAAC;IACjE,CAAC;IAEDkC,YAAYA,CAAA,EAAG;MACb,MAAMC,KAAI,GAAI,EAAE;MAChB,MAAMR,KAAI,GAAII,IAAI,CAACK,GAAG,CAAC,CAAC,EAAE,IAAI,CAACrC,WAAU,GAAI,CAAC,CAAC;MAC/C,MAAM6B,GAAE,GAAIG,IAAI,CAACM,GAAG,CAAC,IAAI,CAACP,UAAU,EAAE,IAAI,CAAC/B,WAAU,GAAI,CAAC,CAAC;MAE3D,KAAK,IAAIuC,CAAA,GAAIX,KAAK,EAAEW,CAAA,IAAKV,GAAG,EAAEU,CAAC,EAAE,EAAE;QACjCH,KAAK,CAACI,IAAI,CAACD,CAAC,CAAC;MACf;MACA,OAAOH,KAAK;IACd;EACF,CAAC;EAED,MAAMK,OAAOA,CAAA,EAAG;IACd;IACA,IAAI,CAACxD,gBAAgB,CAACyD,UAAU,CAAC,CAAC,EAAE;MAClC,IAAI,CAACC,OAAO,CAACH,IAAI,CAAC,cAAc,CAAC;MACjC;IACF;;IAEA;IACA,IAAI,CAACI,YAAY,CAAC,CAAC;;IAEnB;IACA,IAAI,CAACC,UAAS,GAAI;MAAE1D;IAAM,CAAC;;IAE3B;IACA,MAAM,IAAI,CAAC2D,gBAAgB,CAAC,CAAC;IAC7B,MAAM,IAAI,CAACC,aAAa,CAAC,CAAC;IAC1B,MAAM,IAAI,CAACC,SAAS,CAAC,CAAC;EACxB,CAAC;EAEDC,aAAaA,CAAA,EAAG;IACd,IAAI,IAAI,CAACC,YAAY,EAAE;MACrBC,MAAM,CAACC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACF,YAAY,CAAC;IACzD;EACF,CAAC;EAEDG,OAAO,EAAE;IACP;IACAT,YAAYA,CAAA,EAAG;MACb,IAAI,CAACnD,QAAO,GAAI0D,MAAM,CAACG,UAAS,IAAK,GAAG;;MAExC;MACA,IAAI,CAAC,IAAI,CAAC7D,QAAQ,EAAE;QAClB,MAAM8D,KAAI,GAAIC,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;QAC3D,IAAI,CAAClE,gBAAe,GAAIgE,KAAI,GAAIG,IAAI,CAACC,KAAK,CAACJ,KAAK,IAAI,KAAK;MAC3D,OAAO;QACL,IAAI,CAAChE,gBAAe,GAAI,IAAI,EAAE;MAChC;;MAEA;MACA,IAAI,CAAC2D,YAAW,GAAI,MAAM;QACxB,MAAMU,SAAQ,GAAI,IAAI,CAACnE,QAAQ;QAC/B,IAAI,CAACA,QAAO,GAAI0D,MAAM,CAACG,UAAS,IAAK,GAAG;QAExC,IAAI,IAAI,CAAC7D,QAAO,IAAK,CAACmE,SAAS,EAAE;UAC/B,IAAI,CAACrE,gBAAe,GAAI,IAAI,EAAE;QAChC,OAAO,IAAI,CAAC,IAAI,CAACE,QAAO,IAAKmE,SAAS,EAAE;UACtC;UACA,MAAML,KAAI,GAAIC,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;UAC3D,IAAI,CAAClE,gBAAe,GAAIgE,KAAI,GAAIG,IAAI,CAACC,KAAK,CAACJ,KAAK,IAAI,KAAK;QAC3D;MACF,CAAC;MACDJ,MAAM,CAACU,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACX,YAAY,CAAC;IACtD,CAAC;IAED;IACAY,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAACvE,gBAAe,GAAI,CAAC,IAAI,CAACA,gBAAgB;MAC9CiE,YAAY,CAACO,OAAO,CAAC,uBAAuB,EAAEL,IAAI,CAACM,SAAS,CAAC,IAAI,CAACzE,gBAAgB,CAAC,CAAC;IACtF,CAAC;IAED;IACA0E,gBAAgBA,CAACC,IAAI,EAAE;MACrB,MAAMC,MAAK,GAAI;QACb,WAAW,EAAE,kBAAkB;QAC/B,OAAO,EAAE,cAAc;QACvB,UAAU,EAAE,iBAAiB;QAC7B,SAAS,EAAE,gBAAgB;QAC3B,UAAU,EAAE,iBAAiB;QAC7B,UAAU,EAAE,sBAAsB;QAClC,SAAS,EAAE;MACb,CAAC;;MAED;MACA,IAAI,IAAI,CAAC1E,QAAQ,EAAE;QACjB,IAAI,CAACF,gBAAe,GAAI,IAAI;MAC9B;MAEA,IAAI4E,MAAM,CAACD,IAAI,CAAC,EAAE;QAChB,IAAI,CAACvB,OAAO,CAACH,IAAI,CAAC2B,MAAM,CAACD,IAAI,CAAC,CAAC;MACjC;IACF,CAAC;IAED;IACAE,wBAAwBA,CAAA,EAAG;MACzB,IAAI,CAAC5E,gBAAe,GAAI,CAAC,IAAI,CAACA,gBAAgB;IAChD,CAAC;IAED;IACA6E,gBAAgBA,CAACC,MAAM,EAAE;MACvB,IAAIA,MAAK,KAAM,SAAS,EAAE;QACxB,IAAI,CAAC3B,OAAO,CAACH,IAAI,CAAC,gBAAgB,CAAC;MACrC,OAAO,IAAI8B,MAAK,KAAM,UAAU,EAAE;QAChC,IAAI,CAAC3B,OAAO,CAACH,IAAI,CAAC,iBAAiB,CAAC;MACtC;MACA,IAAI,CAAChD,gBAAe,GAAI,KAAK;IAC/B,CAAC;IAED;IACA+E,kBAAkBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAAC9E,QAAQ,EAAE;QACjB,IAAI,CAACF,gBAAe,GAAI,IAAI;MAC9B;IACF,CAAC;IAED;IACAiF,YAAYA,CAAA,EAAG;MACbvF,gBAAgB,CAACwF,MAAM,CAAC,CAAC;MACzB,IAAI,CAAC9B,OAAO,CAACH,IAAI,CAAC,cAAc,CAAC;IACnC,CAAC;IAED;IACA,MAAMM,gBAAgBA,CAAA,EAAG;MACvB,IAAI;QACF,MAAM4B,QAAO,GAAI,MAAMzF,gBAAgB,CAAC0F,UAAU,CAAC,CAAC;QACpD,IAAID,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAAClF,SAAQ,GAAIgF,QAAQ,CAACpF,IAAI;QAChC;MACF,EAAE,OAAOuF,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,IAAI,CAACnF,SAAQ,GAAIT,gBAAgB,CAAC8F,YAAY,CAAC,CAAC;MAClD;IACF,CAAC;IAED;IACA,MAAMhC,aAAaA,CAAA,EAAG;MACpB,IAAI;QACF,MAAM2B,QAAO,GAAI,MAAMxF,qBAAqB,CAAC8F,YAAY,CAAC,CAAC;QAE3D,IAAIN,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACzE,SAAQ,GAAIuE,QAAQ,CAACpF,IAAI;QAChC,OAAO;UACL,MAAM,IAAI2F,KAAK,CAACP,QAAQ,CAACQ,OAAM,IAAK,gCAAgC,CAAC;QACvE;MACF,EAAE,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD;QACA,IAAI,CAAC1E,SAAQ,GAAI;UACfC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTC,OAAO,EAAE,CAAC;UACVC,MAAM,EAAE;QACV,CAAC;MACH;IACF,CAAC;IAED;IACA,MAAMyC,SAASA,CAAA,EAAG;MAChB,IAAI,CAAC9C,OAAM,GAAI,IAAI;MACnB,IAAI;QACF,MAAMiF,MAAK,GAAI;UACbC,IAAI,EAAE,IAAI,CAACpF,WAAW;UACtBqF,KAAK,EAAE,EAAE;UAAE;UACXC,MAAM,EAAE,IAAI,CAACzF,WAAU,IAAK0F,SAAS;UACrCvE,IAAI,EAAE,IAAI,CAACjB,UAAS,IAAKwF,SAAS;UAClCC,SAAS,EAAE,IAAI,CAAC1F,YAAW,KAAM,QAAO,GAAI,IAAG,GACpC,IAAI,CAACA,YAAW,KAAM,UAAS,GAAI,KAAI,GAAIyF;QACxD,CAAC;QAED,MAAMb,QAAO,GAAI,MAAMxF,qBAAqB,CAACuG,QAAQ,CAACN,MAAM,CAAC;QAE7D,IAAIT,QAAQ,CAACE,OAAO,EAAE;UACpB;UACA,IAAI,CAACjF,KAAI,GAAI+E,QAAQ,CAACpF,IAAI,CAACK,KAAK,CAAC+F,GAAG,CAACC,IAAG,IACtCzG,qBAAqB,CAAC0G,cAAc,CAACD,IAAI,CAC3C,CAAC;UAED,IAAI,CAAC/F,aAAY,GAAI,CAAC,GAAG,IAAI,CAACD,KAAK,CAAC;UACpC,IAAI,CAACkG,cAAc,CAAC,CAAC;QACvB,OAAO;UACL,MAAM,IAAIZ,KAAK,CAACP,QAAQ,CAACQ,OAAM,IAAK,sBAAsB,CAAC;QAC7D;MACF,EAAE,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7C,IAAI,CAACiB,MAAM,EAAEjB,KAAK,GAAGA,KAAK,CAACK,OAAM,IAAK,sBAAsB,CAAC;;QAE7D;QACA,IAAI,CAACvF,KAAI,GAAI,EAAE;QACf,IAAI,CAACC,aAAY,GAAI,EAAE;QACvB,IAAI,CAACiG,cAAc,CAAC,CAAC;MACvB,UAAU;QACR,IAAI,CAAC3F,OAAM,GAAI,KAAK;MACtB;IACF,CAAC;IAED;IACA2F,cAAcA,CAAA,EAAG;MACf,IAAI,CAAC1F,SAAQ,GAAI;QACfC,KAAK,EAAE,IAAI,CAACT,KAAK,CAACuC,MAAM;QACxB7B,MAAM,EAAE,IAAI,CAACV,KAAK,CAACoG,MAAM,CAACC,CAAA,IAAKA,CAAC,CAAC3E,MAAK,KAAM,QAAQ,CAAC,CAACa,MAAM;QAC5D5B,OAAO,EAAE,IAAI,CAACX,KAAK,CAACoG,MAAM,CAACC,CAAA,IAAKA,CAAC,CAAC3E,MAAK,KAAM,SAAS,CAAC,CAACa,MAAM;QAC9D3B,MAAM,EAAE,IAAI,CAACZ,KAAK,CAACoG,MAAM,CAACC,CAAA,IAAKA,CAAC,CAACC,IAAG,KAAM,OAAO,CAAC,CAAC/D;MACrD,CAAC;IACH,CAAC;IAED;IACAgE,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACC,WAAW,CAAC,CAAC;IACpB,CAAC;IAED;IACAA,WAAWA,CAAA,EAAG;MACZ,IAAIC,QAAO,GAAI,CAAC,GAAG,IAAI,CAACzG,KAAK,CAAC;;MAE9B;MACA,IAAI,IAAI,CAACE,WAAW,EAAE;QACpB,MAAMwG,KAAI,GAAI,IAAI,CAACxG,WAAW,CAACyG,WAAW,CAAC,CAAC;QAC5CF,QAAO,GAAIA,QAAQ,CAACL,MAAM,CAACJ,IAAG,IAC5BA,IAAI,CAACY,SAAS,CAACD,WAAW,CAAC,CAAC,CAAC5E,QAAQ,CAAC2E,KAAK,KAC3CV,IAAI,CAAC9E,KAAK,CAACyF,WAAW,CAAC,CAAC,CAAC5E,QAAQ,CAAC2E,KAAK,KACvCV,IAAI,CAAC/E,QAAQ,CAAC0F,WAAW,CAAC,CAAC,CAAC5E,QAAQ,CAAC2E,KAAK,CAC5C,CAAC;MACH;;MAEA;MACA,IAAI,IAAI,CAACvG,YAAY,EAAE;QACrBsG,QAAO,GAAIA,QAAQ,CAACL,MAAM,CAACJ,IAAG,IAAKA,IAAI,CAACtE,MAAK,KAAM,IAAI,CAACvB,YAAY,CAAC;MACvE;;MAEA;MACA,IAAI,IAAI,CAACC,UAAU,EAAE;QACnBqG,QAAO,GAAIA,QAAQ,CAACL,MAAM,CAACJ,IAAG,IAAKA,IAAI,CAACM,IAAG,KAAM,IAAI,CAAClG,UAAU,CAAC;MACnE;MAEA,IAAI,CAACH,aAAY,GAAIwG,QAAQ;MAC7B,IAAI,CAACpG,WAAU,GAAI,CAAC,EAAE;IACxB,CAAC;IAED;IACAwG,UAAUA,CAACpB,IAAI,EAAE;MACf,IAAIA,IAAG,IAAK,KAAKA,IAAG,IAAK,IAAI,CAACrD,UAAU,EAAE;QACxC,IAAI,CAAC/B,WAAU,GAAIoF,IAAI;MACzB;IACF,CAAC;IAED;IACAqB,WAAWA,CAACC,QAAQ,EAAE;MACpB,IAAI,CAACA,QAAQ,EAAE,OAAO,GAAG;MACzB,OAAOA,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAACjB,GAAG,CAACtG,IAAG,IAAKA,IAAI,CAACwH,MAAM,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,CAAChF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3F,CAAC;IAED;IACAiF,mBAAmBA,CAAC1F,MAAM,EAAE;MAC1B,MAAM2F,OAAM,GAAI;QACd,QAAQ,EAAE,YAAY;QACtB,UAAU,EAAE,cAAc;QAC1B,SAAS,EAAE,YAAY;QACvB,WAAW,EAAE;MACf,CAAC;MACD,OAAOA,OAAO,CAAC3F,MAAM,KAAK,cAAc;IAC1C,CAAC;IAED;IACA4F,YAAYA,CAAC5F,MAAM,EAAE;MACnB,OAAOA,MAAM,CAACuF,MAAM,CAAC,CAAC,CAAC,CAACE,WAAW,CAAC,IAAIzF,MAAM,CAACS,KAAK,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;IACAoF,UAAUA,CAACC,UAAU,EAAE;MACrB,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;MAC1B,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC;IAED;;IAGA,MAAMC,gBAAgBA,CAAC/B,IAAI,EAAE;MAC3B,IAAI;QACF,MAAMgC,SAAQ,GAAIhC,IAAI,CAACtE,MAAK,KAAM,QAAO,GAAI,WAAU,GAAI,QAAQ;QACnE,MAAMuG,MAAK,GAAI,4BAA4B,IAAI,CAAClI,SAAS,EAAEoB,UAAS,IAAK,OAAO,EAAE;QAElF,MAAM4D,QAAO,GAAI,MAAMxF,qBAAqB,CAAC2I,gBAAgB,CAAClC,IAAI,CAACvE,EAAE,EAAEuG,SAAS,EAAEC,MAAM,CAAC;QAEzF,IAAIlD,QAAQ,CAACE,OAAO,EAAE;UACpB;UACAe,IAAI,CAACtE,MAAK,GAAIsG,SAAS;UACvB,IAAI,CAAC9B,cAAc,CAAC,CAAC;UAErB,IAAI,CAACC,MAAM,EAAElB,OAAO,GAAG,QAAQe,IAAI,CAACY,SAAS,aAAaoB,SAAQ,KAAM,QAAO,GAAI,WAAU,GAAI,WAAW,GAAG,CAAC;QAClH,OAAO;UACL,MAAM,IAAI1C,KAAK,CAACP,QAAQ,CAACQ,OAAM,IAAK,8BAA8B,CAAC;QACrE;MACF,EAAE,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAACiB,MAAM,EAAEjB,KAAK,GAAGA,KAAK,CAACK,OAAM,IAAK,iDAAiD,CAAC;MAC1F;IACF,CAAC;IAED,MAAM4C,UAAUA,CAACnC,IAAI,EAAE;MACrB,IAAI,CAACoC,OAAO,CAAC,yCAAyCpC,IAAI,CAACY,SAAS,kCAAkC,CAAC,EAAE;QACvG;MACF;MAEA,IAAI;QACF,MAAMqB,MAAK,GAAI,0BAA0B,IAAI,CAAClI,SAAS,EAAEoB,UAAS,IAAK,OAAO,EAAE;QAChF,MAAM4D,QAAO,GAAI,MAAMxF,qBAAqB,CAAC4I,UAAU,CAACnC,IAAI,CAACvE,EAAE,EAAEwG,MAAM,CAAC;QAExE,IAAIlD,QAAQ,CAACE,OAAO,EAAE;UACpB;UACA,MAAMoD,KAAI,GAAI,IAAI,CAACrI,KAAK,CAACsI,SAAS,CAACjC,CAAA,IAAKA,CAAC,CAAC5E,EAAC,KAAMuE,IAAI,CAACvE,EAAE,CAAC;UACzD,IAAI4G,KAAI,GAAI,CAAC,CAAC,EAAE;YACd,IAAI,CAACrI,KAAK,CAACuI,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;YAC3B,IAAI,CAAC7B,WAAW,CAAC,CAAC;YAClB,IAAI,CAACN,cAAc,CAAC,CAAC;UACvB;UAEA,IAAI,CAACC,MAAM,EAAElB,OAAO,GAAG,QAAQe,IAAI,CAACY,SAAS,oBAAoB,CAAC;QACpE,OAAO;UACL,MAAM,IAAItB,KAAK,CAACP,QAAQ,CAACQ,OAAM,IAAK,uBAAuB,CAAC;QAC9D;MACF,EAAE,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACiB,MAAM,EAAEjB,KAAK,GAAGA,KAAK,CAACK,OAAM,IAAK,0CAA0C,CAAC;MACnF;IACF,CAAC;IAID;IACAiD,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACvB,IAAI;QACF,MAAMC,YAAW,GAAIC,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC;QAC5D,IAAIF,YAAY,EAAE;UAChB,MAAMG,KAAI,GAAI,IAAIrJ,KAAK,CAACkJ,YAAY,CAAC;UACrCG,KAAK,CAACC,IAAI,CAAC,CAAC;QACd;MACF,EAAE,OAAO5D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAACiB,MAAM,EAAEjB,KAAK,GAAG,+BAA+B,CAAC;MACvD;IACF,CAAC;IAEDuD,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACzH,WAAU,GAAI;QACjBC,QAAQ,EAAE,EAAE;QACZC,KAAK,EAAE,EAAE;QACTC,UAAU,EAAE,EAAE;QACdC,SAAS,EAAE,EAAE;QACbC,IAAI,EAAE,EAAE;QACRC,QAAQ,EAAE,EAAE;QACZC,YAAY,EAAE;MAChB,CAAC;IACH,CAAC;IAED,MAAMwH,aAAaA,CAAA,EAAG;MACpB,IAAI;QACF,IAAI,CAACjI,cAAa,GAAI,IAAI;;QAE1B;QACA,MAAMkI,UAAS,GAAIzJ,qBAAqB,CAAC0J,gBAAgB,CAAC,IAAI,CAACjI,WAAW,CAAC;QAC3E,IAAI,CAACgI,UAAU,CAACE,OAAO,EAAE;UACvB,IAAI,CAAC/C,MAAM,EAAEjB,KAAK,GAAG8D,UAAU,CAACG,MAAM,CAACjC,IAAI,CAAC,IAAI,CAAC,CAAC;UAClD;QACF;QAEA,MAAMnC,QAAO,GAAI,MAAMxF,qBAAqB,CAAC6J,UAAU,CAAC,IAAI,CAACpI,WAAW,CAAC;QAEzE,IAAI+D,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACkB,MAAM,EAAElB,OAAO,GAAG,2BAA2B,CAAC;;UAEnD;UACA,IAAI;YACF,MAAM4D,KAAI,GAAIrJ,KAAK,CAAC6J,WAAW,CAACV,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC,CAAC;YACxE,IAAIC,KAAK,EAAEA,KAAK,CAACS,IAAI,CAAC,CAAC;UACzB,EAAE,OAAOpE,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC9C;;UAEA;UACA,MAAM,IAAI,CAAC7B,SAAS,CAAC,CAAC;UACtB,MAAM,IAAI,CAACD,aAAa,CAAC,CAAC;QAC5B,OAAO;UACL,MAAM,IAAIkC,KAAK,CAACP,QAAQ,CAACQ,OAAM,IAAK,uBAAuB,CAAC;QAC9D;MACF,EAAE,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACiB,MAAM,EAAEjB,KAAK,GAAGA,KAAK,CAACK,OAAM,IAAK,uBAAuB,CAAC;MAChE,UAAU;QACR,IAAI,CAACzE,cAAa,GAAI,KAAK;MAC7B;IACF,CAAC;IAEDyI,QAAQA,CAACvD,IAAI,EAAE;MACb,IAAI,CAACxE,YAAW,GAAI;QAClBC,EAAE,EAAEuE,IAAI,CAACvE,EAAE;QACXR,QAAQ,EAAE+E,IAAI,CAAC/E,QAAQ;QACvBC,KAAK,EAAE8E,IAAI,CAAC9E,KAAK;QACjBC,UAAU,EAAE6E,IAAI,CAAC7E,UAAU;QAC3BC,SAAS,EAAE4E,IAAI,CAAC5E,SAAS;QACzBC,IAAI,EAAE2E,IAAI,CAACM,IAAI;QACf5E,MAAM,EAAEsE,IAAI,CAACtE,MAAM;QACnBH,YAAY,EAAEyE,IAAI,CAACzE,YAAW,IAAK;MACrC,CAAC;MAED,IAAI;QACF,MAAMmH,YAAW,GAAIC,QAAQ,CAACC,cAAc,CAAC,eAAe,CAAC;QAC7D,IAAIF,YAAY,EAAE;UAChB,MAAMG,KAAI,GAAI,IAAIrJ,KAAK,CAACkJ,YAAY,CAAC;UACrCG,KAAK,CAACC,IAAI,CAAC,CAAC;QACd;MACF,EAAE,OAAO5D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,IAAI,CAACiB,MAAM,EAAEjB,KAAK,GAAG,gCAAgC,CAAC;MACxD;IACF,CAAC;IAED,MAAMsE,cAAcA,CAAA,EAAG;MACrB,IAAI;QACF,IAAI,CAACzI,eAAc,GAAI,IAAI;;QAE3B;QACA,MAAMiI,UAAS,GAAIzJ,qBAAqB,CAAC0J,gBAAgB,CAAC,IAAI,CAACzH,YAAY,EAAE,IAAI,CAAC;QAClF,IAAI,CAACwH,UAAU,CAACE,OAAO,EAAE;UACvB,IAAI,CAAC/C,MAAM,EAAEjB,KAAK,GAAG8D,UAAU,CAACG,MAAM,CAACjC,IAAI,CAAC,IAAI,CAAC,CAAC;UAClD;QACF;QAEA,MAAMnC,QAAO,GAAI,MAAMxF,qBAAqB,CAACkK,UAAU,CAAC,IAAI,CAACjI,YAAY,CAACC,EAAE,EAAE,IAAI,CAACD,YAAY,CAAC;QAEhG,IAAIuD,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACkB,MAAM,EAAElB,OAAO,GAAG,2BAA2B,CAAC;;UAEnD;UACA,IAAI;YACF,MAAM4D,KAAI,GAAIrJ,KAAK,CAAC6J,WAAW,CAACV,QAAQ,CAACC,cAAc,CAAC,eAAe,CAAC,CAAC;YACzE,IAAIC,KAAK,EAAEA,KAAK,CAACS,IAAI,CAAC,CAAC;UACzB,EAAE,OAAOpE,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC9C;;UAEA;UACA,MAAMwE,SAAQ,GAAI,IAAI,CAAC1J,KAAK,CAACsI,SAAS,CAACjC,CAAA,IAAKA,CAAC,CAAC5E,EAAC,KAAM,IAAI,CAACD,YAAY,CAACC,EAAE,CAAC;UAC1E,IAAIiI,SAAQ,GAAI,CAAC,CAAC,EAAE;YAClB,IAAI,CAAC1J,KAAK,CAAC0J,SAAS,IAAI;cAAE,GAAG,IAAI,CAAC1J,KAAK,CAAC0J,SAAS,CAAC;cAAE,GAAG,IAAI,CAAClI;YAAa,CAAC;YAC1E,IAAI,CAACgF,WAAW,CAAC,CAAC;UACpB;;UAEA;UACA,MAAM,IAAI,CAACpD,aAAa,CAAC,CAAC;QAC5B,OAAO;UACL,MAAM,IAAIkC,KAAK,CAACP,QAAQ,CAACQ,OAAM,IAAK,uBAAuB,CAAC;QAC9D;MACF,EAAE,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACiB,MAAM,EAAEjB,KAAK,GAAGA,KAAK,CAACK,OAAM,IAAK,uBAAuB,CAAC;MAChE,UAAU;QACR,IAAI,CAACxE,eAAc,GAAI,KAAK;MAC9B;IACF,CAAC;IAED,MAAM4I,QAAQA,CAAC3D,IAAI,EAAE;MACnB,IAAI;QACF,MAAMjB,QAAO,GAAI,MAAMxF,qBAAqB,CAACqK,OAAO,CAAC5D,IAAI,CAACvE,EAAE,CAAC;QAE7D,IAAIsD,QAAQ,CAACE,OAAO,EAAE;UACpB,IAAI,CAACpE,YAAW,GAAIkE,QAAQ,CAACpF,IAAI;UACjC,IAAI;YACF,MAAM+I,YAAW,GAAIC,QAAQ,CAACC,cAAc,CAAC,eAAe,CAAC;YAC7D,IAAIF,YAAY,EAAE;cAChB,MAAMG,KAAI,GAAI,IAAIrJ,KAAK,CAACkJ,YAAY,CAAC;cACrCG,KAAK,CAACC,IAAI,CAAC,CAAC;YACd;UACF,EAAE,OAAO5D,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;YACtD,IAAI,CAACiB,MAAM,EAAEjB,KAAK,GAAG,mCAAmC,CAAC;UAC3D;QACF,OAAO;UACL,MAAM,IAAII,KAAK,CAACP,QAAQ,CAACQ,OAAM,IAAK,6BAA6B,CAAC;QACpE;MACF,EAAE,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,IAAI,CAACiB,MAAM,EAAEjB,KAAK,GAAGA,KAAK,CAACK,OAAM,IAAK,6BAA6B,CAAC;MACtE;IACF;;IAEA;IACA;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}