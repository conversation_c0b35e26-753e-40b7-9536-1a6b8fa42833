const { executeQuery } = require('./src/config/database');

async function checkClientProfilesSchema() {
  console.log('🔍 Checking client_profiles table schema...');
  
  try {
    // Check if client_profiles table exists and its structure
    const describeQuery = `DESCRIBE client_profiles`;
    const columns = await executeQuery(describeQuery);
    
    console.log('\n📋 client_profiles table columns:');
    columns.forEach(col => {
      console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(nullable)' : '(not null)'}`);
    });
    
    // Also check admin_employee_profiles
    console.log('\n📋 admin_employee_profiles table columns:');
    const adminColumns = await executeQuery(`DESCRIBE admin_employee_profiles`);
    adminColumns.forEach(col => {
      console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(nullable)' : '(not null)'}`);
    });
    
    // Check sample data
    console.log('\n📊 Sample client_profiles data:');
    const sampleData = await executeQuery(`SELECT * FROM client_profiles LIMIT 3`);
    console.log(sampleData);
    
  } catch (error) {
    console.error('❌ Error checking schema:', error.message);
  }
  
  process.exit(0);
}

checkClientProfilesSchema();
