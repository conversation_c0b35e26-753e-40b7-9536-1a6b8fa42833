const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testAdminEndpoints() {
  console.log('🔧 Testing Admin Endpoints...');
  
  try {
    // Login as admin
    const loginResponse = await axios.post(`${BASE_URL}/admin/auth/login`, {
      username: 'admin12345',
      password: '12345QWERTqwert'
    });
    
    if (!loginResponse.data.success) {
      console.log('❌ Admin login failed');
      return;
    }
    
    const adminToken = loginResponse.data.data.token;
    console.log('✅ Admin login successful');
    
    // Test different request queries
    console.log('\n📋 Testing request queries...');
    
    // 1. Get all requests
    const allRequestsResponse = await axios.get(`${BASE_URL}/admin/documents/requests`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    if (allRequestsResponse.data.success) {
      const allRequests = allRequestsResponse.data.data.requests || [];
      console.log(`✅ All requests: Found ${allRequests.length} requests`);
      allRequests.forEach(req => {
        console.log(`   - ID: ${req.id}, Status: ${req.status_name}, Number: ${req.request_number}`);
      });
    }
    
    // 2. Get pending requests specifically
    const pendingResponse = await axios.get(`${BASE_URL}/admin/documents/requests?status=pending`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    if (pendingResponse.data.success) {
      const pendingRequests = pendingResponse.data.data.requests || [];
      console.log(`✅ Pending requests: Found ${pendingRequests.length} requests`);
      pendingRequests.forEach(req => {
        console.log(`   - ID: ${req.id}, Status: ${req.status_name}, Number: ${req.request_number}`);
      });
    }
    
    // 3. Test approval workflow with a pending request
    if (allRequestsResponse.data.success) {
      const allRequests = allRequestsResponse.data.data.requests || [];
      const pendingRequest = allRequests.find(r => r.status_name === 'pending');
      
      if (pendingRequest) {
        console.log(`\n✅ Testing approval workflow with request ${pendingRequest.request_number} (ID: ${pendingRequest.id})`);
        
        // Move to under_review first
        const reviewResponse = await axios.put(
          `${BASE_URL}/admin/documents/requests/${pendingRequest.id}/status`,
          { status_id: 2, reason: 'Moving to review for testing' },
          { headers: { Authorization: `Bearer ${adminToken}` } }
        );
        
        if (reviewResponse.data.success) {
          console.log('✅ Moved to under_review');
          
          // Now approve
          await new Promise(resolve => setTimeout(resolve, 1000));
          const approveResponse = await axios.post(
            `${BASE_URL}/admin/documents/requests/${pendingRequest.id}/approve`,
            { reason: 'Approved for testing' },
            { headers: { Authorization: `Bearer ${adminToken}` } }
          );
          
          if (approveResponse.data.success) {
            console.log('✅ Request approved successfully');
            
            // Verify status
            const verifyResponse = await axios.get(
              `${BASE_URL}/admin/documents/requests/${pendingRequest.id}`,
              { headers: { Authorization: `Bearer ${adminToken}` } }
            );
            
            if (verifyResponse.data.success) {
              console.log(`✅ Status verified: ${verifyResponse.data.data.status_name}`);
            }
          } else {
            console.log('❌ Approval failed');
          }
        } else {
          console.log('❌ Failed to move to review');
        }
      } else {
        console.log('⚠️ No pending requests found for approval testing');
      }
    }
    
    // 4. Test rejection workflow
    if (allRequestsResponse.data.success) {
      const allRequests = allRequestsResponse.data.data.requests || [];
      const anotherPendingRequest = allRequests.find(r => r.status_name === 'pending' && r.id !== (pendingRequest?.id || 0));
      
      if (anotherPendingRequest) {
        console.log(`\n❌ Testing rejection workflow with request ${anotherPendingRequest.request_number} (ID: ${anotherPendingRequest.id})`);
        
        const rejectResponse = await axios.post(
          `${BASE_URL}/admin/documents/requests/${anotherPendingRequest.id}/reject`,
          { reason: 'Rejected for testing - missing documents' },
          { headers: { Authorization: `Bearer ${adminToken}` } }
        );
        
        if (rejectResponse.data.success) {
          console.log('✅ Request rejected successfully');
          
          // Verify status
          const verifyResponse = await axios.get(
            `${BASE_URL}/admin/documents/requests/${anotherPendingRequest.id}`,
            { headers: { Authorization: `Bearer ${adminToken}` } }
          );
          
          if (verifyResponse.data.success) {
            console.log(`✅ Status verified: ${verifyResponse.data.data.status_name}`);
          }
        } else {
          console.log('❌ Rejection failed');
        }
      } else {
        console.log('⚠️ No additional pending requests found for rejection testing');
      }
    }
    
  } catch (error) {
    console.error('❌ Error testing admin endpoints:', error.response?.data?.message || error.message);
  }
}

testAdminEndpoints().then(() => {
  console.log('\n🏁 Admin endpoint testing completed!');
  process.exit(0);
}).catch(console.error);
