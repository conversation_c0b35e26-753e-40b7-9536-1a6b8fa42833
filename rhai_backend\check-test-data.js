const { executeQuery } = require('./src/config/database');

async function checkTestData() {
  console.log('🔍 Checking existing test data...');
  
  try {
    // Check clients
    console.log('\n👤 Checking client accounts...');
    const clientQuery = `
      SELECT id, username, status
      FROM client_accounts
      LIMIT 5
    `;
    const clients = await executeQuery(clientQuery);
    console.log(`Found ${clients.length} client accounts:`);
    clients.forEach(client => {
      console.log(`  - ID: ${client.id}, Username: ${client.username}, Status: ${client.status}`);
    });
    
    // Check document requests
    console.log('\n📋 Checking document requests...');
    const requestQuery = `
      SELECT dr.id, dr.request_number, dr.status_id, rs.status_name,
             ca.username as client_username
      FROM document_requests dr
      JO<PERSON> request_status rs ON dr.status_id = rs.id
      JOIN client_accounts ca ON dr.client_id = ca.id
      ORDER BY dr.created_at DESC
      LIMIT 10
    `;
    const requests = await executeQuery(requestQuery);
    console.log(`Found ${requests.length} document requests:`);
    requests.forEach(req => {
      console.log(`  - ID: ${req.id}, Number: ${req.request_number}, Status: ${req.status_name}, Client: ${req.client_username}`);
    });
    
    // Check document statuses
    console.log('\n📊 Checking document statuses...');
    const statusQuery = `SELECT id, status_name, description FROM request_status ORDER BY id`;
    const statuses = await executeQuery(statusQuery);
    console.log(`Found ${statuses.length} document statuses:`);
    statuses.forEach(status => {
      console.log(`  - ID: ${status.id}, Name: ${status.status_name}, Description: ${status.description}`);
    });
    
    // Check notifications
    console.log('\n🔔 Checking notifications...');
    const notifQuery = `
      SELECT id, user_id, user_type, title, message, is_read, created_at
      FROM notifications 
      ORDER BY created_at DESC 
      LIMIT 5
    `;
    const notifications = await executeQuery(notifQuery);
    console.log(`Found ${notifications.length} recent notifications:`);
    notifications.forEach(notif => {
      console.log(`  - ID: ${notif.id}, User: ${notif.user_type} ${notif.user_id}, Title: ${notif.title}, Read: ${notif.is_read}`);
    });
    
  } catch (error) {
    console.error('❌ Error checking test data:', error);
  }
  
  process.exit(0);
}

checkTestData();
