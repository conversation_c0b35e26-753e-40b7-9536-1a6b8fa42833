const DocumentRequestService = require('./src/services/documentRequestService');

async function testRequest() {
  try {
    console.log('Testing document request submission...');
    
    const requestData = {
      document_type_id: 2,
      purpose_category_id: 2,
      purpose_details: 'Test Automotive Shop, Relocation',
      payment_method_id: 6,
      delivery_method: 'pickup',
      priority: 'normal',
      emergency_contact_name: 'Test Contact',
      emergency_contact_relationship: 'Friend',
      emergency_contact_phone: '09955958343',
      emergency_contact_address: 'Test Address',
      has_pending_cases: false,
      pending_cases_details: null,
      is_registered_voter: true,
      additional_notes: null,
      total_fee: 50
    };
    
    const clientId = 12;
    
    console.log('Submitting request with data:', requestData);
    const result = await DocumentRequestService.submitRequest(requestData, clientId);
    console.log('Success:', result);
    
  } catch (error) {
    console.error('Error:', error);
  }
  
  process.exit(0);
}

testRequest();
