[{"D:\\rhai_front_and_back\\BOSFDR\\src\\main.js": "1", "D:\\rhai_front_and_back\\BOSFDR\\src\\App.vue": "2", "D:\\rhai_front_and_back\\BOSFDR\\src\\services\\clientAuthService.js": "3", "D:\\rhai_front_and_back\\BOSFDR\\src\\services\\adminAuthService.js": "4", "D:\\rhai_front_and_back\\BOSFDR\\src\\router\\index.js": "5", "D:\\rhai_front_and_back\\BOSFDR\\src\\services\\api.js": "6", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminProfile.vue": "7", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\NotFound.vue": "8", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminSettings.vue": "9", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminActivityLogs.vue": "10", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminTest.vue": "11", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminReports.vue": "12", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminUsers.vue": "13", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\RequestDetails.vue": "14", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminRequests.vue": "15", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\MyRequests.vue": "16", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\BarangayClearanceRequest.vue": "17", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\CedulaRequest.vue": "18", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminDashboard.vue": "19", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\NewDocumentRequest.vue": "20", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientDashboard.vue": "21", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminRegistration.vue": "22", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientLogin.vue": "23", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminLogin.vue": "24", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientRegistration.vue": "25", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\js\\clientDashboard.js": "26", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\js\\clientLogin.js": "27", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\js\\clientRegistration.js": "28", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminHeader.vue": "29", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminSidebar.vue": "30", "D:\\rhai_front_and_back\\BOSFDR\\src\\services\\documentRequestService.js": "31", "D:\\rhai_front_and_back\\BOSFDR\\src\\services\\notificationService.js": "32", "D:\\rhai_front_and_back\\BOSFDR\\src\\services\\adminDocumentService.js": "33", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientHeader.vue": "34", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientSidebar.vue": "35", "D:\\rhai_front_and_back\\BOSFDR\\src\\utils\\validation.js": "36", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\js\\clientHeader.js": "37", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\js\\clientSidebar.js": "38", "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminNotifications.vue": "39"}, {"size": 1635, "mtime": 1751934720409, "results": "40", "hashOfConfig": "41"}, {"size": 4003, "mtime": 1750328333266, "results": "42", "hashOfConfig": "41"}, {"size": 4682, "mtime": 1751936902857, "results": "43", "hashOfConfig": "41"}, {"size": 9352, "mtime": 1751929099190, "results": "44", "hashOfConfig": "41"}, {"size": 8215, "mtime": 1751929304108, "results": "45", "hashOfConfig": "41"}, {"size": 2079, "mtime": 1750614925317, "results": "46", "hashOfConfig": "41"}, {"size": 12243, "mtime": 1750613889378, "results": "47", "hashOfConfig": "41"}, {"size": 1167, "mtime": 1750603944931, "results": "48", "hashOfConfig": "41"}, {"size": 34488, "mtime": 1750613914045, "results": "49", "hashOfConfig": "41"}, {"size": 17919, "mtime": 1750613762308, "results": "50", "hashOfConfig": "41"}, {"size": 4476, "mtime": 1750593733241, "results": "51", "hashOfConfig": "41"}, {"size": 20608, "mtime": 1751927898758, "results": "52", "hashOfConfig": "41"}, {"size": 25753, "mtime": 1750613714476, "results": "53", "hashOfConfig": "41"}, {"size": 24434, "mtime": 1751921074811, "results": "54", "hashOfConfig": "41"}, {"size": 86023, "mtime": 1751943745830, "results": "55", "hashOfConfig": "41"}, {"size": 25333, "mtime": 1751936860007, "results": "56", "hashOfConfig": "41"}, {"size": 30188, "mtime": 1751920548448, "results": "57", "hashOfConfig": "41"}, {"size": 36885, "mtime": 1751938202813, "results": "58", "hashOfConfig": "41"}, {"size": 39819, "mtime": 1751933080597, "results": "59", "hashOfConfig": "41"}, {"size": 11516, "mtime": 1751920347978, "results": "60", "hashOfConfig": "41"}, {"size": 9693, "mtime": 1750328834397, "results": "61", "hashOfConfig": "41"}, {"size": 27941, "mtime": 1750607046034, "results": "62", "hashOfConfig": "41"}, {"size": 8448, "mtime": 1750337621762, "results": "63", "hashOfConfig": "41"}, {"size": 13092, "mtime": 1750607004846, "results": "64", "hashOfConfig": "41"}, {"size": 21084, "mtime": 1750321846093, "results": "65", "hashOfConfig": "41"}, {"size": 8709, "mtime": 1751920745237, "results": "66", "hashOfConfig": "41"}, {"size": 4535, "mtime": 1750321681679, "results": "67", "hashOfConfig": "41"}, {"size": 9240, "mtime": 1750321654826, "results": "68", "hashOfConfig": "41"}, {"size": 6957, "mtime": 1751939746116, "results": "69", "hashOfConfig": "41"}, {"size": 12058, "mtime": 1751929414818, "results": "70", "hashOfConfig": "41"}, {"size": 6285, "mtime": 1751938225288, "results": "71", "hashOfConfig": "41"}, {"size": 9177, "mtime": 1751940148292, "results": "72", "hashOfConfig": "41"}, {"size": 12080, "mtime": 1751940085319, "results": "73", "hashOfConfig": "41"}, {"size": 3877, "mtime": 1750327026214, "results": "74", "hashOfConfig": "41"}, {"size": 5504, "mtime": 1750614515550, "results": "75", "hashOfConfig": "41"}, {"size": 6148, "mtime": 1750593547792, "results": "76", "hashOfConfig": "41"}, {"size": 3695, "mtime": 1750327075856, "results": "77", "hashOfConfig": "41"}, {"size": 2227, "mtime": 1751920766329, "results": "78", "hashOfConfig": "41"}, {"size": 11316, "mtime": 1751930499603, "results": "79", "hashOfConfig": "41"}, {"filePath": "80", "messages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "82"}, "jpzhxp", {"filePath": "83", "messages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "85"}, {"filePath": "86", "messages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "82"}, {"filePath": "90", "messages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "82"}, {"filePath": "92", "messages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "82"}, {"filePath": "94", "messages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "85"}, {"filePath": "96", "messages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "85"}, {"filePath": "98", "messages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "85"}, {"filePath": "100", "messages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "85"}, {"filePath": "102", "messages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "85"}, {"filePath": "104", "messages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "85"}, {"filePath": "106", "messages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "85"}, {"filePath": "108", "messages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "85"}, {"filePath": "110", "messages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "85"}, {"filePath": "116", "messages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "85"}, {"filePath": "120", "messages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "85"}, {"filePath": "122", "messages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "85"}, {"filePath": "124", "messages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "85"}, {"filePath": "126", "messages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "85"}, {"filePath": "128", "messages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "85"}, {"filePath": "130", "messages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "85"}, {"filePath": "132", "messages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "82"}, {"filePath": "134", "messages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "82"}, {"filePath": "136", "messages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "82"}, {"filePath": "138", "messages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "85"}, {"filePath": "142", "messages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "85"}, {"filePath": "150", "messages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "85"}, {"filePath": "152", "messages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "82"}, {"filePath": "154", "messages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "82"}, {"filePath": "156", "messages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "82"}, {"filePath": "158", "messages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "85"}, "D:\\rhai_front_and_back\\BOSFDR\\src\\main.js", [], [], "D:\\rhai_front_and_back\\BOSFDR\\src\\App.vue", [], [], "D:\\rhai_front_and_back\\BOSFDR\\src\\services\\clientAuthService.js", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\services\\adminAuthService.js", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\router\\index.js", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\services\\api.js", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminProfile.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\NotFound.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminSettings.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminActivityLogs.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminTest.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminReports.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminUsers.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\RequestDetails.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminRequests.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\MyRequests.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\BarangayClearanceRequest.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\CedulaRequest.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminDashboard.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\NewDocumentRequest.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientDashboard.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminRegistration.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientLogin.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminLogin.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientRegistration.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\js\\clientDashboard.js", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\js\\clientLogin.js", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\js\\clientRegistration.js", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminHeader.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminSidebar.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\services\\documentRequestService.js", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\services\\notificationService.js", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\services\\adminDocumentService.js", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientHeader.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientSidebar.vue", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\utils\\validation.js", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\js\\clientHeader.js", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\js\\clientSidebar.js", [], "D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminNotifications.vue", []]