<template>
  <div class="admin-notifications">
    <!-- Notification Bell Icon -->
    <div class="notification-bell" @click="toggleNotificationPanel">
      <i class="fas fa-bell"></i>
      <span v-if="unreadCount > 0" class="notification-badge">{{ unreadCount > 99 ? '99+' : unreadCount }}</span>
    </div>

    <!-- Notification Panel -->
    <div v-if="showPanel" class="notification-panel" @click.stop>
      <div class="notification-header">
        <h5>Notifications</h5>
        <div class="notification-actions">
          <button 
            v-if="unreadCount > 0" 
            @click="markAllAsRead" 
            class="btn btn-sm btn-outline-primary"
            :disabled="markingAllRead"
          >
            <i class="fas fa-check-double"></i>
            {{ markingAllRead ? 'Marking...' : 'Mark All Read' }}
          </button>
          <button @click="toggleNotificationPanel" class="btn btn-sm btn-outline-secondary">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>

      <div class="notification-body">
        <div v-if="loading" class="text-center p-3">
          <div class="spinner-border spinner-border-sm" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-2 mb-0">Loading notifications...</p>
        </div>

        <div v-else-if="notifications.length === 0" class="text-center p-4 text-muted">
          <i class="fas fa-bell-slash fa-2x mb-2"></i>
          <p class="mb-0">No notifications</p>
        </div>

        <div v-else class="notification-list">
          <div 
            v-for="notification in notifications" 
            :key="notification.id"
            class="notification-item"
            :class="{ 'unread': !notification.is_read, 'priority-high': notification.priority === 'high' || notification.priority === 'urgent' }"
            @click="handleNotificationClick(notification)"
          >
            <div class="notification-icon">
              <i :class="getNotificationIcon(notification.type)"></i>
            </div>
            <div class="notification-content">
              <div class="notification-title">{{ notification.title }}</div>
              <div class="notification-message">{{ notification.message }}</div>
              <div class="notification-time">{{ formatTime(notification.created_at) }}</div>
            </div>
            <div class="notification-priority" v-if="notification.priority === 'high' || notification.priority === 'urgent'">
              <i class="fas fa-exclamation-triangle text-warning"></i>
            </div>
          </div>
        </div>

        <div v-if="hasMore" class="notification-footer">
          <button 
            @click="loadMore" 
            class="btn btn-sm btn-outline-primary w-100"
            :disabled="loadingMore"
          >
            <i class="fas fa-chevron-down"></i>
            {{ loadingMore ? 'Loading...' : 'Load More' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Overlay -->
    <div v-if="showPanel" class="notification-overlay" @click="toggleNotificationPanel"></div>
  </div>
</template>

<script>
import notificationService from '../../services/notificationService';

export default {
  name: 'AdminNotifications',
  data() {
    return {
      showPanel: false,
      notifications: [],
      unreadCount: 0,
      loading: false,
      loadingMore: false,
      markingAllRead: false,
      currentPage: 1,
      hasMore: true,
      limit: 10
    };
  },
  mounted() {
    this.initializeNotifications();
  },
  beforeUnmount() {
    this.cleanup();
  },
  methods: {
    async initializeNotifications() {
      try {
        // Request notification permission
        await notificationService.requestNotificationPermission();

        // Initialize notification service
        await notificationService.init('admin');

        // Set up event listeners
        notificationService.on('notification', this.handleNewNotification);
        notificationService.on('connected', this.onConnected);
        notificationService.on('error', this.onError);

        // Load initial data
        await this.loadUnreadCount();

      } catch (error) {
        console.error('Failed to initialize notifications:', error);
      }
    },

    cleanup() {
      notificationService.off('notification', this.handleNewNotification);
      notificationService.off('connected', this.onConnected);
      notificationService.off('error', this.onError);

      // Cleanup (simplified)
      notificationService.cleanup();
    },

    async toggleNotificationPanel() {
      this.showPanel = !this.showPanel;
      
      if (this.showPanel && this.notifications.length === 0) {
        await this.loadNotifications();
      }
    },

    async loadNotifications(page = 1) {
      try {
        if (page === 1) {
          this.loading = true;
          this.notifications = [];
          this.currentPage = 1;
        } else {
          this.loadingMore = true;
        }

        const response = await notificationService.getNotifications(page, this.limit);

        // Handle the correct response structure from backend
        let notifications = [];
        let pagination = {};

        if (response.data && response.data.data) {
          // Backend returns: { success: true, data: { notifications: [...], pagination: {...} } }
          if (response.data.data.notifications && Array.isArray(response.data.data.notifications)) {
            notifications = response.data.data.notifications;
            pagination = response.data.data.pagination || {};
          }
        }

        if (page === 1) {
          this.notifications = notifications;
        } else {
          this.notifications.push(...notifications);
        }

        this.hasMore = pagination.page < pagination.pages;
        this.currentPage = page;
        
      } catch (error) {
        console.error('Failed to load notifications:', error);
        this.$emit('error', 'Failed to load notifications');
      } finally {
        this.loading = false;
        this.loadingMore = false;
      }
    },

    async loadMore() {
      if (this.hasMore && !this.loadingMore) {
        await this.loadNotifications(this.currentPage + 1);
      }
    },

    async loadUnreadCount() {
      try {
        this.unreadCount = await notificationService.getUnreadCount();
      } catch (error) {
        console.error('Failed to load unread count:', error);
      }
    },

    async markAllAsRead() {
      try {
        this.markingAllRead = true;
        await notificationService.markAllAsRead();
        
        // Update local state
        this.notifications.forEach(notification => {
          notification.is_read = true;
        });
        this.unreadCount = 0;
        
        this.$emit('notifications-read');
        
      } catch (error) {
        console.error('Failed to mark all as read:', error);
        this.$emit('error', 'Failed to mark notifications as read');
      } finally {
        this.markingAllRead = false;
      }
    },

    async handleNotificationClick(notification) {
      if (!notification.is_read) {
        try {
          await notificationService.markAsRead(notification.id);
          notification.is_read = true;
          this.unreadCount = Math.max(0, this.unreadCount - 1);
          this.$emit('notification-read', notification);
        } catch (error) {
          console.error('Failed to mark notification as read:', error);
        }
      }
      
      // Emit click event for parent components to handle
      this.$emit('notification-click', notification);
    },

    handleNewNotification(notification) {
      // Add to beginning of list if panel is open
      if (this.showPanel) {
        this.notifications.unshift(notification);
      }
      
      // Update unread count
      if (!notification.is_read) {
        this.unreadCount++;
      }
      
      // Emit event for parent components
      this.$emit('new-notification', notification);
    },

    onConnected() {
      console.log('Connected to notification stream');
      this.$emit('connected');
    },

    onError(error) {
      console.error('Notification stream error:', error);
      this.$emit('error', 'Connection to notification stream failed');
    },

    getNotificationIcon(type) {
      const icons = {
        'status_change': 'fas fa-sync-alt text-info',
        'new_request': 'fas fa-file-alt text-success',
        'request_update': 'fas fa-edit text-warning',
        'system_alert': 'fas fa-exclamation-triangle text-danger',
        'test': 'fas fa-vial text-secondary',
        'connection': 'fas fa-plug text-success'
      };
      return icons[type] || 'fas fa-bell text-primary';
    },

    formatTime(timestamp) {
      const date = new Date(timestamp);
      const now = new Date();
      const diffInMinutes = Math.floor((now - date) / (1000 * 60));
      
      if (diffInMinutes < 1) return 'Just now';
      if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
      if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
      return date.toLocaleDateString();
    }
  }
};
</script>

<style scoped>
.admin-notifications {
  position: relative;
}

.notification-bell {
  position: relative;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.notification-bell:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.notification-bell i {
  font-size: 1.2rem;
  color: #6c757d;
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: #dc3545;
  color: white;
  border-radius: 50%;
  padding: 2px 6px;
  font-size: 0.75rem;
  font-weight: bold;
  min-width: 18px;
  text-align: center;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.notification-panel {
  position: absolute;
  top: 100%;
  right: 0;
  width: 400px;
  max-height: 500px;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1050;
  overflow: hidden;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  background: #f8f9fa;
}

.notification-header h5 {
  margin: 0;
  font-weight: 600;
}

.notification-actions {
  display: flex;
  gap: 0.5rem;
}

.notification-body {
  max-height: 400px;
  overflow-y: auto;
}

.notification-list {
  padding: 0;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 1rem;
  border-bottom: 1px solid #f1f3f4;
  cursor: pointer;
  transition: background-color 0.2s;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item.unread {
  background-color: #e3f2fd;
  border-left: 4px solid #2196f3;
}

.notification-item.priority-high {
  border-left: 4px solid #ff9800;
}

.notification-icon {
  margin-right: 0.75rem;
  margin-top: 0.25rem;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: #212529;
}

.notification-message {
  font-size: 0.875rem;
  color: #6c757d;
  margin-bottom: 0.25rem;
  line-height: 1.4;
}

.notification-time {
  font-size: 0.75rem;
  color: #adb5bd;
}

.notification-priority {
  margin-left: 0.5rem;
  margin-top: 0.25rem;
}

.notification-footer {
  padding: 0.75rem;
  border-top: 1px solid #dee2e6;
  background: #f8f9fa;
}

.notification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1040;
}

@media (max-width: 768px) {
  .notification-panel {
    width: 320px;
    right: -50px;
  }
}
</style>
