const axios = require('axios');

async function testAPIDirect() {
  console.log('🔍 Testing API endpoints directly...');
  
  try {
    // First login to get a fresh token
    console.log('\n1. Getting fresh admin token...');
    const loginResponse = await axios.post('http://localhost:3000/api/admin/auth/login', {
      username: 'admin12345',
      password: '12345QWERTqwert'
    });
    
    if (!loginResponse.data.success) {
      console.log('❌ Login failed');
      return;
    }
    
    const token = loginResponse.data.data.token;
    console.log('✅ Got token:', token.substring(0, 50) + '...');
    
    // Test the users endpoint
    console.log('\n2. Testing /api/users endpoint...');
    try {
      const usersResponse = await axios.get('http://localhost:3000/api/users', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Users endpoint response:', usersResponse.status);
      console.log('Data:', JSON.stringify(usersResponse.data, null, 2));
      
    } catch (error) {
      console.log('❌ Users endpoint error:', error.response?.status);
      console.log('Error data:', error.response?.data);
      console.log('Error message:', error.message);
    }
    
    // Test the stats endpoint
    console.log('\n3. Testing /api/users/stats endpoint...');
    try {
      const statsResponse = await axios.get('http://localhost:3000/api/users/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Stats endpoint response:', statsResponse.status);
      console.log('Data:', JSON.stringify(statsResponse.data, null, 2));
      
    } catch (error) {
      console.log('❌ Stats endpoint error:', error.response?.status);
      console.log('Error data:', error.response?.data);
      console.log('Error message:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
  
  process.exit(0);
}

testAPIDirect();
