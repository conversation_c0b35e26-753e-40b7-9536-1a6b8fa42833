const express = require('express');
const rateLimit = require('express-rate-limit');
const adminDocumentController = require('../controllers/adminDocumentController');
const auth = require('../middleware/auth');

const router = express.Router();

// Rate limiting for admin operations (TEMPORARILY DISABLED FOR TESTING)
const adminRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // Increased limit for testing
  message: {
    success: false,
    message: 'Too many requests from this IP, please try again later.'
  }
});

// Apply rate limiting to all admin routes (TEMPORARILY DISABLED)
// router.use(adminRateLimit);

// Apply authentication middleware to all routes
router.use(auth.protect);

// Apply admin/employee authorization to all routes
router.use(auth.authorize('admin', 'employee'));

/**
 * @route   GET /api/admin/documents/test
 * @desc    Test endpoint to verify admin document routes are working
 * @access  Private (Admin/Employee only)
 */
router.get('/test', (req, res) => {
  // Test the status transition logic
  const adminDocumentService = require('../services/adminDocumentService');

  let transitionTest = 'UNKNOWN';
  try {
    adminDocumentService.validateStatusTransition(1, 4);
    transitionTest = 'ALLOWED (1→4)';
  } catch (error) {
    transitionTest = `BLOCKED: ${error.message}`;
  }

  res.json({
    success: true,
    message: 'Admin document routes are working!',
    user: {
      id: req.user.id,
      username: req.user.username,
      role: req.user.role
    },
    timestamp: new Date().toISOString(),
    statusTransitionTest: transitionTest,
    serverReloadTime: new Date().toISOString(),
    endpoints: [
      'GET /requests - Get all document requests with filtering',
      'GET /requests/:id - Get specific request details',
      'PUT /requests/:id/status - Update request status',
      'GET /requests/:id/history - Get request status history',
      'GET /dashboard/stats - Get dashboard statistics',
      'GET /dashboard/recent - Get recent activity',
      'POST /requests/:id/approve - Approve request',
      'POST /requests/:id/reject - Reject request',
      'POST /requests/:id/process - Mark as processing',
      'POST /requests/:id/complete - Mark as completed',
      'GET /requests/export - Export requests data'
    ]
  });
});

/**
 * @route   GET /api/admin/documents/dashboard/stats
 * @desc    Get dashboard statistics
 * @access  Private (Admin/Employee only)
 */
router.get('/dashboard/stats', adminDocumentController.getDashboardStats);

/**
 * @route   GET /api/admin/documents/dashboard/recent
 * @desc    Get recent activity for dashboard
 * @access  Private (Admin/Employee only)
 */
router.get('/dashboard/recent', adminDocumentController.getRecentActivity);

/**
 * @route   GET /api/admin/documents/requests
 * @desc    Get all document requests with filtering and pagination
 * @access  Private (Admin/Employee only)
 * @query   { page?, limit?, status?, document_type?, priority?, search?, date_from?, date_to? }
 */
router.get('/requests', adminDocumentController.getAllRequests);

/**
 * @route   GET /api/admin/documents/requests/export
 * @desc    Export requests data as CSV
 * @access  Private (Admin/Employee only)
 * @query   { status?, document_type?, date_from?, date_to? }
 */
router.get('/requests/export',
  auth.protect,
  auth.authorize('admin', 'employee'),
  adminDocumentController.exportRequests
);

/**
 * @route   GET /api/admin/documents/requests/:id
 * @desc    Get specific document request details
 * @access  Private (Admin/Employee only)
 */
router.get('/requests/:id', adminDocumentController.getRequestDetails);

/**
 * @route   GET /api/admin/documents/requests/:id/history
 * @desc    Get request status history
 * @access  Private (Admin/Employee only)
 */
router.get('/requests/:id/history', adminDocumentController.getRequestHistory);

/**
 * @route   PUT /api/admin/documents/requests/:id/status
 * @desc    Update request status with reason
 * @access  Private (Admin/Employee only)
 * @body    { status_id, reason? }
 */
router.put('/requests/:id/status',
  adminDocumentController.constructor.updateStatusValidation(),
  adminDocumentController.updateRequestStatus
);

/**
 * @route   POST /api/admin/documents/requests/:id/approve
 * @desc    Approve document request
 * @access  Private (Admin/Employee only)
 * @body    { reason? }
 */
router.post('/requests/:id/approve', adminDocumentController.approveRequest);

/**
 * @route   POST /api/admin/documents/requests/:id/reject
 * @desc    Reject document request
 * @access  Private (Admin/Employee only)
 * @body    { reason }
 */
router.post('/requests/:id/reject',
  adminDocumentController.constructor.rejectRequestValidation(),
  adminDocumentController.rejectRequest
);

/**
 * @route   POST /api/admin/documents/requests/:id/process
 * @desc    Mark request as processing
 * @access  Private (Admin/Employee only)
 * @body    { reason? }
 */
router.post('/requests/:id/process', adminDocumentController.processRequest);

/**
 * @route   POST /api/admin/documents/requests/:id/complete
 * @desc    Mark request as completed
 * @access  Private (Admin/Employee only)
 * @body    { reason? }
 */
router.post('/requests/:id/complete', adminDocumentController.completeRequest);

/**
 * @route   POST /api/admin/documents/requests/:id/ready-pickup
 * @desc    Mark request as ready for pickup
 * @access  Private (Admin/Employee only)
 * @body    { reason? }
 */
router.post('/requests/:id/ready-pickup', adminDocumentController.markReadyForPickup);

/**
 * @route   POST /api/admin/documents/requests/:id/require-info
 * @desc    Request additional information from client
 * @access  Private (Admin/Employee only)
 * @body    { reason }
 */
router.post('/requests/:id/require-info',
  adminDocumentController.constructor.requireInfoValidation(),
  adminDocumentController.requireAdditionalInfo
);

/**
 * @route   GET /api/admin/documents/status-options
 * @desc    Get all available status options
 * @access  Private (Admin/Employee only)
 */
router.get('/status-options', adminDocumentController.getStatusOptions);

/**
 * @route   POST /api/admin/documents/requests/bulk-update
 * @desc    Bulk update multiple requests
 * @access  Private (Admin only)
 * @body    { request_ids: [], status_id, reason? }
 */
router.post('/requests/bulk-update',
  auth.authorize('admin'), // Only admins can do bulk operations
  adminDocumentController.constructor.bulkUpdateValidation(),
  adminDocumentController.bulkUpdateRequests
);

/**
 * @route   GET /api/admin/documents/analytics
 * @desc    Get analytics data for reporting
 * @access  Private (Admin/Employee)
 * @query   { period: 'day'|'week'|'month' }
 */
router.get('/analytics', adminDocumentController.getAnalyticsData);

/**
 * @route   GET /api/admin/documents/reports/:reportType
 * @desc    Generate comprehensive report
 * @access  Private (Admin/Employee)
 * @params  { reportType: 'daily'|'weekly'|'monthly'|'custom' }
 * @query   { date_from?, date_to?, format: 'csv'|'json' }
 */
router.get('/reports/:reportType', adminDocumentController.generateReport);

module.exports = router;
