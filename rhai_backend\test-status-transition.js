const adminDocumentService = require('./src/services/adminDocumentService');

async function testStatusTransition() {
  console.log('🧪 Testing status transition logic...');
  
  try {
    // Test the validateStatusTransition method directly
    console.log('Testing transition from pending (1) to approved (4)...');
    
    // This should work with our fix
    adminDocumentService.validateStatusTransition(1, 4);
    console.log('✅ Transition 1 → 4 (pending → approved) is ALLOWED');
    
    // Test other transitions
    adminDocumentService.validateStatusTransition(1, 2);
    console.log('✅ Transition 1 → 2 (pending → under_review) is ALLOWED');
    
    adminDocumentService.validateStatusTransition(1, 9);
    console.log('✅ Transition 1 → 9 (pending → rejected) is ALLOWED');
    
    // Test invalid transition
    try {
      adminDocumentService.validateStatusTransition(1, 7);
      console.log('❌ Transition 1 → 7 should be INVALID but was allowed');
    } catch (error) {
      console.log('✅ Transition 1 → 7 (pending → completed) is correctly BLOCKED');
    }
    
    console.log('\n🎯 Status transition logic is working correctly!');
    
  } catch (error) {
    console.error('❌ Status transition test failed:', error.message);
  }
}

testStatusTransition();
