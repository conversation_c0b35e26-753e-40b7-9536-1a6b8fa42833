{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport axios from 'axios';\nclass NotificationService {\n  constructor() {\n    this.eventSource = null;\n    this.listeners = new Map();\n    this.isConnected = false;\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n    this.reconnectDelay = 1000; // Start with 1 second\n    this.maxReconnectDelay = 30000; // Max 30 seconds\n    this.baseURL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api';\n    this.connectionRefs = 0; // Track how many components are using the connection\n    this.currentUserType = null; // Track current connection type\n    this.isConnecting = false; // Prevent multiple simultaneous connection attempts\n  }\n\n  /**\n   * Initialize connection (simplified)\n   */\n  init(userType = 'admin') {\n    console.log('🚀 Initializing notification service');\n    if (!this.eventSource) {\n      this.connect(userType);\n    }\n    return Promise.resolve();\n  }\n\n  /**\n   * Cleanup (simplified)\n   */\n  cleanup() {\n    console.log('🧹 Notification service cleanup');\n    // Don't disconnect - let connection persist\n  }\n\n  /**\n   * Connect using POLLING instead of SSE (EMERGENCY FIX)\n   */\n  connect(userType = 'admin') {\n    // Don't create multiple polling intervals\n    if (this.pollingInterval) {\n      console.log('📊 Polling already active');\n      return Promise.resolve();\n    }\n    console.log('📊 EMERGENCY FIX: Using polling instead of SSE');\n    console.log('📊 Starting notification polling for:', userType);\n    this.isConnected = true;\n    this.currentUserType = userType;\n    this.emit('connected');\n\n    // Poll for notifications every 3 seconds\n    this.pollingInterval = setInterval(async () => {\n      try {\n        await this.pollNotifications(userType);\n      } catch (error) {\n        console.error('Polling error:', error);\n      }\n    }, 3000);\n\n    // Initial poll\n    this.pollNotifications(userType);\n    return Promise.resolve();\n  }\n\n  /**\n   * Poll for new notifications and updates\n   */\n  async pollNotifications(userType = 'admin') {\n    try {\n      const token = userType === 'admin' ? localStorage.getItem('adminToken') : localStorage.getItem('clientToken');\n      if (!token) return;\n\n      // Get unread count\n      const response = await fetch(`${this.baseURL}/notifications/unread-count`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n\n        // Emit notification event to update UI\n        this.emit('notification', {\n          type: 'unread_count_update',\n          count: data.count,\n          timestamp: new Date().toISOString()\n        });\n\n        // Simulate heartbeat\n        this.emit('notification', {\n          type: 'heartbeat',\n          timestamp: new Date().toISOString()\n        });\n      }\n    } catch (error) {\n      console.error('Failed to poll notifications:', error);\n    }\n  }\n\n  /**\n   * Disconnect from polling\n   */\n  disconnect() {\n    console.log('🔌 disconnect() called');\n    if (this.pollingInterval) {\n      console.log('🔌 Stopping polling interval');\n      clearInterval(this.pollingInterval);\n      this.pollingInterval = null;\n      this.isConnected = false;\n      console.log('Disconnected from notification polling');\n      this.emit('disconnected');\n    }\n\n    // Also clean up any SSE connections if they exist\n    if (this.eventSource) {\n      console.log('🔌 Closing any existing EventSource');\n      this.eventSource.close();\n      this.eventSource = null;\n    }\n  }\n\n  /**\n   * Schedule reconnection attempt\n   */\n  scheduleReconnect() {\n    console.log('🚫 Auto-reconnect disabled to prevent connection loops');\n    // Disabled to prevent connection issues during debugging\n  }\n\n  /**\n   * Handle incoming notification\n   */\n  handleNotification(notification) {\n    console.log('📢 Received notification:', notification);\n\n    // Emit to specific type listeners\n    this.emit(notification.type, notification);\n\n    // Emit to general notification listeners\n    this.emit('notification', notification);\n\n    // Show browser notification if permission granted\n    this.showBrowserNotification(notification);\n  }\n\n  /**\n   * Show browser notification\n   */\n  showBrowserNotification(notification) {\n    if ('Notification' in window && Notification.permission === 'granted') {\n      const options = {\n        body: notification.message,\n        icon: '/favicon.ico',\n        badge: '/favicon.ico',\n        tag: `notification-${notification.id}`,\n        requireInteraction: notification.priority === 'high' || notification.priority === 'urgent'\n      };\n      const browserNotification = new Notification(notification.title, options);\n      browserNotification.onclick = () => {\n        window.focus();\n        this.emit('notification_click', notification);\n        browserNotification.close();\n      };\n\n      // Auto close after 5 seconds for normal priority\n      if (notification.priority !== 'high' && notification.priority !== 'urgent') {\n        setTimeout(() => {\n          browserNotification.close();\n        }, 5000);\n      }\n    }\n  }\n\n  /**\n   * Request browser notification permission\n   */\n  async requestNotificationPermission() {\n    if ('Notification' in window) {\n      const permission = await Notification.requestPermission();\n      return permission === 'granted';\n    }\n    return false;\n  }\n\n  /**\n   * Subscribe to notification events\n   */\n  on(event, callback) {\n    if (!this.listeners.has(event)) {\n      this.listeners.set(event, new Set());\n    }\n    this.listeners.get(event).add(callback);\n  }\n\n  /**\n   * Unsubscribe from notification events\n   */\n  off(event, callback) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).delete(callback);\n    }\n  }\n\n  /**\n   * Emit event to listeners\n   */\n  emit(event, data = null) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).forEach(callback => {\n        try {\n          callback(data);\n        } catch (error) {\n          console.error(`Error in notification listener for ${event}:`, error);\n        }\n      });\n    }\n  }\n\n  /**\n   * Get user notifications\n   */\n  async getNotifications(page = 1, limit = 20, unreadOnly = false) {\n    try {\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin ? localStorage.getItem('adminToken') : localStorage.getItem('clientToken');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.get(`${this.baseURL}/notifications`, {\n        params: {\n          page,\n          limit,\n          unread_only: unreadOnly\n        },\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get notifications:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get unread notification count\n   */\n  async getUnreadCount() {\n    try {\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin ? localStorage.getItem('adminToken') : localStorage.getItem('clientToken');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.get(`${this.baseURL}/notifications/unread-count`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data.data.count;\n    } catch (error) {\n      console.error('Failed to get unread count:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Mark notification as read\n   */\n  async markAsRead(notificationId) {\n    try {\n      // Validate notification ID\n      if (!notificationId || notificationId === 'undefined') {\n        throw new Error('Invalid notification ID provided');\n      }\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin ? localStorage.getItem('adminToken') : localStorage.getItem('clientToken');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n      console.log('Marking notification as read:', notificationId);\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.put(`${this.baseURL}/notifications/${notificationId}/read`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to mark notification as read:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Mark all notifications as read\n   */\n  async markAllAsRead() {\n    try {\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin ? localStorage.getItem('adminToken') : localStorage.getItem('clientToken');\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.put(`${this.baseURL}/notifications/mark-all-read`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to mark all notifications as read:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Send test notification (admin only)\n   */\n  async sendTestNotification(data) {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.post(`${this.baseURL}/notifications/test`, data, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to send test notification:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get notification statistics (admin only)\n   */\n  async getStatistics() {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.get(`${this.baseURL}/notifications/statistics`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get notification statistics:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Test SSE connection (for debugging)\n   */\n  async testConnection() {\n    console.log('🧪 Testing SSE connection...');\n    try {\n      // Clear any existing connection\n      if (this.eventSource) {\n        console.log('🧪 Clearing existing connection');\n        this.eventSource.close();\n        this.eventSource = null;\n        this.isConnected = false;\n        this.isConnecting = false;\n      }\n\n      // Reset state\n      this.connectionRefs = 1;\n\n      // Test connection\n      await this.connect('admin');\n      console.log('🧪 Test connection established');\n\n      // Keep connection alive for 10 seconds\n      setTimeout(() => {\n        console.log('🧪 Test completed, keeping connection');\n      }, 10000);\n    } catch (error) {\n      console.error('🧪 Test connection failed:', error);\n    }\n  }\n\n  /**\n   * Clean up old notifications (admin only)\n   */\n  async cleanupOldNotifications(days = 90) {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.delete(`${this.baseURL}/notifications/cleanup`, {\n        params: {\n          days\n        },\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to cleanup old notifications:', error);\n      throw error;\n    }\n  }\n}\n\n// Create singleton instance\nconst notificationService = new NotificationService();\n\n// Make available globally for debugging\nwindow.__notificationService = notificationService;\n\n// Add global test function\nwindow.testSSE = () => {\n  console.log('🧪 Testing SSE connection manually...');\n  notificationService.connect('admin');\n};\nexport default notificationService;", "map": {"version": 3, "names": ["axios", "NotificationService", "constructor", "eventSource", "listeners", "Map", "isConnected", "reconnectAttempts", "maxReconnectAttempts", "reconnectDelay", "maxReconnectDelay", "baseURL", "process", "env", "VUE_APP_API_URL", "connectionRefs", "currentUserType", "isConnecting", "init", "userType", "console", "log", "connect", "Promise", "resolve", "cleanup", "pollingInterval", "emit", "setInterval", "pollNotifications", "error", "token", "localStorage", "getItem", "response", "fetch", "headers", "ok", "data", "json", "type", "count", "timestamp", "Date", "toISOString", "disconnect", "clearInterval", "close", "scheduleReconnect", "handleNotification", "notification", "showBrowserNotification", "window", "Notification", "permission", "options", "body", "message", "icon", "badge", "tag", "id", "requireInteraction", "priority", "browserNotification", "title", "onclick", "focus", "setTimeout", "requestNotificationPermission", "requestPermission", "on", "event", "callback", "has", "set", "Set", "get", "add", "off", "delete", "for<PERSON>ach", "getNotifications", "page", "limit", "unreadOnly", "isAdmin", "Error", "params", "unread_only", "getUnreadCount", "mark<PERSON><PERSON><PERSON>", "notificationId", "put", "markAllAsRead", "sendTestNotification", "post", "getStatistics", "testConnection", "cleanupOldNotifications", "days", "notificationService", "__notificationService", "testSSE"], "sources": ["D:/rhai_front_and_back/BOSFDR/src/services/notificationService.js"], "sourcesContent": ["import axios from 'axios';\n\nclass NotificationService {\n  constructor() {\n    this.eventSource = null;\n    this.listeners = new Map();\n    this.isConnected = false;\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n    this.reconnectDelay = 1000; // Start with 1 second\n    this.maxReconnectDelay = 30000; // Max 30 seconds\n    this.baseURL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api';\n    this.connectionRefs = 0; // Track how many components are using the connection\n    this.currentUserType = null; // Track current connection type\n    this.isConnecting = false; // Prevent multiple simultaneous connection attempts\n  }\n\n  /**\n   * Initialize connection (simplified)\n   */\n  init(userType = 'admin') {\n    console.log('🚀 Initializing notification service');\n    if (!this.eventSource) {\n      this.connect(userType);\n    }\n    return Promise.resolve();\n  }\n\n  /**\n   * Cleanup (simplified)\n   */\n  cleanup() {\n    console.log('🧹 Notification service cleanup');\n    // Don't disconnect - let connection persist\n  }\n\n  /**\n   * Connect using POLLING instead of SSE (EMERGENCY FIX)\n   */\n  connect(userType = 'admin') {\n    // Don't create multiple polling intervals\n    if (this.pollingInterval) {\n      console.log('📊 Polling already active');\n      return Promise.resolve();\n    }\n\n    console.log('📊 EMERGENCY FIX: Using polling instead of SSE');\n    console.log('📊 Starting notification polling for:', userType);\n\n    this.isConnected = true;\n    this.currentUserType = userType;\n    this.emit('connected');\n\n    // Poll for notifications every 3 seconds\n    this.pollingInterval = setInterval(async () => {\n      try {\n        await this.pollNotifications(userType);\n      } catch (error) {\n        console.error('Polling error:', error);\n      }\n    }, 3000);\n\n    // Initial poll\n    this.pollNotifications(userType);\n\n    return Promise.resolve();\n  }\n\n  /**\n   * Poll for new notifications and updates\n   */\n  async pollNotifications(userType = 'admin') {\n    try {\n      const token = userType === 'admin'\n        ? localStorage.getItem('adminToken')\n        : localStorage.getItem('clientToken');\n\n      if (!token) return;\n\n      // Get unread count\n      const response = await fetch(`${this.baseURL}/notifications/unread-count`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n\n        // Emit notification event to update UI\n        this.emit('notification', {\n          type: 'unread_count_update',\n          count: data.count,\n          timestamp: new Date().toISOString()\n        });\n\n        // Simulate heartbeat\n        this.emit('notification', {\n          type: 'heartbeat',\n          timestamp: new Date().toISOString()\n        });\n      }\n    } catch (error) {\n      console.error('Failed to poll notifications:', error);\n    }\n  }\n\n  /**\n   * Disconnect from polling\n   */\n  disconnect() {\n    console.log('🔌 disconnect() called');\n\n    if (this.pollingInterval) {\n      console.log('🔌 Stopping polling interval');\n      clearInterval(this.pollingInterval);\n      this.pollingInterval = null;\n      this.isConnected = false;\n      console.log('Disconnected from notification polling');\n      this.emit('disconnected');\n    }\n\n    // Also clean up any SSE connections if they exist\n    if (this.eventSource) {\n      console.log('🔌 Closing any existing EventSource');\n      this.eventSource.close();\n      this.eventSource = null;\n    }\n  }\n\n  /**\n   * Schedule reconnection attempt\n   */\n  scheduleReconnect() {\n    console.log('🚫 Auto-reconnect disabled to prevent connection loops');\n    // Disabled to prevent connection issues during debugging\n  }\n\n  /**\n   * Handle incoming notification\n   */\n  handleNotification(notification) {\n    console.log('📢 Received notification:', notification);\n    \n    // Emit to specific type listeners\n    this.emit(notification.type, notification);\n    \n    // Emit to general notification listeners\n    this.emit('notification', notification);\n    \n    // Show browser notification if permission granted\n    this.showBrowserNotification(notification);\n  }\n\n  /**\n   * Show browser notification\n   */\n  showBrowserNotification(notification) {\n    if ('Notification' in window && Notification.permission === 'granted') {\n      const options = {\n        body: notification.message,\n        icon: '/favicon.ico',\n        badge: '/favicon.ico',\n        tag: `notification-${notification.id}`,\n        requireInteraction: notification.priority === 'high' || notification.priority === 'urgent'\n      };\n\n      const browserNotification = new Notification(notification.title, options);\n      \n      browserNotification.onclick = () => {\n        window.focus();\n        this.emit('notification_click', notification);\n        browserNotification.close();\n      };\n\n      // Auto close after 5 seconds for normal priority\n      if (notification.priority !== 'high' && notification.priority !== 'urgent') {\n        setTimeout(() => {\n          browserNotification.close();\n        }, 5000);\n      }\n    }\n  }\n\n  /**\n   * Request browser notification permission\n   */\n  async requestNotificationPermission() {\n    if ('Notification' in window) {\n      const permission = await Notification.requestPermission();\n      return permission === 'granted';\n    }\n    return false;\n  }\n\n  /**\n   * Subscribe to notification events\n   */\n  on(event, callback) {\n    if (!this.listeners.has(event)) {\n      this.listeners.set(event, new Set());\n    }\n    this.listeners.get(event).add(callback);\n  }\n\n  /**\n   * Unsubscribe from notification events\n   */\n  off(event, callback) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).delete(callback);\n    }\n  }\n\n  /**\n   * Emit event to listeners\n   */\n  emit(event, data = null) {\n    if (this.listeners.has(event)) {\n      this.listeners.get(event).forEach(callback => {\n        try {\n          callback(data);\n        } catch (error) {\n          console.error(`Error in notification listener for ${event}:`, error);\n        }\n      });\n    }\n  }\n\n  /**\n   * Get user notifications\n   */\n  async getNotifications(page = 1, limit = 20, unreadOnly = false) {\n    try {\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin\n        ? localStorage.getItem('adminToken')\n        : localStorage.getItem('clientToken');\n\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.get(`${this.baseURL}/notifications`, {\n        params: { page, limit, unread_only: unreadOnly },\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get notifications:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get unread notification count\n   */\n  async getUnreadCount() {\n    try {\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin\n        ? localStorage.getItem('adminToken')\n        : localStorage.getItem('clientToken');\n\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.get(`${this.baseURL}/notifications/unread-count`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data.data.count;\n    } catch (error) {\n      console.error('Failed to get unread count:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Mark notification as read\n   */\n  async markAsRead(notificationId) {\n    try {\n      // Validate notification ID\n      if (!notificationId || notificationId === 'undefined') {\n        throw new Error('Invalid notification ID provided');\n      }\n\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin\n        ? localStorage.getItem('adminToken')\n        : localStorage.getItem('clientToken');\n\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      console.log('Marking notification as read:', notificationId);\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.put(`${this.baseURL}/notifications/${notificationId}/read`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to mark notification as read:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Mark all notifications as read\n   */\n  async markAllAsRead() {\n    try {\n      const isAdmin = !!localStorage.getItem('adminToken');\n      const token = isAdmin\n        ? localStorage.getItem('adminToken')\n        : localStorage.getItem('clientToken');\n\n      if (!token) {\n        throw new Error('No authentication token found');\n      }\n\n      // Use unified endpoint for both admin and client\n      const response = await axios.put(`${this.baseURL}/notifications/mark-all-read`, {}, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to mark all notifications as read:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Send test notification (admin only)\n   */\n  async sendTestNotification(data) {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.post(`${this.baseURL}/notifications/test`, data, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to send test notification:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get notification statistics (admin only)\n   */\n  async getStatistics() {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.get(`${this.baseURL}/notifications/statistics`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get notification statistics:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Test SSE connection (for debugging)\n   */\n  async testConnection() {\n    console.log('🧪 Testing SSE connection...');\n\n    try {\n      // Clear any existing connection\n      if (this.eventSource) {\n        console.log('🧪 Clearing existing connection');\n        this.eventSource.close();\n        this.eventSource = null;\n        this.isConnected = false;\n        this.isConnecting = false;\n      }\n\n      // Reset state\n      this.connectionRefs = 1;\n\n      // Test connection\n      await this.connect('admin');\n\n      console.log('🧪 Test connection established');\n\n      // Keep connection alive for 10 seconds\n      setTimeout(() => {\n        console.log('🧪 Test completed, keeping connection');\n      }, 10000);\n\n    } catch (error) {\n      console.error('🧪 Test connection failed:', error);\n    }\n  }\n\n  /**\n   * Clean up old notifications (admin only)\n   */\n  async cleanupOldNotifications(days = 90) {\n    try {\n      const token = localStorage.getItem('adminToken');\n      const response = await axios.delete(`${this.baseURL}/notifications/cleanup`, {\n        params: { days },\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to cleanup old notifications:', error);\n      throw error;\n    }\n  }\n}\n\n// Create singleton instance\nconst notificationService = new NotificationService();\n\n// Make available globally for debugging\nwindow.__notificationService = notificationService;\n\n// Add global test function\nwindow.testSSE = () => {\n  console.log('🧪 Testing SSE connection manually...');\n  notificationService.connect('admin');\n};\n\nexport default notificationService;\n"], "mappings": ";;;;;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,mBAAmB,CAAC;EACxBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACC,cAAc,GAAG,IAAI,CAAC,CAAC;IAC5B,IAAI,CAACC,iBAAiB,GAAG,KAAK,CAAC,CAAC;IAChC,IAAI,CAACC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,eAAe,IAAI,2BAA2B;IACzE,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC,CAAC;IACzB,IAAI,CAACC,eAAe,GAAG,IAAI,CAAC,CAAC;IAC7B,IAAI,CAACC,YAAY,GAAG,KAAK,CAAC,CAAC;EAC7B;;EAEA;AACF;AACA;EACEC,IAAIA,CAACC,QAAQ,GAAG,OAAO,EAAE;IACvBC,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnD,IAAI,CAAC,IAAI,CAAClB,WAAW,EAAE;MACrB,IAAI,CAACmB,OAAO,CAACH,QAAQ,CAAC;IACxB;IACA,OAAOI,OAAO,CAACC,OAAO,CAAC,CAAC;EAC1B;;EAEA;AACF;AACA;EACEC,OAAOA,CAAA,EAAG;IACRL,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C;EACF;;EAEA;AACF;AACA;EACEC,OAAOA,CAACH,QAAQ,GAAG,OAAO,EAAE;IAC1B;IACA,IAAI,IAAI,CAACO,eAAe,EAAE;MACxBN,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACxC,OAAOE,OAAO,CAACC,OAAO,CAAC,CAAC;IAC1B;IAEAJ,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;IAC7DD,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEF,QAAQ,CAAC;IAE9D,IAAI,CAACb,WAAW,GAAG,IAAI;IACvB,IAAI,CAACU,eAAe,GAAGG,QAAQ;IAC/B,IAAI,CAACQ,IAAI,CAAC,WAAW,CAAC;;IAEtB;IACA,IAAI,CAACD,eAAe,GAAGE,WAAW,CAAC,YAAY;MAC7C,IAAI;QACF,MAAM,IAAI,CAACC,iBAAiB,CAACV,QAAQ,CAAC;MACxC,CAAC,CAAC,OAAOW,KAAK,EAAE;QACdV,OAAO,CAACU,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACxC;IACF,CAAC,EAAE,IAAI,CAAC;;IAER;IACA,IAAI,CAACD,iBAAiB,CAACV,QAAQ,CAAC;IAEhC,OAAOI,OAAO,CAACC,OAAO,CAAC,CAAC;EAC1B;;EAEA;AACF;AACA;EACE,MAAMK,iBAAiBA,CAACV,QAAQ,GAAG,OAAO,EAAE;IAC1C,IAAI;MACF,MAAMY,KAAK,GAAGZ,QAAQ,KAAK,OAAO,GAC9Ba,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAClCD,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MAEvC,IAAI,CAACF,KAAK,EAAE;;MAEZ;MACA,MAAMG,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAACxB,OAAO,6BAA6B,EAAE;QACzEyB,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,KAAK;QAClC;MACF,CAAC,CAAC;MAEF,IAAIG,QAAQ,CAACG,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;;QAElC;QACA,IAAI,CAACZ,IAAI,CAAC,cAAc,EAAE;UACxBa,IAAI,EAAE,qBAAqB;UAC3BC,KAAK,EAAEH,IAAI,CAACG,KAAK;UACjBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACpC,CAAC,CAAC;;QAEF;QACA,IAAI,CAACjB,IAAI,CAAC,cAAc,EAAE;UACxBa,IAAI,EAAE,WAAW;UACjBE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACpC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOd,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF;;EAEA;AACF;AACA;EACEe,UAAUA,CAAA,EAAG;IACXzB,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IAErC,IAAI,IAAI,CAACK,eAAe,EAAE;MACxBN,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3CyB,aAAa,CAAC,IAAI,CAACpB,eAAe,CAAC;MACnC,IAAI,CAACA,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACpB,WAAW,GAAG,KAAK;MACxBc,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrD,IAAI,CAACM,IAAI,CAAC,cAAc,CAAC;IAC3B;;IAEA;IACA,IAAI,IAAI,CAACxB,WAAW,EAAE;MACpBiB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD,IAAI,CAAClB,WAAW,CAAC4C,KAAK,CAAC,CAAC;MACxB,IAAI,CAAC5C,WAAW,GAAG,IAAI;IACzB;EACF;;EAEA;AACF;AACA;EACE6C,iBAAiBA,CAAA,EAAG;IAClB5B,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;IACrE;EACF;;EAEA;AACF;AACA;EACE4B,kBAAkBA,CAACC,YAAY,EAAE;IAC/B9B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE6B,YAAY,CAAC;;IAEtD;IACA,IAAI,CAACvB,IAAI,CAACuB,YAAY,CAACV,IAAI,EAAEU,YAAY,CAAC;;IAE1C;IACA,IAAI,CAACvB,IAAI,CAAC,cAAc,EAAEuB,YAAY,CAAC;;IAEvC;IACA,IAAI,CAACC,uBAAuB,CAACD,YAAY,CAAC;EAC5C;;EAEA;AACF;AACA;EACEC,uBAAuBA,CAACD,YAAY,EAAE;IACpC,IAAI,cAAc,IAAIE,MAAM,IAAIC,YAAY,CAACC,UAAU,KAAK,SAAS,EAAE;MACrE,MAAMC,OAAO,GAAG;QACdC,IAAI,EAAEN,YAAY,CAACO,OAAO;QAC1BC,IAAI,EAAE,cAAc;QACpBC,KAAK,EAAE,cAAc;QACrBC,GAAG,EAAE,gBAAgBV,YAAY,CAACW,EAAE,EAAE;QACtCC,kBAAkB,EAAEZ,YAAY,CAACa,QAAQ,KAAK,MAAM,IAAIb,YAAY,CAACa,QAAQ,KAAK;MACpF,CAAC;MAED,MAAMC,mBAAmB,GAAG,IAAIX,YAAY,CAACH,YAAY,CAACe,KAAK,EAAEV,OAAO,CAAC;MAEzES,mBAAmB,CAACE,OAAO,GAAG,MAAM;QAClCd,MAAM,CAACe,KAAK,CAAC,CAAC;QACd,IAAI,CAACxC,IAAI,CAAC,oBAAoB,EAAEuB,YAAY,CAAC;QAC7Cc,mBAAmB,CAACjB,KAAK,CAAC,CAAC;MAC7B,CAAC;;MAED;MACA,IAAIG,YAAY,CAACa,QAAQ,KAAK,MAAM,IAAIb,YAAY,CAACa,QAAQ,KAAK,QAAQ,EAAE;QAC1EK,UAAU,CAAC,MAAM;UACfJ,mBAAmB,CAACjB,KAAK,CAAC,CAAC;QAC7B,CAAC,EAAE,IAAI,CAAC;MACV;IACF;EACF;;EAEA;AACF;AACA;EACE,MAAMsB,6BAA6BA,CAAA,EAAG;IACpC,IAAI,cAAc,IAAIjB,MAAM,EAAE;MAC5B,MAAME,UAAU,GAAG,MAAMD,YAAY,CAACiB,iBAAiB,CAAC,CAAC;MACzD,OAAOhB,UAAU,KAAK,SAAS;IACjC;IACA,OAAO,KAAK;EACd;;EAEA;AACF;AACA;EACEiB,EAAEA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IAClB,IAAI,CAAC,IAAI,CAACrE,SAAS,CAACsE,GAAG,CAACF,KAAK,CAAC,EAAE;MAC9B,IAAI,CAACpE,SAAS,CAACuE,GAAG,CAACH,KAAK,EAAE,IAAII,GAAG,CAAC,CAAC,CAAC;IACtC;IACA,IAAI,CAACxE,SAAS,CAACyE,GAAG,CAACL,KAAK,CAAC,CAACM,GAAG,CAACL,QAAQ,CAAC;EACzC;;EAEA;AACF;AACA;EACEM,GAAGA,CAACP,KAAK,EAAEC,QAAQ,EAAE;IACnB,IAAI,IAAI,CAACrE,SAAS,CAACsE,GAAG,CAACF,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACpE,SAAS,CAACyE,GAAG,CAACL,KAAK,CAAC,CAACQ,MAAM,CAACP,QAAQ,CAAC;IAC5C;EACF;;EAEA;AACF;AACA;EACE9C,IAAIA,CAAC6C,KAAK,EAAElC,IAAI,GAAG,IAAI,EAAE;IACvB,IAAI,IAAI,CAAClC,SAAS,CAACsE,GAAG,CAACF,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACpE,SAAS,CAACyE,GAAG,CAACL,KAAK,CAAC,CAACS,OAAO,CAACR,QAAQ,IAAI;QAC5C,IAAI;UACFA,QAAQ,CAACnC,IAAI,CAAC;QAChB,CAAC,CAAC,OAAOR,KAAK,EAAE;UACdV,OAAO,CAACU,KAAK,CAAC,sCAAsC0C,KAAK,GAAG,EAAE1C,KAAK,CAAC;QACtE;MACF,CAAC,CAAC;IACJ;EACF;;EAEA;AACF;AACA;EACE,MAAMoD,gBAAgBA,CAACC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,EAAE,EAAEC,UAAU,GAAG,KAAK,EAAE;IAC/D,IAAI;MACF,MAAMC,OAAO,GAAG,CAAC,CAACtD,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MACpD,MAAMF,KAAK,GAAGuD,OAAO,GACjBtD,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAClCD,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MAEvC,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIwD,KAAK,CAAC,+BAA+B,CAAC;MAClD;;MAEA;MACA,MAAMrD,QAAQ,GAAG,MAAMlC,KAAK,CAAC6E,GAAG,CAAC,GAAG,IAAI,CAAClE,OAAO,gBAAgB,EAAE;QAChE6E,MAAM,EAAE;UAAEL,IAAI;UAAEC,KAAK;UAAEK,WAAW,EAAEJ;QAAW,CAAC;QAChDjD,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAOG,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM4D,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAMJ,OAAO,GAAG,CAAC,CAACtD,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MACpD,MAAMF,KAAK,GAAGuD,OAAO,GACjBtD,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAClCD,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MAEvC,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIwD,KAAK,CAAC,+BAA+B,CAAC;MAClD;;MAEA;MACA,MAAMrD,QAAQ,GAAG,MAAMlC,KAAK,CAAC6E,GAAG,CAAC,GAAG,IAAI,CAAClE,OAAO,6BAA6B,EAAE;QAC7EyB,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAOG,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACG,KAAK;IACjC,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM6D,UAAUA,CAACC,cAAc,EAAE;IAC/B,IAAI;MACF;MACA,IAAI,CAACA,cAAc,IAAIA,cAAc,KAAK,WAAW,EAAE;QACrD,MAAM,IAAIL,KAAK,CAAC,kCAAkC,CAAC;MACrD;MAEA,MAAMD,OAAO,GAAG,CAAC,CAACtD,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MACpD,MAAMF,KAAK,GAAGuD,OAAO,GACjBtD,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAClCD,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MAEvC,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIwD,KAAK,CAAC,+BAA+B,CAAC;MAClD;MAEAnE,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEuE,cAAc,CAAC;;MAE5D;MACA,MAAM1D,QAAQ,GAAG,MAAMlC,KAAK,CAAC6F,GAAG,CAAC,GAAG,IAAI,CAAClF,OAAO,kBAAkBiF,cAAc,OAAO,EAAE,CAAC,CAAC,EAAE;QAC3FxD,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAOG,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMgE,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMR,OAAO,GAAG,CAAC,CAACtD,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MACpD,MAAMF,KAAK,GAAGuD,OAAO,GACjBtD,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,GAClCD,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;MAEvC,IAAI,CAACF,KAAK,EAAE;QACV,MAAM,IAAIwD,KAAK,CAAC,+BAA+B,CAAC;MAClD;;MAEA;MACA,MAAMrD,QAAQ,GAAG,MAAMlC,KAAK,CAAC6F,GAAG,CAAC,GAAG,IAAI,CAAClF,OAAO,8BAA8B,EAAE,CAAC,CAAC,EAAE;QAClFyB,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAOG,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMiE,oBAAoBA,CAACzD,IAAI,EAAE;IAC/B,IAAI;MACF,MAAMP,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,MAAMC,QAAQ,GAAG,MAAMlC,KAAK,CAACgG,IAAI,CAAC,GAAG,IAAI,CAACrF,OAAO,qBAAqB,EAAE2B,IAAI,EAAE;QAC5EF,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAOG,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMmE,aAAaA,CAAA,EAAG;IACpB,IAAI;MACF,MAAMlE,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,MAAMC,QAAQ,GAAG,MAAMlC,KAAK,CAAC6E,GAAG,CAAC,GAAG,IAAI,CAAClE,OAAO,2BAA2B,EAAE;QAC3EyB,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAOG,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;MAC9D,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMoE,cAAcA,CAAA,EAAG;IACrB9E,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAE3C,IAAI;MACF;MACA,IAAI,IAAI,CAAClB,WAAW,EAAE;QACpBiB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,IAAI,CAAClB,WAAW,CAAC4C,KAAK,CAAC,CAAC;QACxB,IAAI,CAAC5C,WAAW,GAAG,IAAI;QACvB,IAAI,CAACG,WAAW,GAAG,KAAK;QACxB,IAAI,CAACW,YAAY,GAAG,KAAK;MAC3B;;MAEA;MACA,IAAI,CAACF,cAAc,GAAG,CAAC;;MAEvB;MACA,MAAM,IAAI,CAACO,OAAO,CAAC,OAAO,CAAC;MAE3BF,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;MAE7C;MACA+C,UAAU,CAAC,MAAM;QACfhD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACtD,CAAC,EAAE,KAAK,CAAC;IAEX,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF;;EAEA;AACF;AACA;EACE,MAAMqE,uBAAuBA,CAACC,IAAI,GAAG,EAAE,EAAE;IACvC,IAAI;MACF,MAAMrE,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAChD,MAAMC,QAAQ,GAAG,MAAMlC,KAAK,CAACgF,MAAM,CAAC,GAAG,IAAI,CAACrE,OAAO,wBAAwB,EAAE;QAC3E6E,MAAM,EAAE;UAAEY;QAAK,CAAC;QAChBhE,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,KAAK;QAClC;MACF,CAAC,CAAC;MACF,OAAOG,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,MAAMA,KAAK;IACb;EACF;AACF;;AAEA;AACA,MAAMuE,mBAAmB,GAAG,IAAIpG,mBAAmB,CAAC,CAAC;;AAErD;AACAmD,MAAM,CAACkD,qBAAqB,GAAGD,mBAAmB;;AAElD;AACAjD,MAAM,CAACmD,OAAO,GAAG,MAAM;EACrBnF,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;EACpDgF,mBAAmB,CAAC/E,OAAO,CAAC,OAAO,CAAC;AACtC,CAAC;AAED,eAAe+E,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}