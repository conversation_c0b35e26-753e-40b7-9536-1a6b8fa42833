{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport AdminNotifications from './AdminNotifications.vue';\nexport default {\n  name: 'AdminHeader',\n  components: {\n    AdminNotifications\n  },\n  props: {\n    userName: {\n      type: String,\n      default: 'Admin'\n    },\n    showUserDropdown: {\n      type: Boolean,\n      default: false\n    },\n    sidebarCollapsed: {\n      type: Boolean,\n      default: false\n    },\n    activeMenu: {\n      type: String,\n      default: 'dashboard'\n    }\n  },\n  emits: ['sidebar-toggle', 'user-dropdown-toggle', 'menu-action', 'logout', 'error', 'new-notification'],\n  computed: {\n    // Get display name from admin data or fallback to prop\n    displayUserName() {\n      const adminData = this.$adminAuth?.getAdminData();\n      if (adminData) {\n        return adminData.first_name && adminData.last_name ? `${adminData.first_name} ${adminData.last_name}` : adminData.username || this.userName;\n      }\n      return this.userName || 'Admin';\n    },\n    // Get display role from admin data\n    displayUserRole() {\n      const adminData = this.$adminAuth?.getAdminData();\n      if (adminData && adminData.role) {\n        return adminData.role.charAt(0).toUpperCase() + adminData.role.slice(1);\n      }\n      return 'Admin';\n    }\n  },\n  mounted() {\n    // Setup event listeners for outside clicks\n    document.addEventListener('click', this.handleOutsideClick);\n  },\n  beforeUnmount() {\n    // Clean up event listeners\n    document.removeEventListener('click', this.handleOutsideClick);\n  },\n  methods: {\n    // Get page title based on current route\n    getPageTitle() {\n      const path = this.$route.path;\n      const routeTitles = {\n        '/admin/dashboard': 'Admin Dashboard',\n        '/admin/users': 'User Management',\n        '/admin/requests': 'Document Requests',\n        '/admin/reports': 'Reports & Analytics',\n        '/admin/settings': 'System Settings',\n        '/admin/activity-logs': 'Activity Logs',\n        '/admin/audit-logs': 'Audit Logs',\n        '/admin/profile': 'Admin Profile'\n      };\n\n      // Use route-based title first, then fall back to activeMenu prop, then default\n      return routeTitles[path] || this.$route.meta?.title || 'Admin Dashboard';\n    },\n    // Handle sidebar toggle\n    handleSidebarToggle() {\n      this.$emit('sidebar-toggle');\n    },\n    // Handle user dropdown toggle\n    handleUserDropdownToggle() {\n      this.$emit('user-dropdown-toggle');\n    },\n    // Handle menu actions (profile, settings, etc.)\n    handleMenuAction(action, event) {\n      // Prevent default link behavior\n      if (event) {\n        event.preventDefault();\n      }\n\n      // Navigate to the appropriate route\n      const routes = {\n        'profile': '/admin/profile',\n        'settings': '/admin/settings',\n        'account': '/admin/profile' // Account info redirects to profile\n      };\n      const route = routes[action];\n      if (route && this.$route.path !== route) {\n        this.$router.push(route).catch(err => {\n          // Handle navigation errors gracefully\n          if (err.name !== 'NavigationDuplicated') {\n            console.error('Navigation error:', err);\n          }\n        });\n      }\n\n      // Close dropdown after navigation\n      if (this.showUserDropdown) {\n        this.$emit('user-dropdown-toggle');\n      }\n\n      // Still emit for parent components that need to track actions\n      this.$emit('menu-action', action);\n    },\n    // Handle logout\n    handleLogout(event) {\n      // Prevent default link behavior\n      if (event) {\n        event.preventDefault();\n      }\n      this.$emit('logout');\n    },\n    // Handle outside clicks to close dropdowns\n    handleOutsideClick(event) {\n      // Check if click is outside user dropdown\n      if (!event.target.closest('.user-dropdown')) {\n        if (this.showUserDropdown) {\n          this.$emit('user-dropdown-toggle');\n        }\n      }\n    },\n    // Notification handlers\n    handleNewNotification(notification) {\n      console.log('New notification received:', notification);\n      // Emit to parent component if needed\n      this.$emit('new-notification', notification);\n    },\n    handleNotificationClick(notification) {\n      console.log('Notification clicked:', notification);\n      // Handle notification click - could navigate to relevant page\n      if (notification.data && notification.data.request_id) {\n        // Navigate to request details if it's a request-related notification\n        this.$router.push(`/admin/requests?highlight=${notification.data.request_id}`);\n      }\n      this.$emit('notification-click', notification);\n    },\n    handleNotificationError(error) {\n      console.error('Notification error:', error);\n      this.$emit('error', error);\n    }\n  }\n};", "map": {"version": 3, "names": ["AdminNotifications", "name", "components", "props", "userName", "type", "String", "default", "showUserDropdown", "Boolean", "sidebarCollapsed", "activeMenu", "emits", "computed", "displayUserName", "adminData", "$adminAuth", "getAdminData", "first_name", "last_name", "username", "displayUserRole", "role", "char<PERSON>t", "toUpperCase", "slice", "mounted", "document", "addEventListener", "handleOutsideClick", "beforeUnmount", "removeEventListener", "methods", "getPageTitle", "path", "$route", "routeTitles", "meta", "title", "handleSidebarToggle", "$emit", "handleUserDropdownToggle", "handleMenuAction", "action", "event", "preventDefault", "routes", "route", "$router", "push", "catch", "err", "console", "error", "handleLogout", "target", "closest", "handleNewNotification", "notification", "log", "handleNotificationClick", "data", "request_id", "handleNotificationError"], "sources": ["D:\\rhai_front_and_back\\BOSFDR\\src\\components\\admin\\AdminHeader.vue"], "sourcesContent": ["<template>\n  <header class=\"dashboard-header\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n    <div class=\"header-content\">\n      <!-- Left Section -->\n      <div class=\"header-left\">\n        <button class=\"sidebar-toggle\" @click=\"handleSidebarToggle\">\n          <i class=\"fas fa-bars\"></i>\n        </button>\n        <div class=\"page-title\">\n          <h1>{{ getPageTitle() }}</h1>\n        </div>\n      </div>\n\n      <!-- Header Actions -->\n      <div class=\"header-actions\">\n        <!-- Notifications -->\n        <AdminNotifications\n          @new-notification=\"handleNewNotification\"\n          @notification-click=\"handleNotificationClick\"\n          @error=\"handleNotificationError\"\n        />\n\n        <!-- User Profile -->\n        <div class=\"user-dropdown\" :class=\"{ active: showUserDropdown }\">\n          <button class=\"user-btn\" @click=\"handleUserDropdownToggle\">\n            <div class=\"user-avatar\">\n              <i class=\"fas fa-user-circle\"></i>\n            </div>\n            <div class=\"user-info\">\n              <span class=\"user-name\">{{ displayUserName }}</span>\n              <span class=\"user-role\">{{ displayUserRole }}</span>\n            </div>\n            <i class=\"fas fa-chevron-down dropdown-arrow\"></i>\n          </button>\n\n          <div v-if=\"showUserDropdown\" class=\"dropdown-menu\">\n            <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('profile', $event)\">\n              <i class=\"fas fa-user me-2\"></i>\n              My Profile\n            </a>\n            <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('settings', $event)\">\n              <i class=\"fas fa-cog me-2\"></i>\n              Settings\n            </a>\n            <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('account', $event)\">\n              <i class=\"fas fa-id-card me-2\"></i>\n              Account Info\n            </a>\n            <div class=\"dropdown-divider\"></div>\n            <a href=\"#\" class=\"dropdown-item\" @click=\"handleLogout($event)\">\n              <i class=\"fas fa-sign-out-alt me-2\"></i>\n              Logout\n            </a>\n          </div>\n        </div>\n      </div>\n    </div>\n  </header>\n</template>\n\n<script>\nimport AdminNotifications from './AdminNotifications.vue';\n\nexport default {\n  name: 'AdminHeader',\n  components: {\n    AdminNotifications\n  },\n  props: {\n    userName: {\n      type: String,\n      default: 'Admin'\n    },\n    showUserDropdown: {\n      type: Boolean,\n      default: false\n    },\n    sidebarCollapsed: {\n      type: Boolean,\n      default: false\n    },\n    activeMenu: {\n      type: String,\n      default: 'dashboard'\n    }\n  },\n\n  emits: [\n    'sidebar-toggle',\n    'user-dropdown-toggle',\n    'menu-action',\n    'logout',\n    'error',\n    'new-notification'\n  ],\n\n  computed: {\n    // Get display name from admin data or fallback to prop\n    displayUserName() {\n      const adminData = this.$adminAuth?.getAdminData();\n      if (adminData) {\n        return adminData.first_name && adminData.last_name\n          ? `${adminData.first_name} ${adminData.last_name}`\n          : adminData.username || this.userName;\n      }\n      return this.userName || 'Admin';\n    },\n\n    // Get display role from admin data\n    displayUserRole() {\n      const adminData = this.$adminAuth?.getAdminData();\n      if (adminData && adminData.role) {\n        return adminData.role.charAt(0).toUpperCase() + adminData.role.slice(1);\n      }\n      return 'Admin';\n    }\n  },\n\n\n\n  mounted() {\n    // Setup event listeners for outside clicks\n    document.addEventListener('click', this.handleOutsideClick);\n  },\n\n  beforeUnmount() {\n    // Clean up event listeners\n    document.removeEventListener('click', this.handleOutsideClick);\n  },\n\n  methods: {\n    // Get page title based on current route\n    getPageTitle() {\n      const path = this.$route.path;\n      const routeTitles = {\n        '/admin/dashboard': 'Admin Dashboard',\n        '/admin/users': 'User Management',\n        '/admin/requests': 'Document Requests',\n        '/admin/reports': 'Reports & Analytics',\n        '/admin/settings': 'System Settings',\n        '/admin/activity-logs': 'Activity Logs',\n        '/admin/audit-logs': 'Audit Logs',\n        '/admin/profile': 'Admin Profile'\n      };\n\n      // Use route-based title first, then fall back to activeMenu prop, then default\n      return routeTitles[path] || this.$route.meta?.title || 'Admin Dashboard';\n    },\n\n    // Handle sidebar toggle\n    handleSidebarToggle() {\n      this.$emit('sidebar-toggle');\n    },\n\n\n\n    // Handle user dropdown toggle\n    handleUserDropdownToggle() {\n      this.$emit('user-dropdown-toggle');\n    },\n\n    // Handle menu actions (profile, settings, etc.)\n    handleMenuAction(action, event) {\n      // Prevent default link behavior\n      if (event) {\n        event.preventDefault();\n      }\n\n      // Navigate to the appropriate route\n      const routes = {\n        'profile': '/admin/profile',\n        'settings': '/admin/settings',\n        'account': '/admin/profile' // Account info redirects to profile\n      };\n\n      const route = routes[action];\n      if (route && this.$route.path !== route) {\n        this.$router.push(route).catch(err => {\n          // Handle navigation errors gracefully\n          if (err.name !== 'NavigationDuplicated') {\n            console.error('Navigation error:', err);\n          }\n        });\n      }\n\n      // Close dropdown after navigation\n      if (this.showUserDropdown) {\n        this.$emit('user-dropdown-toggle');\n      }\n\n      // Still emit for parent components that need to track actions\n      this.$emit('menu-action', action);\n    },\n\n    // Handle logout\n    handleLogout(event) {\n      // Prevent default link behavior\n      if (event) {\n        event.preventDefault();\n      }\n      this.$emit('logout');\n    },\n\n\n\n    // Handle outside clicks to close dropdowns\n    handleOutsideClick(event) {\n      // Check if click is outside user dropdown\n      if (!event.target.closest('.user-dropdown')) {\n        if (this.showUserDropdown) {\n          this.$emit('user-dropdown-toggle');\n        }\n      }\n    },\n\n    // Notification handlers\n    handleNewNotification(notification) {\n      console.log('New notification received:', notification);\n      // Emit to parent component if needed\n      this.$emit('new-notification', notification);\n    },\n\n    handleNotificationClick(notification) {\n      console.log('Notification clicked:', notification);\n      // Handle notification click - could navigate to relevant page\n      if (notification.data && notification.data.request_id) {\n        // Navigate to request details if it's a request-related notification\n        this.$router.push(`/admin/requests?highlight=${notification.data.request_id}`);\n      }\n      this.$emit('notification-click', notification);\n    },\n\n    handleNotificationError(error) {\n      console.error('Notification error:', error);\n      this.$emit('error', error);\n    }\n  }\n};\n</script>\n\n<style scoped src=\"./css/adminHeader.css\"></style>\n"], "mappings": ";AA6DA,OAAOA,kBAAiB,MAAO,0BAA0B;AAEzD,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,KAAK,EAAE;IACLC,QAAQ,EAAE;MACRC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDC,gBAAgB,EAAE;MAChBH,IAAI,EAAEI,OAAO;MACbF,OAAO,EAAE;IACX,CAAC;IACDG,gBAAgB,EAAE;MAChBL,IAAI,EAAEI,OAAO;MACbF,OAAO,EAAE;IACX,CAAC;IACDI,UAAU,EAAE;MACVN,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX;EACF,CAAC;EAEDK,KAAK,EAAE,CACL,gBAAgB,EAChB,sBAAsB,EACtB,aAAa,EACb,QAAQ,EACR,OAAO,EACP,kBAAiB,CAClB;EAEDC,QAAQ,EAAE;IACR;IACAC,eAAeA,CAAA,EAAG;MAChB,MAAMC,SAAQ,GAAI,IAAI,CAACC,UAAU,EAAEC,YAAY,CAAC,CAAC;MACjD,IAAIF,SAAS,EAAE;QACb,OAAOA,SAAS,CAACG,UAAS,IAAKH,SAAS,CAACI,SAAQ,GAC7C,GAAGJ,SAAS,CAACG,UAAU,IAAIH,SAAS,CAACI,SAAS,EAAC,GAC/CJ,SAAS,CAACK,QAAO,IAAK,IAAI,CAAChB,QAAQ;MACzC;MACA,OAAO,IAAI,CAACA,QAAO,IAAK,OAAO;IACjC,CAAC;IAED;IACAiB,eAAeA,CAAA,EAAG;MAChB,MAAMN,SAAQ,GAAI,IAAI,CAACC,UAAU,EAAEC,YAAY,CAAC,CAAC;MACjD,IAAIF,SAAQ,IAAKA,SAAS,CAACO,IAAI,EAAE;QAC/B,OAAOP,SAAS,CAACO,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,IAAIT,SAAS,CAACO,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC;MACzE;MACA,OAAO,OAAO;IAChB;EACF,CAAC;EAIDC,OAAOA,CAAA,EAAG;IACR;IACAC,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACC,kBAAkB,CAAC;EAC7D,CAAC;EAEDC,aAAaA,CAAA,EAAG;IACd;IACAH,QAAQ,CAACI,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACF,kBAAkB,CAAC;EAChE,CAAC;EAEDG,OAAO,EAAE;IACP;IACAC,YAAYA,CAAA,EAAG;MACb,MAAMC,IAAG,GAAI,IAAI,CAACC,MAAM,CAACD,IAAI;MAC7B,MAAME,WAAU,GAAI;QAClB,kBAAkB,EAAE,iBAAiB;QACrC,cAAc,EAAE,iBAAiB;QACjC,iBAAiB,EAAE,mBAAmB;QACtC,gBAAgB,EAAE,qBAAqB;QACvC,iBAAiB,EAAE,iBAAiB;QACpC,sBAAsB,EAAE,eAAe;QACvC,mBAAmB,EAAE,YAAY;QACjC,gBAAgB,EAAE;MACpB,CAAC;;MAED;MACA,OAAOA,WAAW,CAACF,IAAI,KAAK,IAAI,CAACC,MAAM,CAACE,IAAI,EAAEC,KAAI,IAAK,iBAAiB;IAC1E,CAAC;IAED;IACAC,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAACC,KAAK,CAAC,gBAAgB,CAAC;IAC9B,CAAC;IAID;IACAC,wBAAwBA,CAAA,EAAG;MACzB,IAAI,CAACD,KAAK,CAAC,sBAAsB,CAAC;IACpC,CAAC;IAED;IACAE,gBAAgBA,CAACC,MAAM,EAAEC,KAAK,EAAE;MAC9B;MACA,IAAIA,KAAK,EAAE;QACTA,KAAK,CAACC,cAAc,CAAC,CAAC;MACxB;;MAEA;MACA,MAAMC,MAAK,GAAI;QACb,SAAS,EAAE,gBAAgB;QAC3B,UAAU,EAAE,iBAAiB;QAC7B,SAAS,EAAE,gBAAe,CAAE;MAC9B,CAAC;MAED,MAAMC,KAAI,GAAID,MAAM,CAACH,MAAM,CAAC;MAC5B,IAAII,KAAI,IAAK,IAAI,CAACZ,MAAM,CAACD,IAAG,KAAMa,KAAK,EAAE;QACvC,IAAI,CAACC,OAAO,CAACC,IAAI,CAACF,KAAK,CAAC,CAACG,KAAK,CAACC,GAAE,IAAK;UACpC;UACA,IAAIA,GAAG,CAAClD,IAAG,KAAM,sBAAsB,EAAE;YACvCmD,OAAO,CAACC,KAAK,CAAC,mBAAmB,EAAEF,GAAG,CAAC;UACzC;QACF,CAAC,CAAC;MACJ;;MAEA;MACA,IAAI,IAAI,CAAC3C,gBAAgB,EAAE;QACzB,IAAI,CAACgC,KAAK,CAAC,sBAAsB,CAAC;MACpC;;MAEA;MACA,IAAI,CAACA,KAAK,CAAC,aAAa,EAAEG,MAAM,CAAC;IACnC,CAAC;IAED;IACAW,YAAYA,CAACV,KAAK,EAAE;MAClB;MACA,IAAIA,KAAK,EAAE;QACTA,KAAK,CAACC,cAAc,CAAC,CAAC;MACxB;MACA,IAAI,CAACL,KAAK,CAAC,QAAQ,CAAC;IACtB,CAAC;IAID;IACAX,kBAAkBA,CAACe,KAAK,EAAE;MACxB;MACA,IAAI,CAACA,KAAK,CAACW,MAAM,CAACC,OAAO,CAAC,gBAAgB,CAAC,EAAE;QAC3C,IAAI,IAAI,CAAChD,gBAAgB,EAAE;UACzB,IAAI,CAACgC,KAAK,CAAC,sBAAsB,CAAC;QACpC;MACF;IACF,CAAC;IAED;IACAiB,qBAAqBA,CAACC,YAAY,EAAE;MAClCN,OAAO,CAACO,GAAG,CAAC,4BAA4B,EAAED,YAAY,CAAC;MACvD;MACA,IAAI,CAAClB,KAAK,CAAC,kBAAkB,EAAEkB,YAAY,CAAC;IAC9C,CAAC;IAEDE,uBAAuBA,CAACF,YAAY,EAAE;MACpCN,OAAO,CAACO,GAAG,CAAC,uBAAuB,EAAED,YAAY,CAAC;MAClD;MACA,IAAIA,YAAY,CAACG,IAAG,IAAKH,YAAY,CAACG,IAAI,CAACC,UAAU,EAAE;QACrD;QACA,IAAI,CAACd,OAAO,CAACC,IAAI,CAAC,6BAA6BS,YAAY,CAACG,IAAI,CAACC,UAAU,EAAE,CAAC;MAChF;MACA,IAAI,CAACtB,KAAK,CAAC,oBAAoB,EAAEkB,YAAY,CAAC;IAChD,CAAC;IAEDK,uBAAuBA,CAACV,KAAK,EAAE;MAC7BD,OAAO,CAACC,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,IAAI,CAACb,KAAK,CAAC,OAAO,EAAEa,KAAK,CAAC;IAC5B;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}