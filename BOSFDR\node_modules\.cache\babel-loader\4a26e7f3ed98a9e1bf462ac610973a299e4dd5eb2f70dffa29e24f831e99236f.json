{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport notificationService from '../../services/notificationService';\nexport default {\n  name: 'ClientNotifications',\n  emits: ['new-notification', 'notification-click', 'notification-read', 'notifications-read', 'connected', 'error'],\n  data() {\n    return {\n      showPanel: false,\n      notifications: [],\n      unreadCount: 0,\n      loading: false,\n      loadingMore: false,\n      markingAllRead: false,\n      error: null,\n      currentPage: 1,\n      hasMore: true,\n      pageSize: 20\n    };\n  },\n  async mounted() {\n    await this.initializeNotifications();\n\n    // Close panel when clicking outside\n    document.addEventListener('click', this.handleOutsideClick);\n  },\n  beforeUnmount() {\n    // Clean up event listeners\n    document.removeEventListener('click', this.handleOutsideClick);\n\n    // Remove notification service listeners\n    notificationService.off('notification', this.handleNewNotification);\n    notificationService.off('connected', this.onConnected);\n    notificationService.off('error', this.onError);\n  },\n  methods: {\n    async initializeNotifications() {\n      try {\n        // Request notification permission\n        await notificationService.requestNotificationPermission();\n\n        // Initialize notification service for client\n        await notificationService.init('client');\n\n        // Set up event listeners\n        notificationService.on('notification', this.handleNewNotification);\n        notificationService.on('connected', this.onConnected);\n        notificationService.on('error', this.onError);\n\n        // Load initial data\n        await this.loadUnreadCount();\n      } catch (error) {\n        console.error('Failed to initialize client notifications:', error);\n        this.error = 'Failed to connect to notification service';\n      }\n    },\n    toggleNotificationPanel() {\n      this.showPanel = !this.showPanel;\n      if (this.showPanel && this.notifications.length === 0) {\n        this.loadNotifications(1);\n      }\n    },\n    handleOutsideClick(event) {\n      if (!event.target.closest('.client-notifications')) {\n        this.showPanel = false;\n      }\n    },\n    async loadNotifications(page = 1) {\n      try {\n        if (page === 1) {\n          this.loading = true;\n          this.error = null;\n        } else {\n          this.loadingMore = true;\n        }\n        const response = await notificationService.getNotifications(page, this.pageSize);\n        if (page === 1) {\n          this.notifications = response.data || [];\n        } else {\n          this.notifications.push(...(response.data || []));\n        }\n        this.currentPage = page;\n        this.hasMore = response.pagination ? response.pagination.hasMore : false;\n      } catch (error) {\n        console.error('Failed to load notifications:', error);\n        this.error = 'Failed to load notifications';\n      } finally {\n        this.loading = false;\n        this.loadingMore = false;\n      }\n    },\n    async loadMore() {\n      if (this.hasMore && !this.loadingMore) {\n        await this.loadNotifications(this.currentPage + 1);\n      }\n    },\n    async loadUnreadCount() {\n      try {\n        this.unreadCount = await notificationService.getUnreadCount();\n      } catch (error) {\n        console.error('Failed to load unread count:', error);\n      }\n    },\n    async markAllAsRead() {\n      try {\n        this.markingAllRead = true;\n        await notificationService.markAllAsRead();\n\n        // Update local state\n        this.notifications.forEach(notification => {\n          notification.is_read = true;\n        });\n        this.unreadCount = 0;\n        this.$emit('notifications-read');\n      } catch (error) {\n        console.error('Failed to mark all as read:', error);\n        this.$emit('error', 'Failed to mark notifications as read');\n      } finally {\n        this.markingAllRead = false;\n      }\n    },\n    async handleNotificationClick(notification) {\n      // Ensure we have a valid notification object with an ID\n      if (!notification || !notification.id) {\n        console.error('Invalid notification object:', notification);\n        return;\n      }\n      if (!notification.is_read) {\n        try {\n          await notificationService.markAsRead(notification.id);\n          notification.is_read = true;\n          this.unreadCount = Math.max(0, this.unreadCount - 1);\n          this.$emit('notification-read', notification);\n        } catch (error) {\n          console.error('Failed to mark notification as read:', error);\n        }\n      }\n\n      // Emit click event for parent components to handle\n      this.$emit('notification-click', notification);\n    },\n    handleNewNotification(notification) {\n      // Add to beginning of list if panel is open\n      if (this.showPanel) {\n        this.notifications.unshift(notification);\n      }\n\n      // Update unread count\n      if (!notification.is_read) {\n        this.unreadCount++;\n      }\n\n      // Emit event for parent components\n      this.$emit('new-notification', notification);\n    },\n    onConnected() {\n      console.log('Connected to client notification stream');\n      this.$emit('connected');\n    },\n    onError(error) {\n      console.error('Client notification stream error:', error);\n      this.$emit('error', 'Connection to notification stream failed');\n    },\n    getNotificationIcon(type) {\n      const icons = {\n        'status_change': 'fas fa-sync-alt text-info',\n        'payment_confirmed': 'fas fa-credit-card text-success',\n        'document_ready': 'fas fa-file-check text-success',\n        'request_update': 'fas fa-edit text-warning',\n        'system_alert': 'fas fa-exclamation-triangle text-danger',\n        'test': 'fas fa-vial text-secondary',\n        'connection': 'fas fa-plug text-success'\n      };\n      return icons[type] || 'fas fa-bell text-primary';\n    },\n    formatTime(timestamp) {\n      if (!timestamp) return '';\n      const date = new Date(timestamp);\n      const now = new Date();\n      const diffInMinutes = Math.floor((now - date) / (1000 * 60));\n      if (diffInMinutes < 1) return 'Just now';\n      if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n      const diffInHours = Math.floor(diffInMinutes / 60);\n      if (diffInHours < 24) return `${diffInHours}h ago`;\n      const diffInDays = Math.floor(diffInHours / 24);\n      if (diffInDays < 7) return `${diffInDays}d ago`;\n      return date.toLocaleDateString();\n    }\n  }\n};", "map": {"version": 3, "names": ["notificationService", "name", "emits", "data", "showPanel", "notifications", "unreadCount", "loading", "loadingMore", "markingAllRead", "error", "currentPage", "hasMore", "pageSize", "mounted", "initializeNotifications", "document", "addEventListener", "handleOutsideClick", "beforeUnmount", "removeEventListener", "off", "handleNewNotification", "onConnected", "onError", "methods", "requestNotificationPermission", "init", "on", "loadUnreadCount", "console", "toggleNotificationPanel", "length", "loadNotifications", "event", "target", "closest", "page", "response", "getNotifications", "push", "pagination", "loadMore", "getUnreadCount", "markAllAsRead", "for<PERSON>ach", "notification", "is_read", "$emit", "handleNotificationClick", "id", "mark<PERSON><PERSON><PERSON>", "Math", "max", "unshift", "log", "getNotificationIcon", "type", "icons", "formatTime", "timestamp", "date", "Date", "now", "diffInMinutes", "floor", "diffInHours", "diffInDays", "toLocaleDateString"], "sources": ["D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientNotifications.vue"], "sourcesContent": ["<template>\n  <div class=\"client-notifications\">\n    <!-- Notification Bell Icon -->\n    <div class=\"notification-bell\" @click=\"toggleNotificationPanel\">\n      <i class=\"fas fa-bell\"></i>\n      <span v-if=\"unreadCount > 0\" class=\"notification-badge\">{{ unreadCount > 99 ? '99+' : unreadCount }}</span>\n    </div>\n\n    <!-- Notification Panel -->\n    <div v-if=\"showPanel\" class=\"notification-panel\" @click.stop>\n      <div class=\"notification-header\">\n        <h5>Notifications</h5>\n        <div class=\"notification-actions\">\n          <button \n            v-if=\"unreadCount > 0\" \n            @click=\"markAllAsRead\" \n            class=\"btn btn-sm btn-outline-primary\"\n            :disabled=\"markingAllRead\"\n          >\n            <i class=\"fas fa-check-double\"></i>\n            {{ markingAllRead ? 'Marking...' : 'Mark All Read' }}\n          </button>\n          <button @click=\"toggleNotificationPanel\" class=\"btn btn-sm btn-outline-secondary\">\n            <i class=\"fas fa-times\"></i>\n          </button>\n        </div>\n      </div>\n\n      <div class=\"notification-content\">\n        <div v-if=\"loading\" class=\"notification-loading\">\n          <div class=\"spinner-border spinner-border-sm\" role=\"status\">\n            <span class=\"visually-hidden\">Loading...</span>\n          </div>\n          <span class=\"ms-2\">Loading notifications...</span>\n        </div>\n\n        <div v-else-if=\"error\" class=\"notification-error\">\n          <i class=\"fas fa-exclamation-triangle\"></i>\n          <span>{{ error }}</span>\n          <button @click=\"loadNotifications(1)\" class=\"btn btn-sm btn-outline-primary ms-2\">\n            <i class=\"fas fa-redo\"></i> Retry\n          </button>\n        </div>\n\n        <div v-else-if=\"notifications.length === 0\" class=\"no-notifications\">\n          <i class=\"fas fa-bell-slash\"></i>\n          <p>No notifications yet</p>\n        </div>\n\n        <div v-else class=\"notification-list\">\n          <div \n            v-for=\"notification in notifications\" \n            :key=\"notification.id\"\n            class=\"notification-item\"\n            :class=\"{ 'unread': !notification.is_read, 'priority-high': notification.priority === 'high' || notification.priority === 'urgent' }\"\n            @click=\"handleNotificationClick(notification)\"\n          >\n            <div class=\"notification-icon\">\n              <i :class=\"getNotificationIcon(notification.type)\"></i>\n            </div>\n            <div class=\"notification-content\">\n              <div class=\"notification-title\">{{ notification.title }}</div>\n              <div class=\"notification-message\">{{ notification.message }}</div>\n              <div class=\"notification-time\">{{ formatTime(notification.created_at) }}</div>\n            </div>\n            <div v-if=\"!notification.is_read\" class=\"unread-indicator\"></div>\n          </div>\n\n          <!-- Load More Button -->\n          <div v-if=\"hasMore\" class=\"load-more-container\">\n            <button \n              @click=\"loadMore\" \n              class=\"btn btn-sm btn-outline-primary\"\n              :disabled=\"loadingMore\"\n            >\n              <i v-if=\"loadingMore\" class=\"fas fa-spinner fa-spin\"></i>\n              <i v-else class=\"fas fa-chevron-down\"></i>\n              {{ loadingMore ? 'Loading...' : 'Load More' }}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport notificationService from '../../services/notificationService';\n\nexport default {\n  name: 'ClientNotifications',\n  \n  emits: [\n    'new-notification',\n    'notification-click', \n    'notification-read',\n    'notifications-read',\n    'connected',\n    'error'\n  ],\n\n  data() {\n    return {\n      showPanel: false,\n      notifications: [],\n      unreadCount: 0,\n      loading: false,\n      loadingMore: false,\n      markingAllRead: false,\n      error: null,\n      currentPage: 1,\n      hasMore: true,\n      pageSize: 20\n    };\n  },\n\n  async mounted() {\n    await this.initializeNotifications();\n    \n    // Close panel when clicking outside\n    document.addEventListener('click', this.handleOutsideClick);\n  },\n\n  beforeUnmount() {\n    // Clean up event listeners\n    document.removeEventListener('click', this.handleOutsideClick);\n    \n    // Remove notification service listeners\n    notificationService.off('notification', this.handleNewNotification);\n    notificationService.off('connected', this.onConnected);\n    notificationService.off('error', this.onError);\n  },\n\n  methods: {\n    async initializeNotifications() {\n      try {\n        // Request notification permission\n        await notificationService.requestNotificationPermission();\n\n        // Initialize notification service for client\n        await notificationService.init('client');\n\n        // Set up event listeners\n        notificationService.on('notification', this.handleNewNotification);\n        notificationService.on('connected', this.onConnected);\n        notificationService.on('error', this.onError);\n\n        // Load initial data\n        await this.loadUnreadCount();\n\n      } catch (error) {\n        console.error('Failed to initialize client notifications:', error);\n        this.error = 'Failed to connect to notification service';\n      }\n    },\n\n    toggleNotificationPanel() {\n      this.showPanel = !this.showPanel;\n      \n      if (this.showPanel && this.notifications.length === 0) {\n        this.loadNotifications(1);\n      }\n    },\n\n    handleOutsideClick(event) {\n      if (!event.target.closest('.client-notifications')) {\n        this.showPanel = false;\n      }\n    },\n\n    async loadNotifications(page = 1) {\n      try {\n        if (page === 1) {\n          this.loading = true;\n          this.error = null;\n        } else {\n          this.loadingMore = true;\n        }\n\n        const response = await notificationService.getNotifications(page, this.pageSize);\n        \n        if (page === 1) {\n          this.notifications = response.data || [];\n        } else {\n          this.notifications.push(...(response.data || []));\n        }\n        \n        this.currentPage = page;\n        this.hasMore = response.pagination ? response.pagination.hasMore : false;\n        \n      } catch (error) {\n        console.error('Failed to load notifications:', error);\n        this.error = 'Failed to load notifications';\n      } finally {\n        this.loading = false;\n        this.loadingMore = false;\n      }\n    },\n\n    async loadMore() {\n      if (this.hasMore && !this.loadingMore) {\n        await this.loadNotifications(this.currentPage + 1);\n      }\n    },\n\n    async loadUnreadCount() {\n      try {\n        this.unreadCount = await notificationService.getUnreadCount();\n      } catch (error) {\n        console.error('Failed to load unread count:', error);\n      }\n    },\n\n    async markAllAsRead() {\n      try {\n        this.markingAllRead = true;\n        await notificationService.markAllAsRead();\n        \n        // Update local state\n        this.notifications.forEach(notification => {\n          notification.is_read = true;\n        });\n        this.unreadCount = 0;\n        \n        this.$emit('notifications-read');\n        \n      } catch (error) {\n        console.error('Failed to mark all as read:', error);\n        this.$emit('error', 'Failed to mark notifications as read');\n      } finally {\n        this.markingAllRead = false;\n      }\n    },\n\n    async handleNotificationClick(notification) {\n      // Ensure we have a valid notification object with an ID\n      if (!notification || !notification.id) {\n        console.error('Invalid notification object:', notification);\n        return;\n      }\n\n      if (!notification.is_read) {\n        try {\n          await notificationService.markAsRead(notification.id);\n          notification.is_read = true;\n          this.unreadCount = Math.max(0, this.unreadCount - 1);\n          this.$emit('notification-read', notification);\n        } catch (error) {\n          console.error('Failed to mark notification as read:', error);\n        }\n      }\n\n      // Emit click event for parent components to handle\n      this.$emit('notification-click', notification);\n    },\n\n    handleNewNotification(notification) {\n      // Add to beginning of list if panel is open\n      if (this.showPanel) {\n        this.notifications.unshift(notification);\n      }\n      \n      // Update unread count\n      if (!notification.is_read) {\n        this.unreadCount++;\n      }\n      \n      // Emit event for parent components\n      this.$emit('new-notification', notification);\n    },\n\n    onConnected() {\n      console.log('Connected to client notification stream');\n      this.$emit('connected');\n    },\n\n    onError(error) {\n      console.error('Client notification stream error:', error);\n      this.$emit('error', 'Connection to notification stream failed');\n    },\n\n    getNotificationIcon(type) {\n      const icons = {\n        'status_change': 'fas fa-sync-alt text-info',\n        'payment_confirmed': 'fas fa-credit-card text-success',\n        'document_ready': 'fas fa-file-check text-success',\n        'request_update': 'fas fa-edit text-warning',\n        'system_alert': 'fas fa-exclamation-triangle text-danger',\n        'test': 'fas fa-vial text-secondary',\n        'connection': 'fas fa-plug text-success'\n      };\n      return icons[type] || 'fas fa-bell text-primary';\n    },\n\n    formatTime(timestamp) {\n      if (!timestamp) return '';\n      \n      const date = new Date(timestamp);\n      const now = new Date();\n      const diffInMinutes = Math.floor((now - date) / (1000 * 60));\n      \n      if (diffInMinutes < 1) return 'Just now';\n      if (diffInMinutes < 60) return `${diffInMinutes}m ago`;\n      \n      const diffInHours = Math.floor(diffInMinutes / 60);\n      if (diffInHours < 24) return `${diffInHours}h ago`;\n      \n      const diffInDays = Math.floor(diffInHours / 24);\n      if (diffInDays < 7) return `${diffInDays}d ago`;\n      \n      return date.toLocaleDateString();\n    }\n  }\n};\n</script>\n\n<style scoped>\n/* Client Notifications Styles */\n.client-notifications {\n  position: relative;\n  display: inline-block;\n}\n\n.notification-bell {\n  position: relative;\n  cursor: pointer;\n  padding: 0.5rem;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n  color: white;\n  font-size: 1.2rem;\n}\n\n.notification-bell:hover {\n  background: rgba(255, 255, 255, 0.1);\n  transform: scale(1.05);\n}\n\n.notification-badge {\n  position: absolute;\n  top: 0;\n  right: 0;\n  background: #dc3545;\n  color: white;\n  border-radius: 50%;\n  padding: 2px 6px;\n  font-size: 0.75rem;\n  font-weight: bold;\n  min-width: 18px;\n  text-align: center;\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% { transform: scale(1); }\n  50% { transform: scale(1.1); }\n  100% { transform: scale(1); }\n}\n\n.notification-panel {\n  position: absolute;\n  top: 100%;\n  right: 0;\n  width: 400px;\n  max-height: 500px;\n  background: white;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  z-index: 1050;\n  overflow: hidden;\n}\n\n.notification-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem;\n  border-bottom: 1px solid #dee2e6;\n  background: #f8f9fa;\n}\n\n.notification-header h5 {\n  margin: 0;\n  color: #1e3a8a;\n  font-weight: 600;\n}\n\n.notification-actions {\n  display: flex;\n  gap: 0.5rem;\n}\n\n.notification-content {\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.notification-loading,\n.notification-error {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 2rem;\n  color: #6c757d;\n}\n\n.notification-error {\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.no-notifications {\n  text-align: center;\n  padding: 2rem;\n  color: #6c757d;\n}\n\n.no-notifications i {\n  font-size: 2rem;\n  margin-bottom: 0.5rem;\n  opacity: 0.5;\n}\n\n.notification-list {\n  padding: 0;\n}\n\n.notification-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 1rem;\n  padding: 1rem;\n  border-bottom: 1px solid #f1f3f4;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n  position: relative;\n}\n\n.notification-item:hover {\n  background: #f8f9fa;\n}\n\n.notification-item.unread {\n  background: #f0f8ff;\n  border-left: 3px solid #007bff;\n}\n\n.notification-item.priority-high {\n  border-left: 3px solid #dc3545;\n}\n\n.notification-icon {\n  flex-shrink: 0;\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  background: #f8f9fa;\n}\n\n.notification-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.notification-title {\n  font-weight: 600;\n  color: #212529;\n  margin-bottom: 0.25rem;\n  line-height: 1.4;\n}\n\n.notification-message {\n  color: #6c757d;\n  font-size: 0.9rem;\n  line-height: 1.4;\n  margin-bottom: 0.5rem;\n}\n\n.notification-time {\n  color: #adb5bd;\n  font-size: 0.8rem;\n}\n\n.unread-indicator {\n  position: absolute;\n  top: 1rem;\n  right: 1rem;\n  width: 8px;\n  height: 8px;\n  background: #007bff;\n  border-radius: 50%;\n}\n\n.load-more-container {\n  padding: 1rem;\n  text-align: center;\n  border-top: 1px solid #dee2e6;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .notification-panel {\n    width: 320px;\n    max-width: 90vw;\n  }\n\n  .notification-item {\n    padding: 0.75rem;\n  }\n\n  .notification-actions {\n    flex-direction: column;\n    gap: 0.25rem;\n  }\n}\n\n/* Bootstrap utility classes */\n.btn {\n  display: inline-block;\n  font-weight: 400;\n  line-height: 1.5;\n  color: #212529;\n  text-align: center;\n  text-decoration: none;\n  vertical-align: middle;\n  cursor: pointer;\n  user-select: none;\n  background-color: transparent;\n  border: 1px solid transparent;\n  padding: 0.375rem 0.75rem;\n  font-size: 1rem;\n  border-radius: 0.375rem;\n  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\n\n.btn-sm {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.875rem;\n  border-radius: 0.25rem;\n}\n\n.btn-outline-primary {\n  color: #0d6efd;\n  border-color: #0d6efd;\n}\n\n.btn-outline-primary:hover {\n  color: #fff;\n  background-color: #0d6efd;\n  border-color: #0d6efd;\n}\n\n.btn-outline-secondary {\n  color: #6c757d;\n  border-color: #6c757d;\n}\n\n.btn-outline-secondary:hover {\n  color: #fff;\n  background-color: #6c757d;\n  border-color: #6c757d;\n}\n\n.btn:disabled {\n  pointer-events: none;\n  opacity: 0.65;\n}\n\n.spinner-border {\n  display: inline-block;\n  width: 2rem;\n  height: 2rem;\n  vertical-align: -0.125em;\n  border: 0.25em solid currentColor;\n  border-right-color: transparent;\n  border-radius: 50%;\n  animation: spinner-border 0.75s linear infinite;\n}\n\n.spinner-border-sm {\n  width: 1rem;\n  height: 1rem;\n  border-width: 0.2em;\n}\n\n@keyframes spinner-border {\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.visually-hidden {\n  position: absolute !important;\n  width: 1px !important;\n  height: 1px !important;\n  padding: 0 !important;\n  margin: -1px !important;\n  overflow: hidden !important;\n  clip: rect(0, 0, 0, 0) !important;\n  white-space: nowrap !important;\n  border: 0 !important;\n}\n\n.ms-2 {\n  margin-left: 0.5rem !important;\n}\n</style>\n"], "mappings": ";;;AAuFA,OAAOA,mBAAkB,MAAO,oCAAoC;AAEpE,eAAe;EACbC,IAAI,EAAE,qBAAqB;EAE3BC,KAAK,EAAE,CACL,kBAAkB,EAClB,oBAAoB,EACpB,mBAAmB,EACnB,oBAAoB,EACpB,WAAW,EACX,OAAM,CACP;EAEDC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAE,KAAK;MAChBC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,KAAK;MACdC,WAAW,EAAE,KAAK;MAClBC,cAAc,EAAE,KAAK;MACrBC,KAAK,EAAE,IAAI;MACXC,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE;IACZ,CAAC;EACH,CAAC;EAED,MAAMC,OAAOA,CAAA,EAAG;IACd,MAAM,IAAI,CAACC,uBAAuB,CAAC,CAAC;;IAEpC;IACAC,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACC,kBAAkB,CAAC;EAC7D,CAAC;EAEDC,aAAaA,CAAA,EAAG;IACd;IACAH,QAAQ,CAACI,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACF,kBAAkB,CAAC;;IAE9D;IACAlB,mBAAmB,CAACqB,GAAG,CAAC,cAAc,EAAE,IAAI,CAACC,qBAAqB,CAAC;IACnEtB,mBAAmB,CAACqB,GAAG,CAAC,WAAW,EAAE,IAAI,CAACE,WAAW,CAAC;IACtDvB,mBAAmB,CAACqB,GAAG,CAAC,OAAO,EAAE,IAAI,CAACG,OAAO,CAAC;EAChD,CAAC;EAEDC,OAAO,EAAE;IACP,MAAMV,uBAAuBA,CAAA,EAAG;MAC9B,IAAI;QACF;QACA,MAAMf,mBAAmB,CAAC0B,6BAA6B,CAAC,CAAC;;QAEzD;QACA,MAAM1B,mBAAmB,CAAC2B,IAAI,CAAC,QAAQ,CAAC;;QAExC;QACA3B,mBAAmB,CAAC4B,EAAE,CAAC,cAAc,EAAE,IAAI,CAACN,qBAAqB,CAAC;QAClEtB,mBAAmB,CAAC4B,EAAE,CAAC,WAAW,EAAE,IAAI,CAACL,WAAW,CAAC;QACrDvB,mBAAmB,CAAC4B,EAAE,CAAC,OAAO,EAAE,IAAI,CAACJ,OAAO,CAAC;;QAE7C;QACA,MAAM,IAAI,CAACK,eAAe,CAAC,CAAC;MAE9B,EAAE,OAAOnB,KAAK,EAAE;QACdoB,OAAO,CAACpB,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAClE,IAAI,CAACA,KAAI,GAAI,2CAA2C;MAC1D;IACF,CAAC;IAEDqB,uBAAuBA,CAAA,EAAG;MACxB,IAAI,CAAC3B,SAAQ,GAAI,CAAC,IAAI,CAACA,SAAS;MAEhC,IAAI,IAAI,CAACA,SAAQ,IAAK,IAAI,CAACC,aAAa,CAAC2B,MAAK,KAAM,CAAC,EAAE;QACrD,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAAC;MAC3B;IACF,CAAC;IAEDf,kBAAkBA,CAACgB,KAAK,EAAE;MACxB,IAAI,CAACA,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,uBAAuB,CAAC,EAAE;QAClD,IAAI,CAAChC,SAAQ,GAAI,KAAK;MACxB;IACF,CAAC;IAED,MAAM6B,iBAAiBA,CAACI,IAAG,GAAI,CAAC,EAAE;MAChC,IAAI;QACF,IAAIA,IAAG,KAAM,CAAC,EAAE;UACd,IAAI,CAAC9B,OAAM,GAAI,IAAI;UACnB,IAAI,CAACG,KAAI,GAAI,IAAI;QACnB,OAAO;UACL,IAAI,CAACF,WAAU,GAAI,IAAI;QACzB;QAEA,MAAM8B,QAAO,GAAI,MAAMtC,mBAAmB,CAACuC,gBAAgB,CAACF,IAAI,EAAE,IAAI,CAACxB,QAAQ,CAAC;QAEhF,IAAIwB,IAAG,KAAM,CAAC,EAAE;UACd,IAAI,CAAChC,aAAY,GAAIiC,QAAQ,CAACnC,IAAG,IAAK,EAAE;QAC1C,OAAO;UACL,IAAI,CAACE,aAAa,CAACmC,IAAI,CAAC,IAAIF,QAAQ,CAACnC,IAAG,IAAK,EAAE,CAAC,CAAC;QACnD;QAEA,IAAI,CAACQ,WAAU,GAAI0B,IAAI;QACvB,IAAI,CAACzB,OAAM,GAAI0B,QAAQ,CAACG,UAAS,GAAIH,QAAQ,CAACG,UAAU,CAAC7B,OAAM,GAAI,KAAK;MAE1E,EAAE,OAAOF,KAAK,EAAE;QACdoB,OAAO,CAACpB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAACA,KAAI,GAAI,8BAA8B;MAC7C,UAAU;QACR,IAAI,CAACH,OAAM,GAAI,KAAK;QACpB,IAAI,CAACC,WAAU,GAAI,KAAK;MAC1B;IACF,CAAC;IAED,MAAMkC,QAAQA,CAAA,EAAG;MACf,IAAI,IAAI,CAAC9B,OAAM,IAAK,CAAC,IAAI,CAACJ,WAAW,EAAE;QACrC,MAAM,IAAI,CAACyB,iBAAiB,CAAC,IAAI,CAACtB,WAAU,GAAI,CAAC,CAAC;MACpD;IACF,CAAC;IAED,MAAMkB,eAAeA,CAAA,EAAG;MACtB,IAAI;QACF,IAAI,CAACvB,WAAU,GAAI,MAAMN,mBAAmB,CAAC2C,cAAc,CAAC,CAAC;MAC/D,EAAE,OAAOjC,KAAK,EAAE;QACdoB,OAAO,CAACpB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;IAED,MAAMkC,aAAaA,CAAA,EAAG;MACpB,IAAI;QACF,IAAI,CAACnC,cAAa,GAAI,IAAI;QAC1B,MAAMT,mBAAmB,CAAC4C,aAAa,CAAC,CAAC;;QAEzC;QACA,IAAI,CAACvC,aAAa,CAACwC,OAAO,CAACC,YAAW,IAAK;UACzCA,YAAY,CAACC,OAAM,GAAI,IAAI;QAC7B,CAAC,CAAC;QACF,IAAI,CAACzC,WAAU,GAAI,CAAC;QAEpB,IAAI,CAAC0C,KAAK,CAAC,oBAAoB,CAAC;MAElC,EAAE,OAAOtC,KAAK,EAAE;QACdoB,OAAO,CAACpB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAACsC,KAAK,CAAC,OAAO,EAAE,sCAAsC,CAAC;MAC7D,UAAU;QACR,IAAI,CAACvC,cAAa,GAAI,KAAK;MAC7B;IACF,CAAC;IAED,MAAMwC,uBAAuBA,CAACH,YAAY,EAAE;MAC1C;MACA,IAAI,CAACA,YAAW,IAAK,CAACA,YAAY,CAACI,EAAE,EAAE;QACrCpB,OAAO,CAACpB,KAAK,CAAC,8BAA8B,EAAEoC,YAAY,CAAC;QAC3D;MACF;MAEA,IAAI,CAACA,YAAY,CAACC,OAAO,EAAE;QACzB,IAAI;UACF,MAAM/C,mBAAmB,CAACmD,UAAU,CAACL,YAAY,CAACI,EAAE,CAAC;UACrDJ,YAAY,CAACC,OAAM,GAAI,IAAI;UAC3B,IAAI,CAACzC,WAAU,GAAI8C,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC/C,WAAU,GAAI,CAAC,CAAC;UACpD,IAAI,CAAC0C,KAAK,CAAC,mBAAmB,EAAEF,YAAY,CAAC;QAC/C,EAAE,OAAOpC,KAAK,EAAE;UACdoB,OAAO,CAACpB,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;QAC9D;MACF;;MAEA;MACA,IAAI,CAACsC,KAAK,CAAC,oBAAoB,EAAEF,YAAY,CAAC;IAChD,CAAC;IAEDxB,qBAAqBA,CAACwB,YAAY,EAAE;MAClC;MACA,IAAI,IAAI,CAAC1C,SAAS,EAAE;QAClB,IAAI,CAACC,aAAa,CAACiD,OAAO,CAACR,YAAY,CAAC;MAC1C;;MAEA;MACA,IAAI,CAACA,YAAY,CAACC,OAAO,EAAE;QACzB,IAAI,CAACzC,WAAW,EAAE;MACpB;;MAEA;MACA,IAAI,CAAC0C,KAAK,CAAC,kBAAkB,EAAEF,YAAY,CAAC;IAC9C,CAAC;IAEDvB,WAAWA,CAAA,EAAG;MACZO,OAAO,CAACyB,GAAG,CAAC,yCAAyC,CAAC;MACtD,IAAI,CAACP,KAAK,CAAC,WAAW,CAAC;IACzB,CAAC;IAEDxB,OAAOA,CAACd,KAAK,EAAE;MACboB,OAAO,CAACpB,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,IAAI,CAACsC,KAAK,CAAC,OAAO,EAAE,0CAA0C,CAAC;IACjE,CAAC;IAEDQ,mBAAmBA,CAACC,IAAI,EAAE;MACxB,MAAMC,KAAI,GAAI;QACZ,eAAe,EAAE,2BAA2B;QAC5C,mBAAmB,EAAE,iCAAiC;QACtD,gBAAgB,EAAE,gCAAgC;QAClD,gBAAgB,EAAE,0BAA0B;QAC5C,cAAc,EAAE,yCAAyC;QACzD,MAAM,EAAE,4BAA4B;QACpC,YAAY,EAAE;MAChB,CAAC;MACD,OAAOA,KAAK,CAACD,IAAI,KAAK,0BAA0B;IAClD,CAAC;IAEDE,UAAUA,CAACC,SAAS,EAAE;MACpB,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;MAEzB,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,SAAS,CAAC;MAChC,MAAMG,GAAE,GAAI,IAAID,IAAI,CAAC,CAAC;MACtB,MAAME,aAAY,GAAIZ,IAAI,CAACa,KAAK,CAAC,CAACF,GAAE,GAAIF,IAAI,KAAK,IAAG,GAAI,EAAE,CAAC,CAAC;MAE5D,IAAIG,aAAY,GAAI,CAAC,EAAE,OAAO,UAAU;MACxC,IAAIA,aAAY,GAAI,EAAE,EAAE,OAAO,GAAGA,aAAa,OAAO;MAEtD,MAAME,WAAU,GAAId,IAAI,CAACa,KAAK,CAACD,aAAY,GAAI,EAAE,CAAC;MAClD,IAAIE,WAAU,GAAI,EAAE,EAAE,OAAO,GAAGA,WAAW,OAAO;MAElD,MAAMC,UAAS,GAAIf,IAAI,CAACa,KAAK,CAACC,WAAU,GAAI,EAAE,CAAC;MAC/C,IAAIC,UAAS,GAAI,CAAC,EAAE,OAAO,GAAGA,UAAU,OAAO;MAE/C,OAAON,IAAI,CAACO,kBAAkB,CAAC,CAAC;IAClC;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}