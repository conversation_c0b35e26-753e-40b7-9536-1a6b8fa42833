const { executeQuery, executeTransaction } = require('../config/database');
const logger = require('../utils/logger');

class NotificationService {
  constructor() {
    // Store active SSE connections
    this.connections = new Map(); // userId -> Set of response objects
    this.adminConnections = new Set(); // Set of admin response objects
  }

  /**
   * Add SSE connection for a user
   */
  addConnection(userId, userType, res) {
    const connectionId = `${userType}_${userId}_${Date.now()}`;

    // Track connection state to prevent duplicate removals
    const connectionState = { removed: false };

    if (userType === 'admin') {
      this.adminConnections.add({ id: connectionId, userId, res, state: connectionState });
    } else {
      if (!this.connections.has(userId)) {
        this.connections.set(userId, new Set());
      }
      this.connections.get(userId).add({ id: connectionId, res, state: connectionState });
    }

    logger.info(`SSE connection added: ${connectionId} for ${userType} user ${userId}`);
    return { connectionId, state: connectionState };
  }

  /**
   * Remove SSE connection
   */
  removeConnection(userId, userType, connectionId, connectionState = null) {
    // Prevent duplicate removals
    if (connectionState && connectionState.removed) {
      return false; // Already removed
    }

    let removed = false;

    if (userType === 'admin') {
      this.adminConnections.forEach(conn => {
        if (conn.id === connectionId) {
          this.adminConnections.delete(conn);
          removed = true;
        }
      });
    } else {
      const userConnections = this.connections.get(userId);
      if (userConnections) {
        userConnections.forEach(conn => {
          if (conn.id === connectionId) {
            userConnections.delete(conn);
            removed = true;
          }
        });
        if (userConnections.size === 0) {
          this.connections.delete(userId);
        }
      }
    }

    if (removed) {
      if (connectionState) {
        connectionState.removed = true;
      }
      logger.info(`SSE connection removed: ${connectionId}`);
    }

    return removed;
  }

  /**
   * Send notification to specific user
   */
  sendToUser(userId, notification) {
    const userConnections = this.connections.get(userId);
    if (userConnections) {
      const deadConnections = [];

      userConnections.forEach(conn => {
        // Skip already removed connections
        if (conn.state && conn.state.removed) {
          deadConnections.push(conn);
          return;
        }

        try {
          const data = JSON.stringify(notification);
          conn.res.write(`id: ${Date.now()}\n`);
          conn.res.write(`data: ${data}\n\n`);

          // CRITICAL FIX: Force immediate flush to prevent client_aborted
          if (conn.res.flush) conn.res.flush();
        } catch (error) {
          logger.error(`Failed to send notification to user ${userId}:`, error);
          deadConnections.push(conn);
        }
      });

      // Clean up dead connections
      deadConnections.forEach(conn => {
        userConnections.delete(conn);
      });
    }
  }

  /**
   * Send notification to all admin users
   */
  sendToAdmins(notification) {
    const deadConnections = [];

    this.adminConnections.forEach(conn => {
      // Skip already removed connections
      if (conn.state && conn.state.removed) {
        deadConnections.push(conn);
        return;
      }

      try {
        const data = JSON.stringify(notification);
        conn.res.write(`id: ${Date.now()}\n`);
        conn.res.write(`data: ${data}\n\n`);

        // CRITICAL FIX: Force immediate flush to prevent client_aborted
        if (conn.res.flush) conn.res.flush();
      } catch (error) {
        logger.error(`Failed to send notification to admin ${conn.userId}:`, error);
        deadConnections.push(conn);
      }
    });

    // Clean up dead connections
    deadConnections.forEach(conn => {
      this.adminConnections.delete(conn);
    });
  }

  /**
   * Send notification to all connected users
   */
  broadcast(notification) {
    // Send to all clients
    this.connections.forEach((userConnections, userId) => {
      this.sendToUser(userId, notification);
    });

    // Send to all admins
    this.sendToAdmins(notification);
  }

  /**
   * Create and store notification in database
   */
  async createNotification(data) {
    try {
      const {
        user_id,
        user_type,
        type,
        title,
        message,
        data: notificationData = null,
        priority = 'normal'
      } = data;

      const query = `
        INSERT INTO notifications (
          user_id, user_type, type, title, message, data, priority, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
      `;

      const result = await executeQuery(query, [
        user_id,
        user_type,
        type,
        title,
        message,
        notificationData ? JSON.stringify(notificationData) : null,
        priority
      ]);

      const notification = {
        id: result.insertId,
        user_id,
        user_type,
        type,
        title,
        message,
        data: notificationData,
        priority,
        is_read: false,
        created_at: new Date().toISOString()
      };

      return notification;
    } catch (error) {
      logger.error('Failed to create notification:', error);
      throw error;
    }
  }

  /**
   * Send document request status change notification
   */
  async notifyStatusChange(requestId, oldStatusId, newStatusId, changedBy) {
    try {
      // Get request details
      const requestQuery = `
        SELECT dr.*, c.first_name, c.last_name, c.email,
               dt.type_name, rs_old.status_name as old_status,
               rs_new.status_name as new_status
        FROM document_requests dr
        JOIN client_profiles c ON dr.client_id = c.account_id
        JOIN document_types dt ON dr.document_type_id = dt.id
        JOIN request_statuses rs_old ON ? = rs_old.id
        JOIN request_statuses rs_new ON dr.status_id = rs_new.id
        WHERE dr.id = ?
      `;

      const requestResult = await executeQuery(requestQuery, [oldStatusId, requestId]);
      if (requestResult.length === 0) return;

      const request = requestResult[0];
      const clientName = `${request.first_name} ${request.last_name}`;

      // Notification for client
      const clientNotification = await this.createNotification({
        user_id: request.client_id,
        user_type: 'client',
        type: 'status_change',
        title: 'Request Status Updated',
        message: `Your ${request.type_name} request (${request.request_number}) status has been updated to "${request.new_status}"`,
        data: {
          request_id: requestId,
          request_number: request.request_number,
          document_type: request.type_name,
          old_status: request.old_status,
          new_status: request.new_status
        },
        priority: newStatusId === 5 ? 'high' : 'normal' // High priority for rejections
      });

      // Send real-time notification to client
      this.sendToUser(request.client_id, clientNotification);

      // Notification for admins (except the one who made the change)
      const adminNotification = await this.createNotification({
        user_id: null, // Broadcast to all admins
        user_type: 'admin',
        type: 'request_update',
        title: 'Request Status Updated',
        message: `${clientName}'s ${request.type_name} request (${request.request_number}) was updated to "${request.new_status}"`,
        data: {
          request_id: requestId,
          request_number: request.request_number,
          document_type: request.type_name,
          client_name: clientName,
          old_status: request.old_status,
          new_status: request.new_status,
          changed_by: changedBy
        },
        priority: 'normal'
      });

      // Send real-time notification to all admins
      this.sendToAdmins(adminNotification);

      logger.info(`Status change notifications sent for request ${requestId}`);
    } catch (error) {
      logger.error('Failed to send status change notification:', error);
    }
  }

  /**
   * Send new request notification to admins
   */
  async notifyNewRequest(requestId) {
    try {
      // Get request details
      const requestQuery = `
        SELECT dr.*, c.first_name, c.last_name, dt.type_name
        FROM document_requests dr
        JOIN client_profiles c ON dr.client_id = c.account_id
        JOIN document_types dt ON dr.document_type_id = dt.id
        WHERE dr.id = ?
      `;

      const requestResult = await executeQuery(requestQuery, [requestId]);
      if (requestResult.length === 0) return;

      const request = requestResult[0];
      const clientName = `${request.first_name} ${request.last_name}`;

      const notification = await this.createNotification({
        user_id: null, // Broadcast to all admins
        user_type: 'admin',
        type: 'new_request',
        title: 'New Document Request',
        message: `${clientName} submitted a new ${request.type_name} request (${request.request_number})`,
        data: {
          request_id: requestId,
          request_number: request.request_number,
          document_type: request.type_name,
          client_name: clientName,
          priority: request.priority
        },
        priority: request.priority === 'urgent' ? 'high' : 'normal'
      });

      // Send real-time notification to all admins
      this.sendToAdmins(notification);

      logger.info(`New request notification sent for request ${requestId}`);
    } catch (error) {
      logger.error('Failed to send new request notification:', error);
    }
  }

  /**
   * Get user notifications with pagination
   */
  async getUserNotifications(userId, userType, page = 1, limit = 20, unreadOnly = false) {
    try {
      const offset = (page - 1) * limit;
      const unreadFilter = unreadOnly ? 'AND is_read = FALSE' : '';
      
      const query = `
        SELECT * FROM notifications
        WHERE (user_id = ? OR user_id IS NULL) AND user_type = ?
        ${unreadFilter}
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
      `;

      const notifications = await executeQuery(query, [userId, userType, limit, offset]);

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total FROM notifications
        WHERE (user_id = ? OR user_id IS NULL) AND user_type = ?
        ${unreadFilter}
      `;
      
      const countResult = await executeQuery(countQuery, [userId, userType]);
      const total = countResult[0].total;

      return {
        notifications,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Failed to get user notifications:', error);
      throw error;
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId, userId, userType) {
    try {
      const query = `
        UPDATE notifications 
        SET is_read = TRUE, read_at = NOW()
        WHERE id = ? AND (user_id = ? OR user_id IS NULL) AND user_type = ?
      `;

      await executeQuery(query, [notificationId, userId, userType]);
      logger.info(`Notification ${notificationId} marked as read by ${userType} ${userId}`);
    } catch (error) {
      logger.error('Failed to mark notification as read:', error);
      throw error;
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllAsRead(userId, userType) {
    try {
      const query = `
        UPDATE notifications 
        SET is_read = TRUE, read_at = NOW()
        WHERE (user_id = ? OR user_id IS NULL) AND user_type = ? AND is_read = FALSE
      `;

      const result = await executeQuery(query, [userId, userType]);
      logger.info(`${result.affectedRows} notifications marked as read for ${userType} ${userId}`);
      return result.affectedRows;
    } catch (error) {
      logger.error('Failed to mark all notifications as read:', error);
      throw error;
    }
  }

  /**
   * Get unread notification count
   */
  async getUnreadCount(userId, userType) {
    try {
      const query = `
        SELECT COUNT(*) as count FROM notifications
        WHERE (user_id = ? OR user_id IS NULL) AND user_type = ? AND is_read = FALSE
      `;

      const result = await executeQuery(query, [userId, userType]);
      return result[0].count;
    } catch (error) {
      logger.error('Failed to get unread notification count:', error);
      throw error;
    }
  }
}

// Create singleton instance
const notificationService = new NotificationService();

module.exports = notificationService;
