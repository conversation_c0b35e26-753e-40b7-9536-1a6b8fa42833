{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport ClientNotifications from './ClientNotifications.vue';\nexport default {\n  name: 'ClientHeader',\n  components: {\n    ClientNotifications\n  },\n  props: {\n    userName: {\n      type: String,\n      default: 'User'\n    },\n    showUserDropdown: {\n      type: Boolean,\n      default: false\n    },\n    sidebarCollapsed: {\n      type: Boolean,\n      default: false\n    },\n    activeMenu: {\n      type: String,\n      default: 'dashboard'\n    }\n  },\n  emits: ['sidebar-toggle', 'user-dropdown-toggle', 'menu-action', 'logout', 'error'],\n  mounted() {\n    // Setup event listeners for outside clicks\n    document.addEventListener('click', this.handleOutsideClick);\n  },\n  beforeUnmount() {\n    // Clean up event listeners\n    document.removeEventListener('click', this.handleOutsideClick);\n  },\n  methods: {\n    // Get page title based on active menu\n    getPageTitle() {\n      const titles = {\n        'dashboard': 'Dashboard',\n        'services': 'Services',\n        'requests': 'My Requests',\n        'documents': 'Documents',\n        'profile': 'Profile',\n        'notifications': 'Notifications',\n        'help': 'Help & Support'\n      };\n      return titles[this.activeMenu] || 'Dashboard';\n    },\n    // Handle sidebar toggle\n    handleSidebarToggle() {\n      this.$emit('sidebar-toggle');\n    },\n    // Handle user dropdown toggle\n    handleUserDropdownToggle() {\n      this.$emit('user-dropdown-toggle');\n    },\n    // Handle menu actions (profile, settings, etc.)\n    handleMenuAction(action) {\n      this.$emit('menu-action', action);\n    },\n    // Handle logout\n    handleLogout() {\n      this.$emit('logout');\n    },\n    // Handle outside clicks to close dropdowns\n    handleOutsideClick(event) {\n      // Check if click is outside user dropdown\n      if (!event.target.closest('.user-dropdown')) {\n        if (this.showUserDropdown) {\n          this.$emit('user-dropdown-toggle');\n        }\n      }\n    },\n    // Notification event handlers\n    handleNewNotification(notification) {\n      console.log('New notification received:', notification);\n      // Handle new notification - could show toast, update UI, etc.\n    },\n    handleNotificationClick(notification) {\n      console.log('Notification clicked:', notification);\n\n      // Ensure we have a valid notification object\n      if (!notification || typeof notification !== 'object') {\n        console.error('Invalid notification object received:', notification);\n        return;\n      }\n\n      // Handle notification click - could navigate to relevant page\n      if (notification.data && notification.data.request_id) {\n        // Navigate to request details if it's a request-related notification\n        this.$router.push(`/client/requests?highlight=${notification.data.request_id}`);\n      }\n    },\n    handleNotificationError(error) {\n      console.error('Notification error:', error);\n      this.$emit('error', error);\n    }\n  }\n};", "map": {"version": 3, "names": ["ClientNotifications", "name", "components", "props", "userName", "type", "String", "default", "showUserDropdown", "Boolean", "sidebarCollapsed", "activeMenu", "emits", "mounted", "document", "addEventListener", "handleOutsideClick", "beforeUnmount", "removeEventListener", "methods", "getPageTitle", "titles", "handleSidebarToggle", "$emit", "handleUserDropdownToggle", "handleMenuAction", "action", "handleLogout", "event", "target", "closest", "handleNewNotification", "notification", "console", "log", "handleNotificationClick", "error", "data", "request_id", "$router", "push", "handleNotificationError"], "sources": ["D:\\rhai_front_and_back\\BOSFDR\\src\\components\\client\\ClientHeader.vue"], "sourcesContent": ["<template>\n  <header class=\"dashboard-header\" :class=\"{ 'sidebar-collapsed': sidebarCollapsed }\">\n    <div class=\"header-content\">\n      <!-- Left Section -->\n      <div class=\"header-left\">\n        <button class=\"sidebar-toggle\" @click=\"handleSidebarToggle\">\n          <i class=\"fas fa-bars\"></i>\n        </button>\n        <div class=\"page-title\">\n          <h1>{{ getPageTitle() }}</h1>\n        </div>\n      </div>\n\n      <!-- Header Actions -->\n      <div class=\"header-actions\">\n        <!-- Notifications -->\n        <ClientNotifications\n          @new-notification=\"handleNewNotification\"\n          @notification-click=\"handleNotificationClick\"\n          @error=\"handleNotificationError\"\n        />\n\n        <!-- User Profile -->\n        <div class=\"user-dropdown\" :class=\"{ active: showUserDropdown }\">\n          <button class=\"user-btn\" @click=\"handleUserDropdownToggle\">\n            <div class=\"user-avatar\">\n              <i class=\"fas fa-user-circle\"></i>\n            </div>\n            <div class=\"user-info\">\n              <span class=\"user-name\">{{ userName }}</span>\n              <span class=\"user-role\">Client</span>\n            </div>\n            <i class=\"fas fa-chevron-down dropdown-arrow\"></i>\n          </button>\n          \n          <div v-if=\"showUserDropdown\" class=\"dropdown-menu\">\n            <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('profile')\">\n              <i class=\"fas fa-user me-2\"></i>\n              My Profile\n            </a>\n            <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('settings')\">\n              <i class=\"fas fa-cog me-2\"></i>\n              Settings\n            </a>\n            <a href=\"#\" class=\"dropdown-item\" @click=\"handleMenuAction('account')\">\n              <i class=\"fas fa-id-card me-2\"></i>\n              Account Info\n            </a>\n            <div class=\"dropdown-divider\"></div>\n            <a href=\"#\" class=\"dropdown-item\" @click=\"handleLogout\">\n              <i class=\"fas fa-sign-out-alt me-2\"></i>\n              Logout\n            </a>\n          </div>\n        </div>\n      </div>\n    </div>\n  </header>\n</template>\n\n<script>\nimport ClientNotifications from './ClientNotifications.vue';\n\nexport default {\n  name: 'ClientHeader',\n  components: {\n    ClientNotifications\n  },\n  props: {\n    userName: {\n      type: String,\n      default: 'User'\n    },\n    showUserDropdown: {\n      type: Boolean,\n      default: false\n    },\n    sidebarCollapsed: {\n      type: Boolean,\n      default: false\n    },\n    activeMenu: {\n      type: String,\n      default: 'dashboard'\n    }\n  },\n\n  emits: [\n    'sidebar-toggle',\n    'user-dropdown-toggle',\n    'menu-action',\n    'logout',\n    'error'\n  ],\n\n  mounted() {\n    // Setup event listeners for outside clicks\n    document.addEventListener('click', this.handleOutsideClick);\n  },\n\n  beforeUnmount() {\n    // Clean up event listeners\n    document.removeEventListener('click', this.handleOutsideClick);\n  },\n\n  methods: {\n    // Get page title based on active menu\n    getPageTitle() {\n      const titles = {\n        'dashboard': 'Dashboard',\n        'services': 'Services',\n        'requests': 'My Requests',\n        'documents': 'Documents',\n        'profile': 'Profile',\n        'notifications': 'Notifications',\n        'help': 'Help & Support'\n      };\n      return titles[this.activeMenu] || 'Dashboard';\n    },\n\n    // Handle sidebar toggle\n    handleSidebarToggle() {\n      this.$emit('sidebar-toggle');\n    },\n\n    // Handle user dropdown toggle\n    handleUserDropdownToggle() {\n      this.$emit('user-dropdown-toggle');\n    },\n\n    // Handle menu actions (profile, settings, etc.)\n    handleMenuAction(action) {\n      this.$emit('menu-action', action);\n    },\n\n    // Handle logout\n    handleLogout() {\n      this.$emit('logout');\n    },\n\n    // Handle outside clicks to close dropdowns\n    handleOutsideClick(event) {\n      // Check if click is outside user dropdown\n      if (!event.target.closest('.user-dropdown')) {\n        if (this.showUserDropdown) {\n          this.$emit('user-dropdown-toggle');\n        }\n      }\n    },\n\n    // Notification event handlers\n    handleNewNotification(notification) {\n      console.log('New notification received:', notification);\n      // Handle new notification - could show toast, update UI, etc.\n    },\n\n    handleNotificationClick(notification) {\n      console.log('Notification clicked:', notification);\n\n      // Ensure we have a valid notification object\n      if (!notification || typeof notification !== 'object') {\n        console.error('Invalid notification object received:', notification);\n        return;\n      }\n\n      // Handle notification click - could navigate to relevant page\n      if (notification.data && notification.data.request_id) {\n        // Navigate to request details if it's a request-related notification\n        this.$router.push(`/client/requests?highlight=${notification.data.request_id}`);\n      }\n    },\n\n    handleNotificationError(error) {\n      console.error('Notification error:', error);\n      this.$emit('error', error);\n    }\n  }\n};\n</script>\n\n<style scoped src=\"./css/clientHeader.css\"></style>\n"], "mappings": ";AA6DA,OAAOA,mBAAkB,MAAO,2BAA2B;AAE3D,eAAe;EACbC,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,KAAK,EAAE;IACLC,QAAQ,EAAE;MACRC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX,CAAC;IACDC,gBAAgB,EAAE;MAChBH,IAAI,EAAEI,OAAO;MACbF,OAAO,EAAE;IACX,CAAC;IACDG,gBAAgB,EAAE;MAChBL,IAAI,EAAEI,OAAO;MACbF,OAAO,EAAE;IACX,CAAC;IACDI,UAAU,EAAE;MACVN,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX;EACF,CAAC;EAEDK,KAAK,EAAE,CACL,gBAAgB,EAChB,sBAAsB,EACtB,aAAa,EACb,QAAQ,EACR,OAAM,CACP;EAEDC,OAAOA,CAAA,EAAG;IACR;IACAC,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACC,kBAAkB,CAAC;EAC7D,CAAC;EAEDC,aAAaA,CAAA,EAAG;IACd;IACAH,QAAQ,CAACI,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACF,kBAAkB,CAAC;EAChE,CAAC;EAEDG,OAAO,EAAE;IACP;IACAC,YAAYA,CAAA,EAAG;MACb,MAAMC,MAAK,GAAI;QACb,WAAW,EAAE,WAAW;QACxB,UAAU,EAAE,UAAU;QACtB,UAAU,EAAE,aAAa;QACzB,WAAW,EAAE,WAAW;QACxB,SAAS,EAAE,SAAS;QACpB,eAAe,EAAE,eAAe;QAChC,MAAM,EAAE;MACV,CAAC;MACD,OAAOA,MAAM,CAAC,IAAI,CAACV,UAAU,KAAK,WAAW;IAC/C,CAAC;IAED;IACAW,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAACC,KAAK,CAAC,gBAAgB,CAAC;IAC9B,CAAC;IAED;IACAC,wBAAwBA,CAAA,EAAG;MACzB,IAAI,CAACD,KAAK,CAAC,sBAAsB,CAAC;IACpC,CAAC;IAED;IACAE,gBAAgBA,CAACC,MAAM,EAAE;MACvB,IAAI,CAACH,KAAK,CAAC,aAAa,EAAEG,MAAM,CAAC;IACnC,CAAC;IAED;IACAC,YAAYA,CAAA,EAAG;MACb,IAAI,CAACJ,KAAK,CAAC,QAAQ,CAAC;IACtB,CAAC;IAED;IACAP,kBAAkBA,CAACY,KAAK,EAAE;MACxB;MACA,IAAI,CAACA,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC,gBAAgB,CAAC,EAAE;QAC3C,IAAI,IAAI,CAACtB,gBAAgB,EAAE;UACzB,IAAI,CAACe,KAAK,CAAC,sBAAsB,CAAC;QACpC;MACF;IACF,CAAC;IAED;IACAQ,qBAAqBA,CAACC,YAAY,EAAE;MAClCC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEF,YAAY,CAAC;MACvD;IACF,CAAC;IAEDG,uBAAuBA,CAACH,YAAY,EAAE;MACpCC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEF,YAAY,CAAC;;MAElD;MACA,IAAI,CAACA,YAAW,IAAK,OAAOA,YAAW,KAAM,QAAQ,EAAE;QACrDC,OAAO,CAACG,KAAK,CAAC,uCAAuC,EAAEJ,YAAY,CAAC;QACpE;MACF;;MAEA;MACA,IAAIA,YAAY,CAACK,IAAG,IAAKL,YAAY,CAACK,IAAI,CAACC,UAAU,EAAE;QACrD;QACA,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,8BAA8BR,YAAY,CAACK,IAAI,CAACC,UAAU,EAAE,CAAC;MACjF;IACF,CAAC;IAEDG,uBAAuBA,CAACL,KAAK,EAAE;MAC7BH,OAAO,CAACG,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,IAAI,CAACb,KAAK,CAAC,OAAO,EAAEa,KAAK,CAAC;IAC5B;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}