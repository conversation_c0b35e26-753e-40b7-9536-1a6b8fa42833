const { validationResult } = require('express-validator');
const notificationService = require('../services/notificationService');
const { successResponse, errorResponse } = require('../utils/response');
const logger = require('../utils/logger');

class NotificationController {
  /**
   * Establish SSE connection for real-time notifications
   */
  async connect(req, res) {
    try {
      const userId = req.user.id;
      const userType = req.user.type || (req.user.role ? 'admin' : 'client');

      // Set SSE headers
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      });

      // Send initial connection confirmation
      const welcomeMessage = {
        type: 'connection',
        message: 'Connected to notification stream',
        timestamp: new Date().toISOString()
      };
      res.write(`data: ${JSON.stringify(welcomeMessage)}\n\n`);

      // Add connection to notification service
      const { connectionId, state } = notificationService.addConnection(userId, userType, res);

      // Send heartbeat every 30 seconds to keep connection alive
      const heartbeat = setInterval(() => {
        try {
          res.write(`data: ${JSON.stringify({ type: 'heartbeat', timestamp: new Date().toISOString() })}\n\n`);
        } catch (error) {
          clearInterval(heartbeat);
        }
      }, 30000);

      // Single cleanup function to prevent duplicate removals
      const cleanup = () => {
        clearInterval(heartbeat);
        const removed = notificationService.removeConnection(userId, userType, connectionId, state);
        if (removed) {
          logger.info(`SSE connection closed for ${userType} user ${userId}`);
        }
      };

      // Clean up on connection close or abort (only one will trigger due to state tracking)
      req.on('close', cleanup);
      req.on('aborted', cleanup);

    } catch (error) {
      logger.error('SSE connection error:', error);
      res.status(500).json({ error: 'Failed to establish SSE connection' });
    }
  }

  /**
   * Get user notifications with pagination
   */
  async getNotifications(req, res) {
    try {
      const userId = req.user.id;
      const userType = req.user.type || (req.user.role ? 'admin' : 'client');
      const { page = 1, limit = 20, unread_only = false } = req.query;

      const result = await notificationService.getUserNotifications(
        userId,
        userType,
        parseInt(page),
        parseInt(limit),
        unread_only === 'true'
      );

      return successResponse(res, 'Notifications retrieved successfully', result);
    } catch (error) {
      logger.error('Get notifications error:', error);
      return errorResponse(res, 'Failed to retrieve notifications', 500);
    }
  }

  /**
   * Get unread notification count
   */
  async getUnreadCount(req, res) {
    try {
      const userId = req.user.id;
      const userType = req.user.type || (req.user.role ? 'admin' : 'client');

      const count = await notificationService.getUnreadCount(userId, userType);

      return successResponse(res, 'Unread count retrieved successfully', { count });
    } catch (error) {
      logger.error('Get unread count error:', error);
      return errorResponse(res, 'Failed to retrieve unread count', 500);
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return errorResponse(res, 'Validation failed', 400, errors.array());
      }

      const { id } = req.params;
      const userId = req.user.id;
      const userType = req.user.type || (req.user.role ? 'admin' : 'client');

      await notificationService.markAsRead(parseInt(id), userId, userType);

      return successResponse(res, 'Notification marked as read');
    } catch (error) {
      logger.error('Mark as read error:', error);
      return errorResponse(res, 'Failed to mark notification as read', 500);
    }
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead(req, res) {
    try {
      const userId = req.user.id;
      const userType = req.user.type || (req.user.role ? 'admin' : 'client');

      const count = await notificationService.markAllAsRead(userId, userType);

      return successResponse(res, 'All notifications marked as read', { count });
    } catch (error) {
      logger.error('Mark all as read error:', error);
      return errorResponse(res, 'Failed to mark all notifications as read', 500);
    }
  }

  /**
   * Send test notification (admin only)
   */
  async sendTestNotification(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return errorResponse(res, 'Validation failed', 400, errors.array());
      }

      const { user_id, user_type, title, message, type = 'test', priority = 'normal' } = req.body;

      const notification = await notificationService.createNotification({
        user_id: user_id || null,
        user_type,
        type,
        title,
        message,
        priority
      });

      // Send real-time notification
      if (user_id) {
        notificationService.sendToUser(user_id, notification);
      } else if (user_type === 'admin') {
        notificationService.sendToAdmins(notification);
      } else {
        notificationService.broadcast(notification);
      }

      return successResponse(res, 'Test notification sent successfully', notification);
    } catch (error) {
      logger.error('Send test notification error:', error);
      return errorResponse(res, 'Failed to send test notification', 500);
    }
  }

  /**
   * Get notification statistics (admin only)
   */
  async getStatistics(req, res) {
    try {
      const { executeQuery } = require('../utils/database');
      
      // Get notification statistics
      const statsQuery = `
        SELECT 
          user_type,
          type,
          priority,
          is_read,
          COUNT(*) as count
        FROM notifications
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY user_type, type, priority, is_read
        ORDER BY user_type, type, priority
      `;

      const stats = await executeQuery(statsQuery);

      // Get recent activity
      const recentQuery = `
        SELECT 
          type,
          title,
          user_type,
          priority,
          created_at,
          COUNT(*) as count
        FROM notifications
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY type, title, user_type, priority, DATE(created_at)
        ORDER BY created_at DESC
        LIMIT 50
      `;

      const recentActivity = await executeQuery(recentQuery);

      return successResponse(res, 'Notification statistics retrieved successfully', {
        statistics: stats,
        recent_activity: recentActivity
      });
    } catch (error) {
      logger.error('Get notification statistics error:', error);
      return errorResponse(res, 'Failed to retrieve notification statistics', 500);
    }
  }

  /**
   * Delete old notifications (admin only)
   */
  async cleanupOldNotifications(req, res) {
    try {
      const { days = 90 } = req.query;
      const { executeQuery } = require('../utils/database');

      const deleteQuery = `
        DELETE FROM notifications
        WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        AND is_read = TRUE
      `;

      const result = await executeQuery(deleteQuery, [parseInt(days)]);

      return successResponse(res, 'Old notifications cleaned up successfully', {
        deleted_count: result.affectedRows
      });
    } catch (error) {
      logger.error('Cleanup old notifications error:', error);
      return errorResponse(res, 'Failed to cleanup old notifications', 500);
    }
  }
}

module.exports = new NotificationController();
