<template>
  <div class="admin-users">
    <AdminHeader
      :userName="adminData?.first_name || 'Admin'"
      :showUserDropdown="showUserDropdown"
      :sidebarCollapsed="sidebarCollapsed"
      :activeMenu="activeMenu"
      @sidebar-toggle="handleSidebarToggle"
      @user-dropdown-toggle="handleUserDropdownToggle"
      @menu-action="handleMenuAction"
      @logout="handleLogout"
    />

    <!-- Mobile Overlay -->
    <div
      class="mobile-overlay"
      :class="{ active: !sidebarCollapsed && isMobile }"
      @click="closeMobileSidebar"
    ></div>

    <div class="dashboard-container">
      <AdminSidebar
        :collapsed="sidebarCollapsed"
        :activeMenu="activeMenu"
        @menu-change="handleMenuChange"
        @logout="handleLogout"
        @toggle-sidebar="handleSidebarToggle"
      />

      <main class="main-content" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
        <div class="container-fluid p-4">
          <!-- Page Header -->
          <div class="row mb-4">
            <div class="col-12">
              <div class="d-flex justify-content-between align-items-center flex-wrap">

                <div class="d-flex gap-2">
                  <button class="btn btn-outline-success btn-sm" @click="loadUsers" :disabled="loading">
                    <i class="fas fa-sync-alt me-1" :class="{ 'fa-spin': loading }"></i>
                    Refresh
                  </button>
                  <button class="btn btn-success btn-sm" @click="showAddUserModal">
                    <i class="fas fa-user-plus me-1"></i>
                    Add User
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- User Statistics -->
          <div class="row mb-4">
            <div class="col-md-3 mb-3">
              <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                  <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                      <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                        Total Users
                      </div>
                      <div class="h5 mb-0 font-weight-bold text-gray-800">{{ userStats.total || 0 }}</div>
                    </div>
                    <div class="col-auto">
                      <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                  <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                      <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                        Active Users
                      </div>
                      <div class="h5 mb-0 font-weight-bold text-gray-800">{{ userStats.active || 0 }}</div>
                    </div>
                    <div class="col-auto">
                      <i class="fas fa-user-check fa-2x text-gray-300"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                  <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                      <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                        Pending Verification
                      </div>
                      <div class="h5 mb-0 font-weight-bold text-gray-800">{{ userStats.pending || 0 }}</div>
                    </div>
                    <div class="col-auto">
                      <i class="fas fa-user-clock fa-2x text-gray-300"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 mb-3">
              <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                  <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                      <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                        Admin Users
                      </div>
                      <div class="h5 mb-0 font-weight-bold text-gray-800">{{ userStats.admins || 0 }}</div>
                    </div>
                    <div class="col-auto">
                      <i class="fas fa-user-shield fa-2x text-gray-300"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Users Table -->
          <div class="row">
            <div class="col-12">
              <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                  <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-users me-2"></i>
                    User List
                  </h6>
                  <div class="d-flex gap-2">
                    <select class="form-select form-select-sm" v-model="filterStatus" @change="filterUsers">
                      <option value="">All Status</option>
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="pending">Pending</option>
                      <option value="suspended">Suspended</option>
                    </select>
                    <select class="form-select form-select-sm" v-model="filterType" @change="filterUsers">
                      <option value="">All Types</option>
                      <option value="client">Clients</option>
                      <option value="admin">Admins</option>
                    </select>
                  </div>
                </div>
                <div class="card-body">
                  <!-- Search Bar -->
                  <div class="row mb-3">
                    <div class="col-md-6">
                      <div class="input-group">
                        <span class="input-group-text">
                          <i class="fas fa-search"></i>
                        </span>
                        <input
                          type="text"
                          class="form-control"
                          placeholder="Search users by name, email, or username..."
                          v-model="searchQuery"
                          @input="searchUsers"
                        >
                      </div>
                    </div>
                    <div class="col-md-6 text-end">
                      <span class="text-muted">
                        Showing {{ filteredUsers.length }} of {{ users.length }} users
                      </span>
                    </div>
                  </div>

                  <!-- Loading State -->
                  <div v-if="loading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="text-muted mt-2">Loading users...</p>
                  </div>

                  <!-- Empty State -->
                  <div v-else-if="filteredUsers.length === 0" class="text-center py-5">
                    <i class="fas fa-users fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-600">No users found</h5>
                    <p class="text-muted">
                      {{ searchQuery ? 'Try adjusting your search criteria.' : 'No users have been registered yet.' }}
                    </p>
                  </div>

                  <!-- Users Table -->
                  <div v-else class="table-responsive">
                    <table class="table table-hover">
                      <thead class="table-light">
                        <tr>
                          <th>User</th>
                          <th>Email</th>
                          <th>Type</th>
                          <th>Status</th>
                          <th>Registered</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="user in paginatedUsers" :key="user.id">
                          <td>
                            <div class="d-flex align-items-center">
                              <div class="user-avatar me-3">
                                <img v-if="user.profile_picture" :src="user.profile_picture" :alt="user.full_name" class="rounded-circle">
                                <div v-else class="avatar-placeholder rounded-circle">
                                  {{ getInitials(user.full_name) }}
                                </div>
                              </div>
                              <div>
                                <div class="fw-bold">{{ user.full_name }}</div>
                                <div class="text-muted small">@{{ user.username }}</div>
                              </div>
                            </div>
                          </td>
                          <td>{{ user.email }}</td>
                          <td>
                            <span class="badge" :class="user.type === 'admin' ? 'bg-primary' : 'bg-info'">
                              {{ user.type === 'admin' ? 'Admin' : 'Client' }}
                            </span>
                          </td>
                          <td>
                            <span class="badge" :class="getStatusBadgeClass(user.status)">
                              {{ formatStatus(user.status) }}
                            </span>
                          </td>
                          <td>{{ formatDate(user.created_at) }}</td>
                          <td>
                            <div class="btn-group btn-group-sm">
                              <button class="btn btn-outline-primary" @click="viewUser(user)" title="View Details">
                                <i class="fas fa-eye"></i>
                              </button>
                              <button class="btn btn-outline-warning" @click="editUser(user)" title="Edit User">
                                <i class="fas fa-edit"></i>
                              </button>
                              <button
                                class="btn"
                                :class="user.status === 'active' ? 'btn-outline-warning' : 'btn-outline-success'"
                                @click="toggleUserStatus(user)"
                                :title="user.status === 'active' ? 'Suspend User' : 'Activate User'"
                              >
                                <i :class="user.status === 'active' ? 'fas fa-pause' : 'fas fa-play'"></i>
                              </button>
                              <button class="btn btn-outline-danger" @click="deleteUser(user)" title="Delete User">
                                <i class="fas fa-trash"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  <!-- Pagination -->
                  <div v-if="totalPages > 1" class="d-flex justify-content-between align-items-center mt-3">
                    <div class="text-muted">
                      Page {{ currentPage }} of {{ totalPages }}
                    </div>
                    <nav>
                      <ul class="pagination pagination-sm mb-0">
                        <li class="page-item" :class="{ disabled: currentPage === 1 }">
                          <button class="page-link" @click="changePage(currentPage - 1)" :disabled="currentPage === 1">
                            Previous
                          </button>
                        </li>
                        <li
                          v-for="page in visiblePages"
                          :key="page"
                          class="page-item"
                          :class="{ active: page === currentPage }"
                        >
                          <button class="page-link" @click="changePage(page)">{{ page }}</button>
                        </li>
                        <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                          <button class="page-link" @click="changePage(currentPage + 1)" :disabled="currentPage === totalPages">
                            Next
                          </button>
                        </li>
                      </ul>
                    </nav>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="addUserModalLabel">
              <i class="fas fa-user-plus me-2"></i>
              Add New User
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="submitAddUser">
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label for="addUsername" class="form-label">Username *</label>
                  <input
                    type="text"
                    class="form-control"
                    id="addUsername"
                    v-model="addUserForm.username"
                    required
                  >
                </div>
                <div class="col-md-6 mb-3">
                  <label for="addEmail" class="form-label">Email *</label>
                  <input
                    type="email"
                    class="form-control"
                    id="addEmail"
                    v-model="addUserForm.email"
                    required
                  >
                </div>
              </div>
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label for="addFirstName" class="form-label">First Name *</label>
                  <input
                    type="text"
                    class="form-control"
                    id="addFirstName"
                    v-model="addUserForm.first_name"
                    required
                  >
                </div>
                <div class="col-md-6 mb-3">
                  <label for="addLastName" class="form-label">Last Name *</label>
                  <input
                    type="text"
                    class="form-control"
                    id="addLastName"
                    v-model="addUserForm.last_name"
                    required
                  >
                </div>
              </div>
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label for="addRole" class="form-label">Role *</label>
                  <select class="form-select" id="addRole" v-model="addUserForm.role" required>
                    <option value="">Select Role</option>
                    <option value="admin">Administrator</option>
                    <option value="client">Client</option>
                  </select>
                </div>
                <div class="col-md-6 mb-3">
                  <label for="addPassword" class="form-label">Password *</label>
                  <input
                    type="password"
                    class="form-control"
                    id="addPassword"
                    v-model="addUserForm.password"
                    required
                    minlength="6"
                  >
                </div>
              </div>
              <div class="mb-3">
                <label for="addPhone" class="form-label">Phone Number</label>
                <input
                  type="tel"
                  class="form-control"
                  id="addPhone"
                  v-model="addUserForm.phone_number"
                >
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-primary" @click="submitAddUser" :disabled="addUserLoading">
              <span v-if="addUserLoading" class="spinner-border spinner-border-sm me-2" role="status"></span>
              <i v-else class="fas fa-plus me-2"></i>
              {{ addUserLoading ? 'Creating...' : 'Create User' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Edit User Modal -->
    <div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="editUserModalLabel">
              <i class="fas fa-user-edit me-2"></i>
              Edit User
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="submitEditUser" v-if="editUserForm.id">
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label for="editUsername" class="form-label">Username *</label>
                  <input
                    type="text"
                    class="form-control"
                    id="editUsername"
                    v-model="editUserForm.username"
                    required
                  >
                </div>
                <div class="col-md-6 mb-3">
                  <label for="editEmail" class="form-label">Email *</label>
                  <input
                    type="email"
                    class="form-control"
                    id="editEmail"
                    v-model="editUserForm.email"
                    required
                  >
                </div>
              </div>
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label for="editFirstName" class="form-label">First Name *</label>
                  <input
                    type="text"
                    class="form-control"
                    id="editFirstName"
                    v-model="editUserForm.first_name"
                    required
                  >
                </div>
                <div class="col-md-6 mb-3">
                  <label for="editLastName" class="form-label">Last Name *</label>
                  <input
                    type="text"
                    class="form-control"
                    id="editLastName"
                    v-model="editUserForm.last_name"
                    required
                  >
                </div>
              </div>
              <div class="row">
                <div class="col-md-6 mb-3">
                  <label for="editRole" class="form-label">Role *</label>
                  <select class="form-select" id="editRole" v-model="editUserForm.role" required>
                    <option value="admin">Administrator</option>
                    <option value="client">Client</option>
                  </select>
                </div>
                <div class="col-md-6 mb-3">
                  <label for="editStatus" class="form-label">Status *</label>
                  <select class="form-select" id="editStatus" v-model="editUserForm.status" required>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="suspended">Suspended</option>
                    <option value="pending">Pending</option>
                  </select>
                </div>
              </div>
              <div class="mb-3">
                <label for="editPhone" class="form-label">Phone Number</label>
                <input
                  type="tel"
                  class="form-control"
                  id="editPhone"
                  v-model="editUserForm.phone_number"
                >
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-primary" @click="submitEditUser" :disabled="editUserLoading">
              <span v-if="editUserLoading" class="spinner-border spinner-border-sm me-2" role="status"></span>
              <i v-else class="fas fa-save me-2"></i>
              {{ editUserLoading ? 'Updating...' : 'Update User' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- View User Modal -->
    <div class="modal fade" id="viewUserModal" tabindex="-1" aria-labelledby="viewUserModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="viewUserModalLabel">
              <i class="fas fa-user me-2"></i>
              User Details
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body" v-if="viewUserData">
            <div class="row">
              <div class="col-md-4 text-center mb-4">
                <div class="user-avatar-large mx-auto mb-3">
                  <img v-if="viewUserData.profile_picture" :src="viewUserData.profile_picture" :alt="viewUserData.full_name" class="rounded-circle">
                  <div v-else class="avatar-placeholder-large rounded-circle">
                    {{ getInitials(viewUserData.full_name) }}
                  </div>
                </div>
                <h5>{{ viewUserData.full_name }}</h5>
                <span class="badge" :class="getStatusBadgeClass(viewUserData.status)">
                  {{ formatStatus(viewUserData.status) }}
                </span>
              </div>
              <div class="col-md-8">
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Username:</strong></div>
                  <div class="col-sm-8">{{ viewUserData.username }}</div>
                </div>
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Email:</strong></div>
                  <div class="col-sm-8">{{ viewUserData.email }}</div>
                </div>
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Type:</strong></div>
                  <div class="col-sm-8">
                    <span class="badge" :class="viewUserData.type === 'admin' ? 'bg-primary' : 'bg-info'">
                      {{ viewUserData.type === 'admin' ? 'Administrator' : 'Client' }}
                    </span>
                  </div>
                </div>
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Phone:</strong></div>
                  <div class="col-sm-8">{{ viewUserData.phone_number || 'N/A' }}</div>
                </div>
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Registered:</strong></div>
                  <div class="col-sm-8">{{ formatDate(viewUserData.created_at) }}</div>
                </div>
                <div class="row mb-3">
                  <div class="col-sm-4"><strong>Last Login:</strong></div>
                  <div class="col-sm-8">{{ viewUserData.last_login ? formatDate(viewUserData.last_login) : 'Never' }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            <button type="button" class="btn btn-primary" @click="editUser(viewUserData)" data-bs-dismiss="modal">
              <i class="fas fa-edit me-2"></i>
              Edit User
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import AdminHeader from './AdminHeader.vue';
import AdminSidebar from './AdminSidebar.vue';
import adminAuthService from '@/services/adminAuthService';
import userManagementService from '@/services/userManagementService';

export default {
  name: 'AdminUsers',
  components: {
    AdminHeader,
    AdminSidebar
  },

  data() {
    return {
      // UI State
      sidebarCollapsed: false,
      showUserDropdown: false,
      isMobile: false,
      adminData: null,
      // Component Data
      users: [],
      filteredUsers: [],
      searchQuery: '',
      filterStatus: '',
      filterType: '',
      currentPage: 1,
      itemsPerPage: 10,
      loading: false,
      userStats: {
        total: 0,
        active: 0,
        pending: 0,
        admins: 0
      },

      // Modal data
      viewUserData: null,
      addUserLoading: false,
      editUserLoading: false,

      // Add user form
      addUserForm: {
        username: '',
        email: '',
        first_name: '',
        last_name: '',
        role: '',
        password: '',
        phone_number: ''
      },

      // Edit user form
      editUserForm: {
        id: null,
        username: '',
        email: '',
        first_name: '',
        last_name: '',
        role: '',
        status: '',
        phone_number: ''
      }
    };
  },

  computed: {
    activeMenu() {
      const path = this.$route.path;
      if (path.includes('/admin/users')) return 'users';
      if (path.includes('/admin/requests')) return 'requests';
      if (path.includes('/admin/reports')) return 'reports';
      if (path.includes('/admin/settings')) return 'settings';
      if (path.includes('/admin/activity-logs')) return 'activity';
      if (path.includes('/admin/profile')) return 'profile';
      return 'dashboard';
    },

    paginatedUsers() {
      const start = (this.currentPage - 1) * this.itemsPerPage;
      const end = start + this.itemsPerPage;
      return this.filteredUsers.slice(start, end);
    },

    totalPages() {
      return Math.ceil(this.filteredUsers.length / this.itemsPerPage);
    },

    visiblePages() {
      const pages = [];
      const start = Math.max(1, this.currentPage - 2);
      const end = Math.min(this.totalPages, this.currentPage + 2);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      return pages;
    }
  },

  async mounted() {
    // Check authentication
    if (!adminAuthService.isLoggedIn()) {
      this.$router.push('/admin/login');
      return;
    }

    // Initialize UI state
    this.initializeUI();

    // Load component data
    await this.loadAdminProfile();
    await this.loadUserStats();
    await this.loadUsers();
  },

  beforeUnmount() {
    if (this.handleResize) {
      window.removeEventListener('resize', this.handleResize);
    }
  },

  methods: {
    // Initialize UI state
    initializeUI() {
      this.isMobile = window.innerWidth <= 768;

      // Load saved sidebar state (only on desktop)
      if (!this.isMobile) {
        const saved = localStorage.getItem('adminSidebarCollapsed');
        this.sidebarCollapsed = saved ? JSON.parse(saved) : false;
      } else {
        this.sidebarCollapsed = true; // Always collapsed on mobile
      }

      // Setup resize listener
      this.handleResize = () => {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= 768;

        if (this.isMobile && !wasMobile) {
          this.sidebarCollapsed = true; // Collapse when switching to mobile
        } else if (!this.isMobile && wasMobile) {
          // Restore saved state when switching to desktop
          const saved = localStorage.getItem('adminSidebarCollapsed');
          this.sidebarCollapsed = saved ? JSON.parse(saved) : false;
        }
      };
      window.addEventListener('resize', this.handleResize);
    },

    // Sidebar toggle
    handleSidebarToggle() {
      this.sidebarCollapsed = !this.sidebarCollapsed;
      localStorage.setItem('adminSidebarCollapsed', JSON.stringify(this.sidebarCollapsed));
    },

    // Menu navigation
    handleMenuChange(menu) {
      const routes = {
        'dashboard': '/admin/dashboard',
        'users': '/admin/users',
        'requests': '/admin/requests',
        'reports': '/admin/reports',
        'settings': '/admin/settings',
        'activity': '/admin/activity-logs',
        'profile': '/admin/profile'
      };

      // Close sidebar on mobile after navigation
      if (this.isMobile) {
        this.sidebarCollapsed = true;
      }

      if (routes[menu]) {
        this.$router.push(routes[menu]);
      }
    },

    // User dropdown toggle
    handleUserDropdownToggle() {
      this.showUserDropdown = !this.showUserDropdown;
    },

    // Menu actions
    handleMenuAction(action) {
      if (action === 'profile') {
        this.$router.push('/admin/profile');
      } else if (action === 'settings') {
        this.$router.push('/admin/settings');
      }
      this.showUserDropdown = false;
    },

    // Close mobile sidebar
    closeMobileSidebar() {
      if (this.isMobile) {
        this.sidebarCollapsed = true;
      }
    },

    // Logout
    handleLogout() {
      adminAuthService.logout();
      this.$router.push('/admin/login');
    },

    // Load admin profile data
    async loadAdminProfile() {
      try {
        const response = await adminAuthService.getProfile();
        if (response.success) {
          this.adminData = response.data;
        }
      } catch (error) {
        console.error('Failed to load admin data:', error);
        this.adminData = adminAuthService.getAdminData();
      }
    },

    // Load user statistics
    async loadUserStats() {
      try {
        const response = await userManagementService.getUserStats();

        if (response.success) {
          this.userStats = response.data;
        } else {
          throw new Error(response.message || 'Failed to load user statistics');
        }
      } catch (error) {
        console.error('Failed to load user statistics:', error);
        // Keep default stats on error
        this.userStats = {
          total: 0,
          active: 0,
          pending: 0,
          admins: 0
        };
      }
    },

    // Load users data
    async loadUsers() {
      this.loading = true;
      try {
        const params = {
          page: this.currentPage,
          limit: 50, // Load more for client-side filtering
          search: this.searchQuery || undefined,
          role: this.filterType || undefined,
          is_active: this.filterStatus === 'active' ? true :
                     this.filterStatus === 'inactive' ? false : undefined
        };

        const response = await userManagementService.getUsers(params);

        if (response.success) {
          // Format users for display
          this.users = response.data.users.map(user =>
            userManagementService.formatUserData(user)
          );

          this.filteredUsers = [...this.users];
          this.calculateStats();
        } else {
          throw new Error(response.message || 'Failed to load users');
        }
      } catch (error) {
        console.error('Failed to load users:', error);
        this.$toast?.error?.(error.message || 'Failed to load users');

        // Fallback to empty state
        this.users = [];
        this.filteredUsers = [];
        this.calculateStats();
      } finally {
        this.loading = false;
      }
    },

    // Calculate user statistics
    calculateStats() {
      this.userStats = {
        total: this.users.length,
        active: this.users.filter(u => u.status === 'active').length,
        pending: this.users.filter(u => u.status === 'pending').length,
        admins: this.users.filter(u => u.type === 'admin').length
      };
    },

    // Search users
    searchUsers() {
      this.filterUsers();
    },

    // Filter users based on search and filters
    filterUsers() {
      let filtered = [...this.users];

      // Apply search filter
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        filtered = filtered.filter(user =>
          user.full_name.toLowerCase().includes(query) ||
          user.email.toLowerCase().includes(query) ||
          user.username.toLowerCase().includes(query)
        );
      }

      // Apply status filter
      if (this.filterStatus) {
        filtered = filtered.filter(user => user.status === this.filterStatus);
      }

      // Apply type filter
      if (this.filterType) {
        filtered = filtered.filter(user => user.type === this.filterType);
      }

      this.filteredUsers = filtered;
      this.currentPage = 1; // Reset to first page
    },

    // Change page
    changePage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page;
      }
    },

    // Get user initials for avatar
    getInitials(fullName) {
      if (!fullName) return '?';
      return fullName.split(' ').map(name => name.charAt(0)).join('').toUpperCase().slice(0, 2);
    },

    // Get status badge class
    getStatusBadgeClass(status) {
      const classes = {
        'active': 'bg-success',
        'inactive': 'bg-secondary',
        'pending': 'bg-warning',
        'suspended': 'bg-danger'
      };
      return classes[status] || 'bg-secondary';
    },

    // Format status text
    formatStatus(status) {
      return status.charAt(0).toUpperCase() + status.slice(1);
    },

    // Format date
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    },

    // User actions
    viewUser(user) {
      console.log('Viewing user:', user);
      // Implement user view modal or navigation
    },

    editUser(user) {
      console.log('Editing user:', user);
      // Implement user edit modal or navigation
    },

    async toggleUserStatus(user) {
      try {
        const newStatus = user.status === 'active' ? 'suspended' : 'active';
        const reason = `Status changed by admin: ${this.adminData?.first_name || 'Admin'}`;

        const response = await userManagementService.updateUserStatus(user.id, newStatus, reason);

        if (response.success) {
          // Update local data
          user.status = newStatus;
          this.calculateStats();

          this.$toast?.success?.(`User ${user.full_name} has been ${newStatus === 'active' ? 'activated' : 'suspended'}.`);
        } else {
          throw new Error(response.message || 'Failed to update user status');
        }
      } catch (error) {
        console.error('Failed to update user status:', error);
        this.$toast?.error?.(error.message || 'Failed to update user status. Please try again.');
      }
    },

    async deleteUser(user) {
      if (!confirm(`Are you sure you want to delete user "${user.full_name}"? This action cannot be undone.`)) {
        return;
      }

      try {
        const reason = `User deleted by admin: ${this.adminData?.first_name || 'Admin'}`;
        const response = await userManagementService.deleteUser(user.id, reason);

        if (response.success) {
          // Remove from local data
          const index = this.users.findIndex(u => u.id === user.id);
          if (index > -1) {
            this.users.splice(index, 1);
            this.filterUsers();
            this.calculateStats();
          }

          this.$toast?.success?.(`User ${user.full_name} has been deleted.`);
        } else {
          throw new Error(response.message || 'Failed to delete user');
        }
      } catch (error) {
        console.error('Failed to delete user:', error);
        this.$toast?.error?.(error.message || 'Failed to delete user. Please try again.');
      }
    },



    // Modal methods
    showAddUserModal() {
      this.resetAddUserForm();
      const modal = new bootstrap.Modal(document.getElementById('addUserModal'));
      modal.show();
    },

    resetAddUserForm() {
      this.addUserForm = {
        username: '',
        email: '',
        first_name: '',
        last_name: '',
        role: '',
        password: '',
        phone_number: ''
      };
    },

    async submitAddUser() {
      try {
        this.addUserLoading = true;

        // Validate form
        const validation = userManagementService.validateUserData(this.addUserForm);
        if (!validation.isValid) {
          this.$toast?.error?.(validation.errors.join(', '));
          return;
        }

        const response = await userManagementService.createUser(this.addUserForm);

        if (response.success) {
          this.$toast?.success?.('User created successfully');

          // Close modal
          const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
          modal.hide();

          // Reload data
          await this.loadUsers();
          await this.loadUserStats();
        } else {
          throw new Error(response.message || 'Failed to create user');
        }
      } catch (error) {
        console.error('Failed to create user:', error);
        this.$toast?.error?.(error.message || 'Failed to create user');
      } finally {
        this.addUserLoading = false;
      }
    },

    editUser(user) {
      this.editUserForm = {
        id: user.id,
        username: user.username,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        role: user.type,
        status: user.status,
        phone_number: user.phone_number || ''
      };

      const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
      modal.show();
    },

    async submitEditUser() {
      try {
        this.editUserLoading = true;

        // Validate form
        const validation = userManagementService.validateUserData(this.editUserForm, true);
        if (!validation.isValid) {
          this.$toast?.error?.(validation.errors.join(', '));
          return;
        }

        const response = await userManagementService.updateUser(this.editUserForm.id, this.editUserForm);

        if (response.success) {
          this.$toast?.success?.('User updated successfully');

          // Close modal
          const modal = bootstrap.Modal.getInstance(document.getElementById('editUserModal'));
          modal.hide();

          // Update local data
          const userIndex = this.users.findIndex(u => u.id === this.editUserForm.id);
          if (userIndex > -1) {
            this.users[userIndex] = { ...this.users[userIndex], ...this.editUserForm };
            this.filterUsers();
          }

          // Reload stats
          await this.loadUserStats();
        } else {
          throw new Error(response.message || 'Failed to update user');
        }
      } catch (error) {
        console.error('Failed to update user:', error);
        this.$toast?.error?.(error.message || 'Failed to update user');
      } finally {
        this.editUserLoading = false;
      }
    },

    async viewUser(user) {
      try {
        const response = await userManagementService.getUser(user.id);

        if (response.success) {
          this.viewUserData = response.data;
          const modal = new bootstrap.Modal(document.getElementById('viewUserModal'));
          modal.show();
        } else {
          throw new Error(response.message || 'Failed to load user details');
        }
      } catch (error) {
        console.error('Failed to load user details:', error);
        this.$toast?.error?.(error.message || 'Failed to load user details');
      }
    },

    // Additional user-specific methods can be added here
    // Navigation handlers are now provided by the mixin
  }
};
</script>

<style scoped>
@import './css/adminDashboard.css';

/* User avatar styles */
.user-avatar {
  width: 40px;
  height: 40px;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
  border-radius: 50%;
}

.user-avatar-large {
  width: 80px;
  height: 80px;
}

.avatar-placeholder-large {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 24px;
  border-radius: 50%;
}

/* Modal styles */
.modal-lg {
  max-width: 800px;
}

/* Form styles */
.form-label {
  font-weight: 600;
  color: #495057;
}

.form-control:focus,
.form-select:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Loading states */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Table improvements */
.table th {
  border-top: none;
  font-weight: 600;
  color: #5a5c69;
  font-size: 0.875rem;
}

.table td {
  vertical-align: middle;
  font-size: 0.875rem;
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.05);
}

/* Button group improvements */
.btn-group-sm .btn {
  padding: 0.375rem 0.5rem;
  font-size: 0.75rem;
}

/* Search and filter improvements */
.form-select-sm {
  font-size: 0.875rem;
  padding: 0.375rem 0.75rem;
}

/* Pagination improvements */
.pagination-sm .page-link {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

/* Badge improvements */
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
  border-radius: 0.5rem;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .table-responsive {
    font-size: 0.8rem;
  }

  .btn-group-sm .btn {
    padding: 0.25rem 0.375rem;
    font-size: 0.7rem;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
  }

  .avatar-placeholder {
    width: 32px;
    height: 32px;
    font-size: 0.75rem;
  }
}

@media (max-width: 576px) {
  .d-flex.gap-2 {
    flex-direction: column;
    gap: 0.5rem !important;
  }

  .btn-group {
    flex-direction: column;
  }

  .btn-group .btn {
    border-radius: 0.375rem !important;
    margin-bottom: 0.25rem;
  }
}
</style>
